(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,a,t)=>{"use strict";t.d(a,{A0:()=>l,BF:()=>n,Hj:()=>d,XI:()=>i,nA:()=>c,nd:()=>o});var s=t(60687);t(43210);var r=t(4780);function i({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",e),...a})})}function l({className:e,...a}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...a})}function n({className:e,...a}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...a})}function d({className:e,...a}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...a})}function o({className:e,...a}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}function c({className:e,...a}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...a})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,a,t)=>{"use strict";t.d(a,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>o,yv:()=>c});var s=t(60687);t(43210);var r=t(97822),i=t(78272),l=t(13964),n=t(3589),d=t(4780);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:a="default",children:t,...l}){return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...l,children:[t,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:a,position:t="popper",...i}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...i,children:[(0,s.jsx)(p,{}),(0,s.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(h,{})]})})}function m({className:e,children:a,...t}){return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...t,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function p({className:e,...a}){return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}function h({className:e,...a}){return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23652:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>X});var s=t(60687),r=t(43210),i=t(44493),l=t(55527),n=t(63143),d=t(48730),o=t(5336),c=t(88233);function u({user:e,onEdit:a,onToggleStatus:t,onDelete:r}){return(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),console.log("Edit clicked for user:",e.username),a()},type:"button",className:"p-1.5 rounded hover:bg-blue-50 transition-colors",title:"Modifica utente",children:(0,s.jsx)(n.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,s.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),console.log("Toggle status clicked for user:",e.username),t()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded transition-colors ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-slate-50"}`,title:e.abilitato?"Disabilita utente":"Abilita utente",children:e.abilitato?(0,s.jsx)(d.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,s.jsx)(o.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),(0,s.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),console.log("Delete clicked for user:",e.username),r()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded transition-colors ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-red-50"}`,title:"Elimina utente",children:(0,s.jsx)(c.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})})]})}var x=t(96834),m=t(6211),p=t(85763),h=t(63213),g=t(16189),j=t(62185),v=t(29523),b=t(89667),f=t(80013),N=t(15079),y=t(56896),w=t(81806),z=t(12597),A=t(13861),E=t(11860),k=t(8819);function C({user:e,onSave:a,onCancel:t}){let[n,d]=(0,r.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[o,c]=(0,r.useState)({}),[u,x]=(0,r.useState)(!1),[m,p]=(0,r.useState)(""),[h,g]=(0,r.useState)(!1),C=(e,a)=>{d(t=>({...t,[e]:a})),o[e]&&c(a=>({...a,[e]:""}))},S=()=>{let a=(0,w.GN)({username:n.username,password:e?void 0:n.password,ragione_sociale:n.ragione_sociale,email:n.email,vat:n.vat,indirizzo:n.indirizzo,nazione:n.nazione,referente_aziendale:n.referente_aziendale});return c(a.errors),a.isValid},_=async t=>{t.preventDefault();let s=`user-form-${e?.id_utente||"new"}-${Date.now()}`;if(!(0,w.Eb)(s,5,6e4))return void p("Troppi tentativi. Riprova tra un minuto.");if(S()){x(!0),p("");try{let t,s={...n};e||(s.ruolo="user"),e&&!s.password.trim()&&delete s.password,s.data_scadenza&&(s.data_scadenza=s.data_scadenza),t=e?await j.dG.updateUser(e.id_utente,s):await j.dG.createUser(s),a(t)}catch(e){p(e.response?.data?.detail||e.message||"Errore durante il salvataggio dell'utente")}finally{x(!1)}}};return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:e?`Modifica Utente: ${e.username}`:"Crea Nuovo Utente Standard"})}),(0,s.jsxs)(i.Wu,{children:[m&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-600",children:m})}),(0,s.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"username",children:"Username *"}),(0,s.jsx)(b.p,{id:"username",value:n.username,onChange:e=>C("username",e.target.value),disabled:u,className:o.username?"border-red-500":""}),o.username&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.username})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"password",children:e?"Nuova Password (lascia vuoto per non modificare)":"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.p,{id:"password",type:h?"text":"password",value:n.password,onChange:e=>C("password",e.target.value),disabled:u,className:o.password?"border-red-500 pr-10":"pr-10"}),(0,s.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>g(!h),disabled:u,children:h?(0,s.jsx)(z.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(A.A,{className:"h-4 w-4 text-gray-400"})})]}),o.password&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.password})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"ragione_sociale",children:"Ragione Sociale *"}),(0,s.jsx)(b.p,{id:"ragione_sociale",value:n.ragione_sociale,onChange:e=>C("ragione_sociale",e.target.value),disabled:u,className:o.ragione_sociale?"border-red-500":""}),o.ragione_sociale&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.ragione_sociale})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(b.p,{id:"email",type:"email",value:n.email,onChange:e=>C("email",e.target.value),disabled:u,className:o.email?"border-red-500":""}),o.email&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"indirizzo",children:"Indirizzo"}),(0,s.jsx)(b.p,{id:"indirizzo",value:n.indirizzo,onChange:e=>C("indirizzo",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"nazione",children:"Nazione"}),(0,s.jsx)(b.p,{id:"nazione",value:n.nazione,onChange:e=>C("nazione",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"vat",children:"VAT"}),(0,s.jsx)(b.p,{id:"vat",value:n.vat,onChange:e=>C("vat",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,s.jsx)(b.p,{id:"referente_aziendale",value:n.referente_aziendale,onChange:e=>C("referente_aziendale",e.target.value),disabled:u})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,s.jsx)(b.p,{id:"data_scadenza",type:"date",value:n.data_scadenza,onChange:e=>C("data_scadenza",e.target.value),disabled:u})]}),e?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsxs)(N.l6,{value:n.ruolo,onValueChange:e=>C("ruolo",e),disabled:u,children:[(0,s.jsx)(N.bq,{children:(0,s.jsx)(N.yv,{})}),(0,s.jsxs)(N.gC,{children:[(0,s.jsx)(N.eb,{value:"user",children:"User"}),(0,s.jsx)(N.eb,{value:"cantieri_user",children:"Cantieri User"})]})]})]}):(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsx)("div",{className:"px-3 py-2 bg-slate-50 border border-slate-200 rounded-md text-sm text-slate-600",children:"User (Standard)"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(y.S,{id:"abilitato",checked:n.abilitato,onCheckedChange:e=>C("abilitato",e),disabled:u||e&&"owner"===e.ruolo}),(0,s.jsx)(f.J,{htmlFor:"abilitato",children:"Utente abilitato"})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,s.jsx)(l.tA,{type:"button",onClick:t,disabled:u,icon:(0,s.jsx)(E.A,{className:"h-4 w-4"}),children:"Annulla"}),(0,s.jsx)(l.jn,{type:"submit",loading:u,icon:(0,s.jsx)(k.A,{className:"h-4 w-4"}),glow:!0,children:u?"Salvataggio...":"Salva"})]})]})]})]})}var S=t(61611),_=t(78122),T=t(41862);function R(){let[e,a]=(0,r.useState)(null),[t,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),c=async()=>{n(!0),o("");try{console.log("Caricamento dati database raw...");let e=await j.dG.getDatabaseData();console.log("Dati ricevuti:",e),a(e)}catch(e){console.error("Errore durante il caricamento:",e),o(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati del database")}finally{n(!1)}},u=(e,a,t)=>{if(!a||0===a.length)return(0,s.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",t]});let r=Object.keys(a[0]);return(0,s.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,s.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900",children:t}),(0,s.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,s.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,s.jsxs)(m.XI,{children:[(0,s.jsx)(m.A0,{className:"sticky top-0 bg-slate-50",children:(0,s.jsx)(m.Hj,{children:r.map(e=>(0,s.jsx)(m.nd,{className:"font-medium",children:e},e))})}),(0,s.jsx)(m.BF,{children:a.map((e,a)=>(0,s.jsx)(m.Hj,{children:r.map(a=>(0,s.jsx)(m.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,s.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},x=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(S.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,s.jsx)(l.jn,{size:"sm",onClick:c,loading:t,icon:(0,s.jsx)(_.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(A.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),t?(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(T.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,s.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):d?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,s.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,s.jsx)("p",{className:"text-red-600",children:d})]}):e?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),x.map(a=>e[a.key]&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),u(a.key,e[a.key],a.title)]},a.key)),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:x.map(a=>(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,s.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,s.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var D=t(62688);let V=(0,D.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var P=t(43649);function I(){let[e,a]=(0,r.useState)(""),[t,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(!1),[u,x]=(0,r.useState)(""),[m,p]=(0,r.useState)(""),h=async()=>{if("RESET DATABASE"!==e||!t)return void x("Conferma richiesta per procedere con il reset");o(!0),x(""),p("");try{await j.dG.resetDatabase(),p("Database resettato con successo! Tutti i dati sono stati eliminati."),a(""),n(!1)}catch(e){x(e.response?.data?.detail||e.message||"Errore durante il reset del database")}finally{o(!1)}},g="RESET DATABASE"===e&&t&&!d;return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(V,{className:"h-5 w-5"}),"Reset Database"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(P.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,s.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,s.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,s.jsx)("li",{children:"Tutti i cavi installati"}),(0,s.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,s.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,s.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,s.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),u&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:u})}),m&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-green-600",children:m})}),(0,s.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(f.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,s.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,s.jsx)(b.p,{id:"confirm-text",value:e,onChange:e=>a(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:d,className:"RESET DATABASE"===e?"border-green-500":""})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(y.S,{id:"confirm-checkbox",checked:t,onCheckedChange:n,disabled:d}),(0,s.jsx)(f.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Stato Conferma:"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${"RESET DATABASE"===e?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===e?"✓ Corretto":"✗ Richiesto"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:`w-3 h-3 rounded-full ${t?"bg-green-500":"bg-red-500"}`}),(0,s.jsxs)("span",{children:["Checkbox confermata: ",t?"✓ S\xec":"✗ Richiesta"]})]})]})]}),(0,s.jsx)(l.Qi,{onClick:h,disabled:!g,className:"w-full",size:"lg",loading:d,icon:(0,s.jsx)(c.A,{className:"h-5 w-5"}),glow:!0,children:d?"Reset in corso...":"RESET DATABASE - ELIMINA TUTTI I DATI"}),!g&&(0,s.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per abilitare il reset"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,s.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,s.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,s.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,s.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,s.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var U=t(23361);let M=(0,D.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),q=(0,D.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var L=t(10022),Z=t(96474);function G(){let[e,a]=(0,r.useState)("categorie");return(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(U.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,s.jsxs)(i.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(U.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,s.jsxs)(p.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,s.jsxs)(p.j7,{className:"grid w-full grid-cols-4",children:[(0,s.jsxs)(p.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,s.jsx)(M,{className:"h-4 w-4"}),"Categorie"]}),(0,s.jsxs)(p.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,s.jsx)(q,{className:"h-4 w-4"}),"Produttori"]}),(0,s.jsxs)(p.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),"Standard"]}),(0,s.jsxs)(p.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,s.jsx)(U.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,s.jsxs)(p.av,{value:"categorie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(M,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,s.jsxs)(p.av,{value:"produttori",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(q,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,s.jsxs)(p.av,{value:"standard",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(L.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,s.jsxs)(p.av,{value:"tipologie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,s.jsxs)(v.$,{children:[(0,s.jsx)(Z.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(U.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var B=t(41312);let F=(0,D.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),$=(0,D.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function X(){let e=(0,g.useRouter)(),[a,t]=(0,r.useState)("visualizza-utenti"),[d,o]=(0,r.useState)(""),[c,v]=(0,r.useState)([]),[b,f]=(0,r.useState)([]),[N,y]=(0,r.useState)(!0),[w,z]=(0,r.useState)(""),[A,E]=(0,r.useState)(null),[k,_]=(0,r.useState)({open:!1,message:"",severity:"success"}),{user:D,impersonateUser:P}=(0,h.A)(),M=async()=>{try{if(y(!0),z(""),console.log("Caricamento dati per tab:",a),console.log("Token presente:","N/A"),console.log("Utente corrente:",D),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){console.log("Chiamata API per ottenere utenti...");let e=await j.dG.getUsers();console.log("Utenti ricevuti:",e),v(e)}else if("cantieri"===a){let e=await j._I.getCantieri();f(e)}}catch(e){console.error("Errore caricamento dati:",e),console.error("Dettagli errore:",e.response),z(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati")}finally{y(!1)}},q=e=>{console.log("Modifica utente:",e.username),E(e),t("modifica-utente")},L=async e=>{console.log("Toggle status utente:",e);try{await j.dG.toggleUserStatus(e),M()}catch(e){console.error("Errore toggle status:",e),z(e.response?.data?.detail||"Errore durante la modifica dello stato utente")}},Z=async e=>{if(console.log("Elimina utente:",e),confirm("Sei sicuro di voler eliminare questo utente?"))try{await j.dG.deleteUser(e),M()}catch(e){console.error("Errore eliminazione utente:",e),z(e.response?.data?.detail||"Errore durante l'eliminazione dell'utente")}},X=e=>{console.log("Utente salvato:",e),E(null),t("visualizza-utenti"),M()},J=()=>{E(null),t("visualizza-utenti")},O=async a=>{try{console.log("Impersonificazione rapida utente:",a.username,"Ruolo:",a.ruolo),await P(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){console.error("Errore durante l'impersonificazione rapida:",e),z(e.response?.data?.detail||e.message||"Errore durante l'impersonificazione")}},H=e=>{switch(e){case"owner":return(0,s.jsx)(x.E,{className:"bg-purple-100 text-purple-800",children:"Owner"});case"user":return(0,s.jsx)(x.E,{className:"bg-blue-100 text-blue-800",children:"User"});case"cantieri_user":return(0,s.jsx)(x.E,{className:"bg-green-100 text-green-800",children:"Cantieri User"});default:return(0,s.jsx)(x.E,{variant:"secondary",children:e})}},Q=(e,a)=>{if(!e)return(0,s.jsx)(x.E,{className:"bg-red-100 text-red-800",children:"Disabilitato"});if(a){let e=new Date(a),t=new Date;if(e<t)return(0,s.jsx)(x.E,{className:"bg-red-100 text-red-800",children:"Scaduto"});if(e.getTime()-t.getTime()<6048e5)return(0,s.jsx)(x.E,{className:"bg-yellow-100 text-yellow-800",children:"In Scadenza"})}return(0,s.jsx)(x.E,{className:"bg-green-100 text-green-800",children:"Attivo"})};return(c.filter(e=>e.username?.toLowerCase().includes(d.toLowerCase())||e.ragione_sociale?.toLowerCase().includes(d.toLowerCase())||e.email?.toLowerCase().includes(d.toLowerCase())),D&&"owner"===D.ruolo)?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,s.jsxs)(p.tU,{value:a,onValueChange:t,className:"w-full",children:[(0,s.jsxs)(p.j7,{className:`grid w-full ${A?"grid-cols-6":"grid-cols-5"}`,children:[(0,s.jsxs)(p.Xi,{value:"visualizza-utenti",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(B.A,{className:"h-4 w-4"}),"Visualizza Utenti"]}),(0,s.jsxs)(p.Xi,{value:"crea-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(F,{className:"h-4 w-4"}),"Crea Nuovo Utente"]}),A&&(0,s.jsxs)(p.Xi,{value:"modifica-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),"Modifica Utente"]}),(0,s.jsxs)(p.Xi,{value:"database-tipologie-cavi",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(U.A,{className:"h-4 w-4"}),"Database Tipologie Cavi"]}),(0,s.jsxs)(p.Xi,{value:"visualizza-database-raw",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(S.A,{className:"h-4 w-4"}),"Visualizza Database Raw"]}),(0,s.jsxs)(p.Xi,{value:"reset-database",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(V,{className:"h-4 w-4"}),"Reset Database"]})]}),(0,s.jsxs)(p.av,{value:"visualizza-utenti",className:"space-y-4",children:[w&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:w})}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Lista Utenti"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(m.XI,{className:"min-w-full",children:[(0,s.jsx)(m.A0,{children:(0,s.jsxs)(m.Hj,{children:[(0,s.jsx)(m.nd,{className:"w-[60px] text-center",children:"ID"}),(0,s.jsx)(m.nd,{className:"w-[120px]",children:"Username"}),(0,s.jsx)(m.nd,{className:"w-[100px] text-center",children:"Password"}),(0,s.jsx)(m.nd,{className:"w-[100px] text-center",children:"Ruolo"}),(0,s.jsx)(m.nd,{className:"w-[250px]",children:"Ragione Sociale"}),(0,s.jsx)(m.nd,{className:"w-[200px]",children:"Email"}),(0,s.jsx)(m.nd,{className:"w-[120px] text-center",children:"VAT"}),(0,s.jsx)(m.nd,{className:"w-[100px] text-center",children:"Nazione"}),(0,s.jsx)(m.nd,{className:"w-[150px]",children:"Referente"}),(0,s.jsx)(m.nd,{className:"w-[100px] text-center",children:"Scadenza"}),(0,s.jsx)(m.nd,{className:"w-[100px] text-center",children:"Stato"}),(0,s.jsx)(m.nd,{className:"w-[120px] text-center",children:"Azioni"})]})}),(0,s.jsx)(m.BF,{children:N?(0,s.jsx)(m.Hj,{children:(0,s.jsx)(m.nA,{colSpan:12,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(T.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===c.length?(0,s.jsx)(m.Hj,{children:(0,s.jsx)(m.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):c.map(e=>(0,s.jsxs)(m.Hj,{className:"hover:bg-slate-50",children:[(0,s.jsx)(m.nA,{className:"text-center text-slate-500 text-sm font-mono",children:e.id_utente}),(0,s.jsx)(m.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,s.jsx)(m.nA,{className:"text-center font-mono text-xs text-slate-500",children:e.password_plain||"***"}),(0,s.jsx)(m.nA,{className:"text-center",children:H(e.ruolo)}),(0,s.jsx)(m.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,s.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,s.jsx)(m.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,s.jsx)(m.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,s.jsx)(m.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,s.jsx)(m.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,s.jsx)(m.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,s.jsx)(m.nA,{className:"text-center",children:Q(e.abilitato,e.data_scadenza)}),(0,s.jsx)(m.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(u,{user:e,onEdit:()=>q(e),onToggleStatus:()=>L(e.id_utente),onDelete:()=>Z(e.id_utente)}),(0,s.jsx)(l.jn,{size:"sm",onClick:()=>O(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,s.jsx)($,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,s.jsx)(p.av,{value:"crea-utente",className:"space-y-4",children:(0,s.jsx)(C,{user:null,onSave:X,onCancel:J})}),A&&(0,s.jsx)(p.av,{value:"modifica-utente",className:"space-y-4",children:(0,s.jsx)(C,{user:A,onSave:X,onCancel:J})}),(0,s.jsx)(p.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,s.jsx)(G,{})}),(0,s.jsx)(p.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,s.jsx)(R,{})}),(0,s.jsx)(p.av,{value:"reset-database",className:"space-y-4",children:(0,s.jsx)(I,{})})]})})}):null}},24883:(e,a,t)=>{Promise.resolve().then(t.bind(t,23652))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,a,t)=>{"use strict";t.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>l});var s=t(60687);t(43210);var r=t(4780);function i({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a})}function l({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function n({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function d({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function o({className:e,...a}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,t)=>{"use strict";t.d(a,{S:()=>n});var s=t(60687);t(43210);var r=t(40211),i=t(13964),l=t(4780);function n({className:e,...a}){return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5"})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});var s=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,a,t)=>{"use strict";t.d(a,{J:()=>l});var s=t(60687);t(43210);var r=t(78148),i=t(4780);function l({className:e,...a}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},81630:e=>{"use strict";e.exports=require("http")},81806:(e,a,t)=>{"use strict";t.d(a,{Eb:()=>p,GN:()=>h,TU:()=>n});let s=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,r=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(s,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},o=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=(e,a=255)=>l(e).length>a?{isValid:!1,error:`Testo troppo lungo (max ${a} caratteri)`}:i.test(e)||r.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0},u=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},x=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},m=new Map,p=(e,a,t)=>{let s=Date.now(),r=m.get(e);return!r||s>r.resetTime?(m.set(e,{count:1,resetTime:s+t}),!0):!(r.count>=a)&&(r.count++,!0)},h=e=>{let a={},t=n(e.username);if(t.isValid||(a.username=t.error),e.password){let t=d(e.password);t.isValid||(a.password=t.error)}let s=u(e.ragione_sociale);if(s.isValid||(a.ragione_sociale=s.error),e.email){let t=o(e.email);t.isValid||(a.email=t.error)}if(e.vat){let t=x(e.vat);t.isValid||(a.vat=t.error)}if(e.indirizzo){let t=c(e.indirizzo,200);t.isValid||(a.indirizzo=t.error)}if(e.nazione){let t=c(e.nazione,50);t.isValid||(a.nazione=t.error)}if(e.referente_aziendale){let t=c(e.referente_aziendale,100);t.isValid||(a.referente_aziendale=t.error)}return{isValid:0===Object.keys(a).length,errors:a}}},83997:e=>{"use strict";e.exports=require("tty")},85763:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>n});var s=t(60687),r=t(43210),i=t(41360),l=t(4780);let n=i.bL,d=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.B8,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));d.displayName=i.B8.displayName;let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.l9,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));o.displayName=i.l9.displayName;let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(i.UC,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));c.displayName=i.UC.displayName},88091:(e,a,t)=>{Promise.resolve().then(t.bind(t,1132))},89667:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var s=t(60687);t(43210);var r=t(4780);function i({className:e,type:a,...t}){return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},94735:e=>{"use strict";e.exports=require("events")},96504:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(a,d);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},96834:(e,a,t)=>{"use strict";t.d(a,{E:()=>d});var s=t(60687);t(43210);var r=t(8730),i=t(24224),l=t(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:a,asChild:t=!1,...i}){let d=t?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(n({variant:a}),e),...i})}}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[447,538,658,952,146,892,660,615],()=>t(96504));module.exports=s})();