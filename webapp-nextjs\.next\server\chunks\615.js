exports.id=615,exports.ids=[615],exports.modules={4780:(e,t,a)=>{"use strict";a.d(t,{cn:()=>r});var s=a(49384),i=a(82348);function r(...e){return(0,i.QP)((0,s.$)(e))}},9861:(e,t,a)=>{Promise.resolve().then(a.bind(a,10501)),Promise.resolve().then(a.bind(a,96141)),Promise.resolve().then(a.bind(a,63213))},10501:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>S});var s=a(60687),i=a(43210),r=a(85814),n=a.n(r),o=a(16189),l=a(29523),c=a(55527),d=a(63213),p=a(32192),m=a(17313),u=a(23361),h=a(19080),v=a(10022),x=a(53411),b=a(6727),f=a(58559),g=a(78272),w=a(16023),j=a(31158),C=a(41312),N=a(40083),y=a(11860),$=a(12941);let A=(e,t,a,s)=>{let i={name:"owner"===e?"Menu Admin":"user"===e?"Lista Cantieri":"cantieri_user"===e?"Gestione Cavi":"Home",href:"owner"===e?"/admin":"user"===e?"/cantieri":"cantieri_user"===e?"/cavi":"/",icon:p.A};if("owner"===e&&!t)return[i];if("user"===e||t&&a?.role==="user"){let e=[i];return t&&e.push({name:"Cantieri",href:"/cantieri",icon:m.A}),s&&e.push({name:"Visualizza Cavi",href:"/cavi",icon:u.A},{name:"Parco Cavi",href:"/parco-cavi",icon:h.A},{name:"Gestione Excel",href:"/excel",icon:v.A,hasDropdown:!0},{name:"Report",href:"/reports",icon:x.A},{name:"Gestione Comande",href:"/comande",icon:b.A},{name:"⚡ Produttivit\xe0",href:"/productivity",icon:f.A}),e}if("cantieri_user"===e||t&&a?.role==="cantieri_user"){let t=[i];return s&&("cantieri_user"!==e&&t.push({name:"Visualizza Cavi",href:"/cavi",icon:u.A}),t.push({name:"Parco Cavi",href:"/parco-cavi",icon:h.A},{name:"Gestione Excel",href:"/excel",icon:v.A,hasDropdown:!0},{name:"Report",href:"/reports",icon:x.A},{name:"Gestione Comande",href:"/comande",icon:b.A},{name:"⚡ Produttivit\xe0",href:"/productivity",icon:f.A})),t}return[i]};function S(){let[e,t]=(0,i.useState)(!1),[a,r]=(0,i.useState)(!1),p=(0,i.useRef)(null),h=(0,o.usePathname)(),{user:v,cantiere:x,isAuthenticated:b,isImpersonating:f,impersonatedUser:S,logout:k}=(0,d.A)(),P=x?.id_cantiere||0,T=x?.commessa||`Cantiere ${P}`,_=A(v?.ruolo,f,S,P);return"/login"!==h&&b?(0,s.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm",children:[(0,s.jsx)("div",{className:"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 cursor-default",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center",children:(0,s.jsx)(u.A,{className:"w-5 h-5 text-white"})}),(0,s.jsxs)("div",{className:"hidden sm:block",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-slate-900",children:"CABLYS"}),(0,s.jsx)("p",{className:"text-xs text-slate-500 -mt-1",children:"Cable Installation System"})]})]}),(0,s.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:_.map(e=>{let t=h===e.href||"/"!==e.href&&h.startsWith(e.href),i=e.icon;return e.hasDropdown&&"Gestione Excel"===e.name?(0,s.jsxs)("div",{className:"relative",ref:p,children:[(0,s.jsxs)(l.$,{variant:"ghost",size:"sm",className:`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent ${t?"bg-blue-100 text-blue-700":"text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200"}`,onClick:()=>r(!a),children:[(0,s.jsx)(i,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"hidden lg:inline",children:e.name}),(0,s.jsx)(g.A,{className:"w-3 h-3"})]}),a&&(0,s.jsx)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white border border-slate-200 rounded-md shadow-lg z-50",children:(0,s.jsxs)("div",{className:"py-1",children:[(0,s.jsx)(n(),{href:"/excel/import",className:"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100",onClick:()=>r(!1),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(w.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Importa Excel"})]})}),(0,s.jsx)(n(),{href:"/excel/export",className:"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100",onClick:()=>r(!1),children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Esporta Excel"})]})})]})})]},e.name):(0,s.jsx)(n(),{href:e.href,children:t?(0,s.jsx)(c.jn,{size:"sm",className:"flex items-center space-x-2",icon:(0,s.jsx)(i,{className:"w-4 h-4"}),children:(0,s.jsx)("span",{className:"hidden lg:inline",children:e.name})}):(0,s.jsxs)(c.eU,{size:"sm",className:"flex items-center space-x-2 px-3 py-2 text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 ease-in-out rounded-md border border-transparent",children:[(0,s.jsx)(i,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"hidden lg:inline",children:e.name})]})},e.name)})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 ml-8",children:[P&&P>0&&(0,s.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md",children:[(0,s.jsx)(m.A,{className:"w-3 h-3 text-blue-600"}),(0,s.jsx)("div",{className:"text-xs",children:(0,s.jsx)("span",{className:"text-blue-900 font-medium",children:T})})]}),(0,s.jsxs)("div",{className:"hidden sm:flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("p",{className:"text-sm font-medium text-slate-900",children:[f&&S?S.username:v?.username,(0,s.jsxs)("span",{className:"text-xs text-slate-500 ml-1",children:["(",v?.ruolo==="owner"?"owner":v?.ruolo||"",")"]})]})}),(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(C.A,{className:"w-3 h-3 text-white"})}),(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:k,title:f?"Torna al menu admin":"Logout",className:"hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md",children:(0,s.jsx)(N.A,{className:"w-4 h-4"})})]}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md",children:e?(0,s.jsx)(y.A,{className:"w-5 h-5"}):(0,s.jsx)($.A,{className:"w-5 h-5"})})})]})]})}),e&&(0,s.jsxs)("div",{className:"md:hidden border-t border-slate-200 bg-white",children:[(0,s.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:_.map(e=>{let a=h===e.href||"/"!==e.href&&h.startsWith(e.href),i=e.icon;return(0,s.jsx)(n(),{href:e.href,children:a?(0,s.jsx)(c.jn,{size:"sm",className:"w-full justify-start space-x-3",onClick:()=>t(!1),icon:(0,s.jsx)(i,{className:"w-4 h-4"}),children:e.name}):(0,s.jsxs)(c.eU,{size:"sm",className:"w-full justify-start space-x-3 text-slate-600 hover:text-slate-900 hover:bg-blue-50 transition-all duration-200 ease-in-out",onClick:()=>t(!1),children:[(0,s.jsx)(i,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.name})]})},e.name)})}),(0,s.jsx)("div",{className:"border-t border-slate-200 px-4 py-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:v?(0,s.jsx)(C.A,{className:"w-3 h-3 text-white"}):(0,s.jsx)(m.A,{className:"w-3 h-3 text-white"})}),(0,s.jsx)("div",{children:(0,s.jsxs)("p",{className:"text-sm font-medium text-slate-900",children:[f&&S?S.username:v?v.username:x?.commessa,(0,s.jsxs)("span",{className:"text-xs text-slate-500 ml-1",children:["(",v?.ruolo==="owner"?"owner":v?.ruolo||"Cantiere",")"]})]})})]})})]})]}):null}},15941:(e,t,a)=>{Promise.resolve().then(a.bind(a,93319)),Promise.resolve().then(a.bind(a,79737)),Promise.resolve().then(a.bind(a,29131))},29131:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>i});var s=a(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx","useAuth");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\contexts\\AuthContext.tsx","AuthProvider")},29523:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(60687);a(43210);var i=a(8730),r=a(24224),n=a(4780);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:r=!1,...l}){let c=r?i.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:a,className:e})),...l})}},51021:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,86346,23)),Promise.resolve().then(a.t.bind(a,27924,23)),Promise.resolve().then(a.t.bind(a,35656,23)),Promise.resolve().then(a.t.bind(a,40099,23)),Promise.resolve().then(a.t.bind(a,38243,23)),Promise.resolve().then(a.t.bind(a,28827,23)),Promise.resolve().then(a.t.bind(a,62763,23)),Promise.resolve().then(a.t.bind(a,97173,23))},55527:(e,t,a)=>{"use strict";a.d(t,{Qi:()=>m,eU:()=>h,jn:()=>c,k7:()=>p,rp:()=>u,tA:()=>d});var s=a(60687),i=a(43210),r=a.n(i),n=a(4780),o=a(41862);let l=r().forwardRef(({className:e,variant:t="primary",size:a="md",loading:i=!1,glow:r=!1,icon:l,children:c,disabled:d,...p},m)=>{let u=d||i;return(0,s.jsxs)("button",{className:(0,n.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[t],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[a],r&&"quick"!==t&&"btn-glow",u&&"opacity-50 cursor-not-allowed hover:shadow-none",e),disabled:u,ref:m,...p,children:[(0,s.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,s.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[i?(0,s.jsx)(o.A,{className:"h-4 w-4 animate-spin btn-icon"}):l?(0,s.jsx)("span",{className:"btn-icon",children:l}):null,c]})]})});l.displayName="AnimatedButton";let c=e=>(0,s.jsx)(l,{variant:"primary",...e}),d=e=>(0,s.jsx)(l,{variant:"secondary",...e}),p=e=>(0,s.jsx)(l,{variant:"success",...e}),m=e=>(0,s.jsx)(l,{variant:"danger",...e}),u=e=>(0,s.jsx)(l,{variant:"outline",...e}),h=e=>(0,s.jsx)(l,{variant:"quick",...e})},61135:()=>{},62185:(e,t,a)=>{"use strict";a.d(t,{AR:()=>c,At:()=>n,CV:()=>l,FH:()=>i,Fw:()=>o,ZQ:()=>r,_I:()=>m,dG:()=>u,km:()=>d,mg:()=>p});let s=a(51060).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let i={get:async(e,t)=>(await s.get(e,t)).data,post:async(e,t,a)=>(await s.post(e,t,a)).data,put:async(e,t,a)=>(await s.put(e,t,a)).data,patch:async(e,t,a)=>(await s.patch(e,t,a)).data,delete:async(e,t)=>(await s.delete(e,t)).data},r={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await s.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>i.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(e,t)=>i.get(`/api/cavi/${e}`,{params:t}),getCavo:(e,t)=>i.get(`/api/cavi/${e}/${t}`),checkCavo:(e,t)=>i.get(`/api/cavi/${e}/check/${t}`),createCavo:(e,t)=>i.post(`/api/cavi/${e}`,t),updateCavo:(e,t,a)=>i.put(`/api/cavi/${e}/${t}`,a),deleteCavo:(e,t,a)=>i.delete(`/api/cavi/${e}/${t}`,{data:a}),updateMetriPosati:(e,t,a,s)=>i.put(`/api/cavi/${e}/${t}/metri-posati`,{metri_posati:a,id_bobina:s}),updateBobina:(e,t,a)=>i.put(`/api/cavi/${e}/${t}/bobina`,{id_bobina:a}),collegaCavo:(e,t,a,s)=>i.post(`/api/cavi/${e}/${t}/collegamento`,{lato:a,responsabile:s}),scollegaCavo:(e,t,a)=>i.delete(`/api/cavi/${e}/${t}/collegamento`,{data:{lato:a}}),markAsSpare:(e,t,a)=>i.put(`/api/cavi/${e}/${t}/spare`,{spare:+!!a}),debugCavi:e=>i.get(`/api/cavi/debug/${e}`),debugCaviRaw:e=>i.get(`/api/cavi/debug/raw/${e}`)},o={getBobine:(e,t)=>i.get(`/api/parco-cavi/${e}`,{params:t}),getBobina:(e,t)=>i.get(`/api/parco-cavi/${e}/${t}`),getBobineCompatibili:(e,t)=>i.get(`/api/parco-cavi/${e}/compatibili`,{params:t}),createBobina:(e,t)=>i.post(`/api/parco-cavi/${e}`,t),updateBobina:(e,t,a)=>i.put(`/api/parco-cavi/${e}/${t}`,a),deleteBobina:(e,t)=>i.delete(`/api/parco-cavi/${e}/${t}`),checkDisponibilita:(e,t,a)=>i.get(`/api/parco-cavi/${e}/${t}/disponibilita`,{params:{metri_richiesti:a}})},l={getComande:e=>i.get(`/api/comande/cantiere/${e}`),getComanda:(e,t)=>i.get(`/api/comande/cantiere/${e}/${t}`),createComanda:(e,t)=>i.post(`/api/comande/cantiere/${e}`,t),createComandaWithCavi:(e,t,a)=>i.post(`/api/comande/cantiere/${e}/crea-con-cavi`,t,{params:{lista_id_cavi:a}}),updateComanda:(e,t,a)=>i.put(`/api/comande/cantiere/${e}/${t}`,a),deleteComanda:(e,t)=>i.delete(`/api/comande/cantiere/${e}/${t}`),assegnaCavi:(e,t,a)=>i.post(`/api/comande/cantiere/${e}/${t}/assegna-cavi`,{cavi_ids:a}),rimuoviCavi:(e,t,a)=>i.delete(`/api/comande/cantiere/${e}/${t}/rimuovi-cavi`,{data:{cavi_ids:a}}),getStatistiche:e=>i.get(`/api/comande/cantiere/${e}/statistiche`),cambiaStato:(e,t,a)=>i.put(`/api/comande/cantiere/${e}/${t}/stato`,{nuovo_stato:a})},c={getResponsabili:e=>i.get(`/api/responsabili/cantiere/${e}`),createResponsabile:(e,t)=>i.post(`/api/responsabili/${e}`,t),updateResponsabile:(e,t,a)=>i.put(`/api/responsabili/${e}/${t}`,a),deleteResponsabile:(e,t)=>i.delete(`/api/responsabili/${e}/${t}`)},d={getCertificazioni:e=>i.get(`/api/cantieri/${e}/certificazioni`),createCertificazione:(e,t)=>i.post(`/api/cantieri/${e}/certificazioni`,t),generatePDF:(e,t)=>i.get(`/api/cantieri/${e}/pdf-cei-64-8/${t}`,{responseType:"blob"})},p={importCavi:(e,t,a)=>{let s=new FormData;return s.append("file",t),s.append("revisione",a),i.post(`/api/excel/${e}/import-cavi`,s,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),i.post(`/api/excel/${e}/import-parco-bobine`,a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>i.get(`/api/excel/${e}/export-cavi`,{responseType:"blob"}),exportBobine:e=>i.get(`/api/excel/${e}/export-parco-bobine`,{responseType:"blob"})},m={getCantieri:()=>i.get("/api/cantieri"),getCantiere:e=>i.get(`/api/cantieri/${e}`),createCantiere:e=>i.post("/api/cantieri",e),updateCantiere:(e,t)=>i.put(`/api/cantieri/${e}`,t)},u={getUsers:()=>i.get("/api/users"),getUser:e=>i.get(`/api/users/${e}`),createUser:e=>i.post("/api/users",e),updateUser:(e,t)=>i.put(`/api/users/${e}`,t),deleteUser:e=>i.delete(`/api/users/${e}`),toggleUserStatus:e=>i.get(`/api/users/toggle/${e}`),checkExpiredUsers:()=>i.get("/api/users/check-expired"),impersonateUser:e=>i.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>i.get("/api/users/db-raw"),resetDatabase:()=>i.post("/api/admin/reset-database")}},63213:(e,t,a)=>{"use strict";a.d(t,{A:()=>o,AuthProvider:()=>l});var s=a(60687),i=a(43210),r=a(62185);let n=(0,i.createContext)(void 0);function o(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l({children:e}){let[t,a]=(0,i.useState)(null),[o,l]=(0,i.useState)(null),[c,d]=(0,i.useState)(!0),[p,m]=(0,i.useState)(()=>!1),[u,h]=(0,i.useState)(()=>null),v=async()=>{try{return console.log("Verificando autenticazione all'avvio..."),void d(!1)}catch(e){console.error("Errore generale durante la verifica dell'autenticazione:",e),a(null),l(null)}finally{console.log("Completata verifica autenticazione, loading:",!1),setTimeout(()=>{d(!1)},500)}},x=async(e,t)=>{try{console.log("Tentativo di login utente:",e),d(!0);let a=await r.ZQ.login({username:e,password:t});console.log("Risposta login ricevuta:",a)}catch(e){throw console.error("Errore login:",e),e}finally{d(!1)}},b=async(e,t)=>{try{console.log("Tentativo di login cantiere:",e),d(!0);let a=await r.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});console.log("Risposta login cantiere ricevuta:",a)}catch(e){throw console.error("Errore login cantiere:",e),e}finally{d(!1)}},f=async e=>{try{await r.dG.impersonateUser(e)}catch(e){throw console.error("Errore durante l'impersonificazione:",e),e}};return(0,s.jsx)(n.Provider,{value:{user:t,cantiere:o,isAuthenticated:!!t||!!o,isLoading:c,isImpersonating:p,impersonatedUser:u,login:x,loginCantiere:b,logout:()=>{console.log("Logout completo - uscita dal sistema")},checkAuth:v,impersonateUser:f},children:e})}},69581:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,16444,23)),Promise.resolve().then(a.t.bind(a,16042,23)),Promise.resolve().then(a.t.bind(a,88170,23)),Promise.resolve().then(a.t.bind(a,49477,23)),Promise.resolve().then(a.t.bind(a,29345,23)),Promise.resolve().then(a.t.bind(a,12089,23)),Promise.resolve().then(a.t.bind(a,46577,23)),Promise.resolve().then(a.t.bind(a,31307,23))},79737:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\components\\ui\\toaster.tsx","Toaster")},93319:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\components\\layout\\Navbar.tsx","Navbar")},94431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m,metadata:()=>p});var s=a(37413),i=a(35759),r=a.n(i),n=a(29404),o=a.n(n);a(61135);var l=a(93319),c=a(29131),d=a(79737);let p={title:"CABLYS - Cable Installation Advance System",description:"Sistema avanzato per la gestione dell'installazione cavi",manifest:"/manifest.json",themeColor:"#2563eb",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",appleWebApp:{capable:!0,statusBarStyle:"default",title:"CABLYS"}};function m({children:e}){return(0,s.jsx)("html",{lang:"it",children:(0,s.jsx)("body",{className:`${r().variable} ${o().variable} antialiased`,children:(0,s.jsxs)(c.AuthProvider,{children:[(0,s.jsxs)("div",{className:"min-h-screen bg-slate-50",children:[(0,s.jsx)(l.Navbar,{}),(0,s.jsx)("main",{className:"pt-16",children:e})]}),(0,s.jsx)(d.Toaster,{})]})})})}},96141:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>b});var s=a(60687),i=a(43210);let r=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),p({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?o(a):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],d={toasts:[]};function p(e){d=l(d,e),c.forEach(e=>{e(d)})}function m({...e}){let t=(r=(r+1)%Number.MAX_VALUE).toString(),a=()=>p({type:"DISMISS_TOAST",toastId:t});return p({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>p({type:"UPDATE_TOAST",toast:{...e,id:t}})}}var u=a(29523),h=a(93613),v=a(5336),x=a(11860);function b(){let{toasts:e,dismiss:t}=function(){let[e,t]=(0,i.useState)(d);return{...e,toast:m,dismiss:e=>p({type:"DISMISS_TOAST",toastId:e})}}();return(0,s.jsx)("div",{className:"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",children:e.map(e=>(0,s.jsxs)("div",{className:"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",style:{backgroundColor:"destructive"===e.variant?"#fef2f2":"#f0f9ff",borderColor:"destructive"===e.variant?"#fecaca":"#bae6fd"},children:[(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:["destructive"===e.variant?(0,s.jsx)(h.A,{className:"h-4 w-4 text-red-600 mt-0.5"}):(0,s.jsx)(v.A,{className:"h-4 w-4 text-green-600 mt-0.5"}),(0,s.jsxs)("div",{className:"grid gap-1",children:[e.title&&(0,s.jsx)("div",{className:`text-sm font-semibold ${"destructive"===e.variant?"text-red-900":"text-gray-900"}`,children:e.title}),e.description&&(0,s.jsx)("div",{className:`text-sm ${"destructive"===e.variant?"text-red-700":"text-gray-700"}`,children:e.description})]})]}),(0,s.jsx)(u.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent",onClick:()=>t(e.id),children:(0,s.jsx)(x.A,{className:"h-3 w-3"})})]},e.id))})}}};