(()=>{var e={};e.id=162,e.ids=[162],e.modules={2892:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36200)),"C:\\CMS\\webapp-nextjs\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\CMS\\webapp-nextjs\\src\\app\\forgot-password\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(e,r,t)=>{"use strict";t.d(r,{A:()=>n,q:()=>i});var s=t(43210),a=t(60687);function i(e,r){let t=s.createContext(r),i=e=>{let{children:r,...i}=e,n=s.useMemo(()=>i,Object.values(i));return(0,a.jsx)(t.Provider,{value:n,children:r})};return i.displayName=e+"Provider",[i,function(a){let i=s.useContext(t);if(i)return i;if(void 0!==r)return r;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function n(e,r=[]){let t=[],i=()=>{let r=t.map(e=>s.createContext(e));return function(t){let a=t?.[e]||r;return s.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return i.scopeName=e,[function(r,i){let n=s.createContext(i),l=t.length;t=[...t,i];let o=r=>{let{scope:t,children:i,...o}=r,d=t?.[e]?.[l]||n,u=s.useMemo(()=>o,Object.values(o));return(0,a.jsx)(d.Provider,{value:u,children:i})};return o.displayName=r+"Provider",[o,function(t,a){let o=a?.[e]?.[l]||n,d=s.useContext(o);if(d)return d;if(void 0!==i)return i;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((r,{useScope:t,scopeName:s})=>{let a=t(e)[`__scope${s}`];return{...r,...a}},{});return s.useMemo(()=>({[`__scope${r.scopeName}`]:a}),[a])}};return t.scopeName=r.scopeName,t}(i,...r)]}},12412:e=>{"use strict";e.exports=require("assert")},14163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>l});var s=t(43210),a=t(51215),i=t(8730),n=t(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,i.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?t:r,{...i,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function o(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24795:(e,r,t)=>{Promise.resolve().then(t.bind(t,36200))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36200:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\forgot-password\\page.tsx","default")},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43649:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var s=t(60687);t(43210);var a=t(4780);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},46657:(e,r,t)=>{"use strict";t.d(r,{k:()=>y});var s=t(60687),a=t(43210),i=t(11273),n=t(14163),l="Progress",[o,d]=(0,i.A)(l),[u,c]=o(l),p=a.forwardRef((e,r)=>{var t,a;let{__scopeProgress:i,value:l=null,max:o,getValueLabel:d=f,...c}=e;(o||0===o)&&!g(o)&&console.error((t=`${o}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=g(o)?o:100;null===l||b(l,p)||console.error((a=`${l}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=b(l,p)?l:null,x=h(m)?d(m,p):void 0;return(0,s.jsx)(u,{scope:i,value:m,max:p,children:(0,s.jsx)(n.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":h(m)?m:void 0,"aria-valuetext":x,role:"progressbar","data-state":v(m,p),"data-value":m??void 0,"data-max":p,...c,ref:r})})});p.displayName=l;var m="ProgressIndicator",x=a.forwardRef((e,r)=>{let{__scopeProgress:t,...a}=e,i=c(m,t);return(0,s.jsx)(n.sG.div,{"data-state":v(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:r})});function f(e,r){return`${Math.round(e/r*100)}%`}function v(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function h(e){return"number"==typeof e}function g(e){return h(e)&&!isNaN(e)&&e>0}function b(e,r){return h(e)&&!isNaN(e)&&e<=r&&e>=0}x.displayName=m;var j=t(4780);function y({className:e,value:r,...t}){return(0,s.jsx)(p,{"data-slot":"progress",className:(0,j.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...t,children:(0,s.jsx)(x,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})})}},48735:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var s=t(60687),a=t(43210),i=t(85814),n=t.n(i),l=t(23361),o=t(28559),d=t(29523),u=t(89667),c=t(80013),p=t(44493),m=t(91821);t(46657);var x=t(41550),f=t(5336),v=t(43649),h=t(99891),g=t(4780);function b(){let[e,r]=(0,a.useState)({email:"",userType:"user"}),[t,i]=(0,a.useState)(!1),[n,l]=(0,a.useState)(null),o=async t=>{t.preventDefault(),i(!0),l(null);try{if(!e.email)throw Error("L'indirizzo email \xe8 obbligatorio");let t=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,user_type:e.userType})}),s=await t.json();if(t.ok&&s.success)l({type:"success",text:"Se l'email \xe8 registrata, riceverai le istruzioni per il reset della password."}),r({email:"",userType:"user"});else throw Error(s.detail||s.message||"Errore durante la richiesta di reset")}catch(e){l({type:"error",text:e instanceof Error?e.message:"Errore durante la richiesta di reset"})}finally{i(!1)}};return(0,s.jsxs)(p.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(p.aR,{className:"space-y-1",children:[(0,s.jsxs)(p.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-mariner-600"}),"Recupera Password"]}),(0,s.jsx)(p.BT,{children:"Inserisci la tua email per ricevere le istruzioni di reset"})]}),(0,s.jsxs)(p.Wu,{children:[(0,s.jsxs)("form",{onSubmit:o,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{children:"Tipo di Account"}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,s.jsx)("input",{type:"radio",value:"user",checked:"user"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,s.jsx)("span",{children:"Utente"})]}),(0,s.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,s.jsx)("input",{type:"radio",value:"cantiere",checked:"cantiere"===e.userType,onChange:e=>r(r=>({...r,userType:e.target.value})),className:"text-mariner-600"}),(0,s.jsx)("span",{children:"Cantiere"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(c.J,{htmlFor:"email",children:"Indirizzo Email"}),(0,s.jsx)(u.p,{id:"email",type:"email",value:e.email,onChange:e=>r(r=>({...r,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),n&&(0,s.jsxs)(m.Fc,{className:(0,g.cn)("success"===n.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===n.type?(0,s.jsx)(f.A,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(v.A,{className:"h-4 w-4 text-red-600"}),(0,s.jsx)(m.TN,{className:(0,g.cn)("success"===n.type?"text-green-800":"text-red-800"),children:n.text})]}),(0,s.jsx)(d.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:t,children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Invio in corso..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Invia Link di Reset"]})})]}),(0,s.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Informazioni sulla Sicurezza"}),(0,s.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,s.jsx)("li",{children:"• Il link di reset \xe8 valido per 30 minuti"}),(0,s.jsx)("li",{children:"• Pu\xf2 essere utilizzato una sola volta"}),(0,s.jsx)("li",{children:"• Se non ricevi l'email, controlla la cartella spam"}),(0,s.jsx)("li",{children:"• Per motivi di sicurezza, non riveleremo se l'email \xe8 registrata"})]})]})]})})]})]})}function j(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,s.jsx)(l.A,{className:"w-8 h-8 text-white"})})}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,s.jsx)("p",{className:"text-slate-600",children:"Recupero Password"})]}),(0,s.jsx)(b,{}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(n(),{href:"/login",children:(0,s.jsxs)(d.$,{variant:"ghost",className:"text-slate-600 hover:text-slate-900",children:[(0,s.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Torna al Login"]})})})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},77939:(e,r,t)=>{Promise.resolve().then(t.bind(t,48735))},78148:(e,r,t)=>{"use strict";t.d(r,{b:()=>l});var s=t(43210),a=t(14163),i=t(60687),n=s.forwardRef((e,r)=>(0,i.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var l=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var s=t(60687);t(43210);var a=t(78148),i=t(4780);function n({className:e,...r}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d});var s=t(60687),a=t(43210),i=t(24224),n=t(4780);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(l({variant:r}),e),...t}));o.displayName="Alert",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...r})).displayName="AlertTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...r}));d.displayName="AlertDescription"},94735:e=>{"use strict";e.exports=require("events")},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,538,658,615],()=>t(2892));module.exports=s})();