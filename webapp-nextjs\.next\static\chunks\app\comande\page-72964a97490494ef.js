(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[484],{1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},13717:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var a=s(12115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:o="",children:x,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...d,width:r,height:r,stroke:s,strokeWidth:l?24*Number(i)/Number(r):i,className:n("lucide",o),...!x&&!c(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let s=(0,a.forwardRef)((s,i)=>{let{className:c,...d}=s;return(0,a.createElement)(o,{ref:i,iconNode:t,className:n("lucide-".concat(r(l(e))),"lucide-".concat(e),c),...d})});return s.displayName=l(e),s}},21815:(e,t,s)=>{Promise.resolve().then(s.bind(s,38220))},25273:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var a=s(95155);s(12115);var r=s(99708),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:i=!1,...c}=e,d=i?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),t),...c})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(95155);s(12115);var r=s(99708),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:i,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,l.cn)(n({variant:s,size:i,className:t})),...d})}},38220:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(95155),r=s(12115),i=s(66695),l=s(30285),n=s(26126),c=s(40283),d=s(25731),o=s(84616),x=s(25273),u=s(19946);let m=(0,u.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var h=s(40646),p=s(14186),v=s(51154),g=s(1243),j=s(92657),f=s(13717);let b=(0,u.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);var N=s(17580);function y(){let[e,t]=(0,r.useState)("active"),[s,u]=(0,r.useState)([]),[y,A]=(0,r.useState)([]),[w,k]=(0,r.useState)(!0),[_,C]=(0,r.useState)(""),{user:E,cantiere:z}=(0,c.A)();(0,r.useEffect)(()=>{S()},[]);let S=async()=>{try{k(!0),C("");let e=(null==z?void 0:z.id_cantiere)||(null==E?void 0:E.id_utente);if(!e)return void C("Cantiere non selezionato");let[t,s]=await Promise.all([d.CV.getComande(e),d.AR.getResponsabili(e)]);u(t),A(s)}catch(s){var e,t;console.error("Errore caricamento dati:",s),C((null==(t=s.response)||null==(e=t.data)?void 0:e.detail)||"Errore durante il caricamento dei dati")}finally{k(!1)}},O=e=>{switch(e){case"completata":return(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"in_corso":return(0,a.jsx)(n.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"pianificata":return(0,a.jsx)(n.E,{className:"bg-yellow-100 text-yellow-800",children:"Pianificata"});case"sospesa":return(0,a.jsx)(n.E,{className:"bg-red-100 text-red-800",children:"Sospesa"});default:return(0,a.jsx)(n.E,{variant:"secondary",children:e})}},T=e=>(0,a.jsx)(n.E,{className:{POSA:"bg-blue-100 text-blue-800",COLLEGAMENTO_PARTENZA:"bg-green-100 text-green-800",COLLEGAMENTO_ARRIVO:"bg-purple-100 text-purple-800",CERTIFICAZIONE:"bg-orange-100 text-orange-800"}[e]||"bg-gray-100 text-gray-800",children:e}),M=s.filter(t=>{switch(e){case"active":return"IN_CORSO"===t.stato||"ASSEGNATA"===t.stato||"CREATA"===t.stato;case"completed":return"COMPLETATA"===t.stato;default:return!0}}),R={totali:s.length,in_corso:s.filter(e=>"IN_CORSO"===e.stato).length,completate:s.filter(e=>"COMPLETATA"===e.stato).length,pianificate:s.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end mb-6",children:(0,a.jsxs)(l.$,{size:"sm",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:R.totali})]}),(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:R.in_corso})]}),(0,a.jsx)(m,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Completate"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:R.completate})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Pianificate"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:R.pianificate})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(i.ZB,{children:"Elenco Comande"}),(0,a.jsx)("div",{className:"flex gap-2",children:[{key:"active",label:"Attive"},{key:"completed",label:"Completate"},{key:"all",label:"Tutte"}].map(s=>(0,a.jsx)(l.$,{variant:e===s.key?"default":"outline",size:"sm",onClick:()=>t(s.key),children:s.label},s.key))})]})}),(0,a.jsx)(i.Wu,{children:w?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})}):_?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-600",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),_]})}):0===M.length?(0,a.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"}):(0,a.jsx)("div",{className:"space-y-4",children:M.map(e=>(0,a.jsx)(i.Zp,{className:"border-l-4 border-l-blue-500",children:(0,a.jsxs)(i.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-slate-900",children:e.codice_comanda}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:e.descrizione||"Nessuna descrizione"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[T(e.tipo_comanda),O(e.stato)]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Responsabile"}),(0,a.jsx)("p",{className:"font-medium",children:e.responsabile||"Non assegnato"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Team"}),(0,a.jsxs)("p",{className:"font-medium",children:[e.numero_componenti_squadra||0," persone"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Creazione"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Scadenza"}),(0,a.jsx)("p",{className:"font-medium",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"Non definita"})]})]}),e.data_completamento&&(0,a.jsx)("div",{className:"mb-3 p-2 bg-green-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["Completata il ",new Date(e.data_completamento).toLocaleDateString("it-IT")]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-1"}),"Dettagli"]}),(0,a.jsxs)(l.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Modifica"]}),"IN_CORSO"===e.stato&&(0,a.jsxs)(l.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(b,{className:"h-4 w-4 mr-1"}),"Sospendi"]}),("CREATA"===e.stato||"ASSEGNATA"===e.stato)&&(0,a.jsxs)(l.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-1"}),"Avvia"]})]})]})},e.codice_comanda))})})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),"Responsabili"]}),(0,a.jsx)(i.BT,{children:"Gestione responsabili di cantiere"})]}),(0,a.jsx)(i.Wu,{children:w?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 animate-spin mr-2"}),"Caricamento..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"space-y-4",children:y.map(e=>{let t=s.filter(t=>t.responsabile===e.nome_responsabile&&("IN_CORSO"===t.stato||"ASSEGNATA"===t.stato)).length;return(0,a.jsxs)("div",{className:"p-3 bg-slate-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900",children:e.nome_responsabile}),(0,a.jsxs)(n.E,{variant:t>0?"default":"secondary",children:[t," attive"]})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[e.numero_telefono&&(0,a.jsx)("p",{children:e.numero_telefono}),e.mail&&(0,a.jsx)("p",{children:e.mail})]})]},e.id_responsabile)})}),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"w-full mt-4",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Nuovo Responsabile"]})]})})]})})]})]})})}},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51154:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(52596),r=s(39688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>l});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},74466:(e,t,s)=>{"use strict";s.d(t,{F:()=>l});var a=s(52596);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=a.$,l=(e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return i(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:l,defaultVariants:n}=t,c=Object.keys(l).map(e=>{let t=null==s?void 0:s[e],a=null==n?void 0:n[e];if(null===t)return null;let i=r(t)||r(a);return l[e][i]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return i(e,c,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:s,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...n,...d}[t]):({...n,...d})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[848,464,283,441,684,358],()=>t(21815)),_N_E=e.O()}]);