'use client'

import { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { ChevronDown, Edit, Clock, CheckCircle, Trash2 } from 'lucide-react'

interface CompactActionsDropdownProps {
  user: {
    id_utente: number
    ruolo: string
    abilitato: boolean
  }
  onEdit: () => void
  onToggleStatus: () => void
  onDelete: () => void
}

export default function CompactActionsDropdown({
  user,
  onEdit,
  onToggleStatus,
  onDelete
}: CompactActionsDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, position: 'bottom' as 'bottom' | 'top' })
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Calcola la posizione assoluta del dropdown
  const calculatePosition = () => {
    if (!buttonRef.current) return

    const buttonRect = buttonRef.current.getBoundingClientRect()
    const viewportHeight = window.innerHeight
    const dropdownHeight = 120 // Altezza stimata del dropdown
    const dropdownWidth = 144 // w-36 = 144px

    let top = buttonRect.bottom + 4 // mt-1
    let position: 'bottom' | 'top' = 'bottom'

    // Se non c'è spazio sotto, apri verso l'alto
    if (buttonRect.bottom + dropdownHeight > viewportHeight) {
      top = buttonRect.top - dropdownHeight - 4 // mb-1
      position = 'top'
    }

    // Calcola left per allineare a destra del pulsante
    const left = buttonRect.right - dropdownWidth

    setDropdownPosition({ top, left, position })
  }

  // Chiudi dropdown quando si clicca fuori
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    console.log('Dropdown toggle clicked', { isOpen, user: user.username })

    if (!isOpen) {
      calculatePosition()
    }
    setIsOpen(!isOpen)
  }

  const handleAction = (action: () => void, actionName: string) => {
    console.log(`Executing action: ${actionName} for user: ${user.username}`)
    action()
    setIsOpen(false)
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Triangolino più visibile */}
      <button
        ref={buttonRef}
        onClick={handleToggle}
        type="button"
        className="inline-flex items-center justify-center p-2 rounded hover:bg-slate-100 transition-colors cursor-pointer border border-transparent hover:border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 relative z-10"
        title="Azioni utente"
        aria-label="Menu azioni utente"
        aria-expanded={isOpen}
        style={{
          minWidth: '32px',
          minHeight: '32px',
          pointerEvents: 'auto'
        }}
      >
        <ChevronDown className={`h-4 w-4 text-slate-700 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} style={{ pointerEvents: 'none' }} />
      </button>

      {/* Dropdown Menu - versione semplice senza portal */}
      {isOpen && (
        <div className="absolute right-0 top-full mt-1 w-36 bg-white border border-slate-200 rounded-md shadow-lg z-50">
          <div className="py-1">
            {/* Modifica - sempre disponibile */}
            <button
              onClick={() => handleAction(onEdit, 'Modifica')}
              type="button"
              className="w-full px-3 py-2 text-left text-sm flex items-center gap-2 hover:bg-slate-50 transition-colors"
            >
              <Edit className="h-3.5 w-3.5 text-slate-600" />
              <span>Modifica</span>
            </button>

            {/* Abilita/Disabilita */}
            <button
              onClick={() => handleAction(onToggleStatus, user.abilitato ? 'Disabilita' : 'Abilita')}
              disabled={user.ruolo === 'owner'}
              type="button"
              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${
                user.ruolo === 'owner'
                  ? 'opacity-50 cursor-not-allowed bg-slate-50'
                  : 'hover:bg-slate-50'
              }`}
            >
              {user.abilitato ? (
                <>
                  <Clock className="h-3.5 w-3.5 text-red-500" />
                  <span>Disabilita</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  <span>Abilita</span>
                </>
              )}
            </button>

            {/* Elimina */}
            <button
              onClick={() => handleAction(onDelete, 'Elimina')}
              disabled={user.ruolo === 'owner'}
              type="button"
              className={`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${
                user.ruolo === 'owner'
                  ? 'opacity-50 cursor-not-allowed bg-slate-50'
                  : 'hover:bg-red-50 text-red-600'
              }`}
            >
              <Trash2 className="h-3.5 w-3.5 text-red-500" />
              <span>Elimina</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
