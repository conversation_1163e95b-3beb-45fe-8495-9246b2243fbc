{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "4ce89cd2df33755aed79a7af8f148fb7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5a827e6e9937850f296081b5262b9776ab759f71d655fee99af7f1f01ef862d3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fb2f1a60d2b5ea3e3cca70e4ba8087c245fc848fb8cf79fbce21179ee72103ee"}}}, "instrumentation": null, "functions": {}}