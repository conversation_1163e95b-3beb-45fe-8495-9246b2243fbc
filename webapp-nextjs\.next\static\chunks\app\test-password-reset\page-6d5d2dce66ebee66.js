(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[849],{30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(95155);t(12115);var a=t(99708),n=t(74466),i=t(59434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:n,asChild:o=!1,...l}=e,c=o?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:n,className:r})),...l})}},55365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>l});var s=t(95155),a=t(12115),n=t(74466),i=t(59434);let d=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,...n}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(d({variant:a}),t),...n})});o.displayName="Alert",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...a})});l.displayName="AlertDescription"},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(95155);t(12115);var a=t(59434);function n(e){let{className:r,type:t,...n}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},63006:(e,r,t)=>{Promise.resolve().then(t.bind(t,90119))},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>n,aR:()=>i});var s=t(95155);t(12115);var a=t(59434);function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var s=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,i=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:d}=r,o=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],s=null==d?void 0:d[e];if(null===r)return null;let n=a(r)||a(s);return i[e][n]}),l=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return n(e,o,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...d,...l}[r]):({...d,...l})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},90119:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(95155),a=t(12115),n=t(30285),i=t(62523),d=t(66695),o=t(55365);function l(){let[e,r]=(0,a.useState)("<EMAIL>"),[t,l]=(0,a.useState)(null),[c,u]=(0,a.useState)(!1),v=async()=>{u(!0),l(null);try{console.log("Testing password reset for:",e);let r=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,user_type:"user"})}),t=await r.json();l({status:r.status,success:r.ok,data:t,headers:Object.fromEntries(r.headers.entries())}),console.log("Response:",{status:r.status,data:t})}catch(e){console.error("Error:",e),l({success:!1,error:e instanceof Error?e.message:"Unknown error"})}finally{u(!1)}},g=async()=>{u(!0),l(null);try{console.log("Testing backend direct for:",e);let r=await fetch("http://localhost:8001/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,user_type:"user"})}),t=await r.json();l({status:r.status,success:r.ok,data:t,headers:Object.fromEntries(r.headers.entries()),direct:!0}),console.log("Direct Response:",{status:r.status,data:t})}catch(e){console.error("Direct Error:",e),l({success:!1,error:e instanceof Error?e.message:"Unknown error",direct:!0})}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsxs)(d.Zp,{className:"w-full max-w-2xl",children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Test Password Reset System"})}),(0,s.jsxs)(d.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Email to test:"}),(0,s.jsx)(i.p,{type:"email",value:e,onChange:e=>r(e.target.value),placeholder:"Enter email to test"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{onClick:v,disabled:c,className:"flex-1",children:c?"Testing...":"Test via Next.js API"}),(0,s.jsx)(n.$,{onClick:g,disabled:c,variant:"outline",className:"flex-1",children:c?"Testing...":"Test Backend Direct"})]}),t&&(0,s.jsx)(o.Fc,{className:t.success?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,s.jsx)(o.TN,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Status:"})," ",t.status||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Success:"})," ",t.success?"Yes":"No"]}),t.direct&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Method:"})," Direct Backend"]}),t.error&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Error:"})," ",t.error]}),t.data&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Response Data:"}),(0,s.jsx)("pre",{className:"mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(t.data,null,2)})]})]})})})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[848,441,684,358],()=>r(63006)),_N_E=e.O()}]);