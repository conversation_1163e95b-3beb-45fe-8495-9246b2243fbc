'use client'

import { useMemo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Cable, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Zap,
  Package,
  BarChart3
} from 'lucide-react'
import { Cavo } from '@/types'

interface CaviStatisticsProps {
  cavi: Cavo[]
  filteredCavi: Cavo[]
  className?: string
  revisioneCorrente?: string
}

export default function CaviStatistics({
  cavi,
  filteredCavi,
  className,
  revisioneCorrente
}: CaviStatisticsProps) {
  const stats = useMemo(() => {
    const totalCavi = cavi.length
    const filteredCount = filteredCavi.length
    
    // Installation status
    const installati = filteredCavi.filter(c => 
      c.stato_installazione === 'Installato' || 
      (c.metri_posati && c.metri_posati > 0) ||
      (c.metratura_reale && c.metratura_reale > 0)
    ).length
    
    const inCorso = filteredCavi.filter(c => 
      c.stato_installazione === 'In corso'
    ).length
    
    const daInstallare = filteredCount - installati - inCorso
    
    // Connection status
    const collegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 3 // Both sides connected
    }).length
    
    const parzialmenteCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 1 || collegamento === 2 // One side connected
    }).length
    
    const nonCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)
    }).length
    
    // Certification status
    const certificati = filteredCavi.filter(c => 
      c.certificato === true || 
      c.certificato === 'SI' || 
      c.certificato === 'CERTIFICATO'
    ).length
    
    // Meters calculation
    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = filteredCavi.reduce((sum, c) => {
      const metri = c.metri_posati || c.metratura_reale || 0
      return sum + metri
    }, 0)
    
    const percentualeInstallazione = metriTotali > 0 ? (metriInstallati / metriTotali * 100) : 0
    
    return {
      totalCavi,
      filteredCount,
      installati,
      inCorso,
      daInstallare,
      collegati,
      parzialmenteCollegati,
      nonCollegati,
      certificati,
      metriTotali,
      metriInstallati,
      percentualeInstallazione
    }
  }, [cavi, filteredCavi])

  return (
    <Card className={className}>
      <CardContent className="p-4">
        {/* Header with revision */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4 text-mariner-600" />
            <span className="text-sm font-semibold text-mariner-900">Statistiche Cavi</span>
          </div>
          {revisioneCorrente && (
            <Badge variant="outline" className="text-xs font-medium">
              Rev. {revisioneCorrente}
            </Badge>
          )}
        </div>

        {/* Single row statistics */}
        <div className="flex flex-wrap items-center gap-3 text-sm">

          {/* Total cables */}
          <div className="flex items-center space-x-1.5 bg-mariner-50 px-3 py-1.5 rounded-lg">
            <Cable className="h-4 w-4 text-mariner-600" />
            <span className="font-bold text-mariner-900">{stats.filteredCount}</span>
            <span className="text-mariner-600">di {stats.totalCavi} cavi</span>
          </div>

          {/* Installation status */}
          <div className="flex items-center space-x-1.5 bg-green-50 px-3 py-1.5 rounded-lg">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="font-bold text-green-700">{stats.installati}</span>
            <span className="text-green-600">installati</span>
          </div>

          <div className="flex items-center space-x-1.5 bg-yellow-50 px-3 py-1.5 rounded-lg">
            <Clock className="h-4 w-4 text-yellow-600" />
            <span className="font-bold text-yellow-700">{stats.inCorso}</span>
            <span className="text-yellow-600">in corso</span>
          </div>

          <div className="flex items-center space-x-1.5 bg-gray-50 px-3 py-1.5 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-gray-600" />
            <span className="font-bold text-gray-700">{stats.daInstallare}</span>
            <span className="text-gray-600">da installare</span>
          </div>

          {/* Connection status */}
          <div className="flex items-center space-x-1.5 bg-blue-50 px-3 py-1.5 rounded-lg">
            <Zap className="h-4 w-4 text-blue-600" />
            <span className="font-bold text-blue-700">{stats.collegati}</span>
            <span className="text-blue-600">collegati</span>
          </div>

          {/* Certification status */}
          <div className="flex items-center space-x-1.5 bg-purple-50 px-3 py-1.5 rounded-lg">
            <Package className="h-4 w-4 text-purple-600" />
            <span className="font-bold text-purple-700">{stats.certificati}</span>
            <span className="text-purple-600">certificati</span>
          </div>

          {/* Meters progress */}
          <div className="flex items-center space-x-1.5 bg-indigo-50 px-3 py-1.5 rounded-lg">
            <div className="h-4 w-4 flex items-center justify-center">
              <div className="h-2.5 w-2.5 bg-indigo-600 rounded-full"></div>
            </div>
            <span className="font-bold text-indigo-700">{stats.metriInstallati.toLocaleString()}m</span>
            <span className="text-indigo-600">di {stats.metriTotali.toLocaleString()}m</span>
            <span className="text-indigo-700 font-semibold">({stats.percentualeInstallazione.toFixed(1)}%)</span>
          </div>

          {/* Connection details - only show if there are partial/non-connected cables */}
          {stats.parzialmenteCollegati > 0 && (
            <div className="flex items-center space-x-1.5 bg-yellow-50 px-3 py-1.5 rounded-lg">
              <div className="h-4 w-4 flex items-center justify-center">
                <div className="h-2.5 w-2.5 bg-yellow-500 rounded-full"></div>
              </div>
              <span className="font-bold text-yellow-700">{stats.parzialmenteCollegati}</span>
              <span className="text-yellow-600">parziali</span>
            </div>
          )}

          {stats.nonCollegati > 0 && (
            <div className="flex items-center space-x-1.5 bg-red-50 px-3 py-1.5 rounded-lg">
              <div className="h-4 w-4 flex items-center justify-center">
                <div className="h-2.5 w-2.5 bg-red-500 rounded-full"></div>
              </div>
              <span className="font-bold text-red-700">{stats.nonCollegati}</span>
              <span className="text-red-600">non collegati</span>
            </div>
          )}

        </div>

        {/* Progress bar */}
        {stats.metriTotali > 0 && (
          <div className="mt-4 bg-gray-50 p-3 rounded-lg">
            <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
              <span>Progresso installazione</span>
              <span className="text-mariner-700">{stats.percentualeInstallazione.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-mariner-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
