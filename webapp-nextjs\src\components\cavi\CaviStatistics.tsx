'use client'

import { useMemo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Cable, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Zap,
  Package,
  BarChart3
} from 'lucide-react'
import { Cavo } from '@/types'

interface CaviStatisticsProps {
  cavi: Cavo[]
  filteredCavi: Cavo[]
  className?: string
  revisioneCorrente?: string
}

export default function CaviStatistics({
  cavi,
  filteredCavi,
  className,
  revisioneCorrente
}: CaviStatisticsProps) {
  const stats = useMemo(() => {
    const totalCavi = cavi.length
    const filteredCount = filteredCavi.length
    
    // Installation status
    const installati = filteredCavi.filter(c => 
      c.stato_installazione === 'Installato' || 
      (c.metri_posati && c.metri_posati > 0) ||
      (c.metratura_reale && c.metratura_reale > 0)
    ).length
    
    const inCorso = filteredCavi.filter(c => 
      c.stato_installazione === 'In corso'
    ).length
    
    const daInstallare = filteredCount - installati - inCorso
    
    // Connection status
    const collegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 3 // Both sides connected
    }).length
    
    const parzialmenteCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 1 || collegamento === 2 // One side connected
    }).length
    
    const nonCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)
    }).length
    
    // Certification status
    const certificati = filteredCavi.filter(c => 
      c.certificato === true || 
      c.certificato === 'SI' || 
      c.certificato === 'CERTIFICATO'
    ).length
    
    // Meters calculation
    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = filteredCavi.reduce((sum, c) => {
      const metri = c.metri_posati || c.metratura_reale || 0
      return sum + metri
    }, 0)
    
    const percentualeInstallazione = metriTotali > 0 ? (metriInstallati / metriTotali * 100) : 0
    
    return {
      totalCavi,
      filteredCount,
      installati,
      inCorso,
      daInstallare,
      collegati,
      parzialmenteCollegati,
      nonCollegati,
      certificati,
      metriTotali,
      metriInstallati,
      percentualeInstallazione
    }
  }, [cavi, filteredCavi])

  return (
    <Card className={className}>
      <CardContent className="p-4">
        {/* Header with revision */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4 text-mariner-600" />
            <span className="text-sm font-semibold text-mariner-900">Statistiche Cavi</span>
          </div>
          {revisioneCorrente && (
            <Badge variant="outline" className="text-xs font-medium">
              Rev. {revisioneCorrente}
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">

          {/* Total cables */}
          <div className="flex items-center space-x-2 bg-mariner-50 p-2 rounded-lg">
            <Cable className="h-4 w-4 text-mariner-600 flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-bold text-mariner-900 text-base">{stats.filteredCount}</div>
              <div className="text-xs text-mariner-600 font-medium">
                di {stats.totalCavi} cavi
              </div>
            </div>
          </div>

          {/* Installation status */}
          <div className="flex items-center space-x-2 bg-green-50 p-2 rounded-lg">
            <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-bold text-green-700 text-base">{stats.installati}</div>
              <div className="text-xs text-green-600 font-medium">Installati</div>
            </div>
          </div>

          <div className="flex items-center space-x-2 bg-yellow-50 p-2 rounded-lg">
            <Clock className="h-4 w-4 text-yellow-600 flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-bold text-yellow-700 text-base">{stats.inCorso}</div>
              <div className="text-xs text-yellow-600 font-medium">In corso</div>
            </div>
          </div>

          <div className="flex items-center space-x-2 bg-gray-50 p-2 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-gray-600 flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-bold text-gray-700 text-base">{stats.daInstallare}</div>
              <div className="text-xs text-gray-600 font-medium">Da installare</div>
            </div>
          </div>

          {/* Connection status */}
          <div className="flex items-center space-x-2 bg-blue-50 p-2 rounded-lg">
            <Zap className="h-4 w-4 text-blue-600 flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-bold text-blue-700 text-base">{stats.collegati}</div>
              <div className="text-xs text-blue-600 font-medium">Collegati</div>
            </div>
          </div>

          {/* Certification status */}
          <div className="flex items-center space-x-2 bg-purple-50 p-2 rounded-lg">
            <Package className="h-4 w-4 text-purple-600 flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-bold text-purple-700 text-base">{stats.certificati}</div>
              <div className="text-xs text-purple-600 font-medium">Certificati</div>
            </div>
          </div>

          {/* Meters progress */}
          <div className="flex items-center space-x-2 bg-indigo-50 p-2 rounded-lg md:col-span-2">
            <div className="h-4 w-4 flex items-center justify-center flex-shrink-0">
              <div className="h-2.5 w-2.5 bg-indigo-600 rounded-full"></div>
            </div>
            <div className="min-w-0">
              <div className="font-bold text-indigo-700 text-base">
                {stats.metriInstallati.toLocaleString()}m
              </div>
              <div className="text-xs text-indigo-600 font-medium">
                di {stats.metriTotali.toLocaleString()}m ({stats.percentualeInstallazione.toFixed(1)}%)
              </div>
            </div>
          </div>

          {/* Connection details - only show if there are partial/non-connected cables */}
          {stats.parzialmenteCollegati > 0 && (
            <div className="flex items-center space-x-2 bg-yellow-50 p-2 rounded-lg">
              <div className="h-4 w-4 flex items-center justify-center flex-shrink-0">
                <div className="h-2.5 w-2.5 bg-yellow-500 rounded-full"></div>
              </div>
              <div className="min-w-0">
                <div className="font-bold text-yellow-700 text-base">{stats.parzialmenteCollegati}</div>
                <div className="text-xs text-yellow-600 font-medium">Parziali</div>
              </div>
            </div>
          )}

          {stats.nonCollegati > 0 && (
            <div className="flex items-center space-x-2 bg-red-50 p-2 rounded-lg">
              <div className="h-4 w-4 flex items-center justify-center flex-shrink-0">
                <div className="h-2.5 w-2.5 bg-red-500 rounded-full"></div>
              </div>
              <div className="min-w-0">
                <div className="font-bold text-red-700 text-base">{stats.nonCollegati}</div>
                <div className="text-xs text-red-600 font-medium">Non collegati</div>
              </div>
            </div>
          )}

        </div>

        {/* Progress bar */}
        {stats.metriTotali > 0 && (
          <div className="mt-4 bg-gray-50 p-3 rounded-lg">
            <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
              <span>Progresso installazione</span>
              <span className="text-mariner-700">{stats.percentualeInstallazione.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-mariner-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
