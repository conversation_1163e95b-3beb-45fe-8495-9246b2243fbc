'use client'

import { useMemo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Cable, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Zap,
  Package,
  BarChart3
} from 'lucide-react'
import { Cavo } from '@/types'

interface CaviStatisticsProps {
  cavi: Cavo[]
  filteredCavi: Cavo[]
  className?: string
  revisioneCorrente?: string
}

export default function CaviStatistics({
  cavi,
  filteredCavi,
  className,
  revisioneCorrente
}: CaviStatisticsProps) {
  const stats = useMemo(() => {
    const totalCavi = cavi.length
    const filteredCount = filteredCavi.length
    
    // Installation status
    const installati = filteredCavi.filter(c => 
      c.stato_installazione === 'Installato' || 
      (c.metri_posati && c.metri_posati > 0) ||
      (c.metratura_reale && c.metratura_reale > 0)
    ).length
    
    const inCorso = filteredCavi.filter(c => 
      c.stato_installazione === 'In corso'
    ).length
    
    const daInstallare = filteredCount - installati - inCorso
    
    // Connection status
    const collegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 3 // Both sides connected
    }).length
    
    const parzialmenteCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 1 || collegamento === 2 // One side connected
    }).length
    
    const nonCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)
    }).length
    
    // Certification status
    const certificati = filteredCavi.filter(c => 
      c.certificato === true || 
      c.certificato === 'SI' || 
      c.certificato === 'CERTIFICATO'
    ).length
    
    // Meters calculation
    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = filteredCavi.reduce((sum, c) => {
      const metri = c.metri_posati || c.metratura_reale || 0
      return sum + metri
    }, 0)
    
    const percentualeInstallazione = metriTotali > 0 ? (metriInstallati / metriTotali * 100) : 0
    
    return {
      totalCavi,
      filteredCount,
      installati,
      inCorso,
      daInstallare,
      collegati,
      parzialmenteCollegati,
      nonCollegati,
      certificati,
      metriTotali,
      metriInstallati,
      percentualeInstallazione
    }
  }, [cavi, filteredCavi])

  return (
    <Card className={className}>
      <CardContent className="p-3">
        {/* Header with revision */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4 text-mariner-600" />
            <span className="text-sm font-medium text-mariner-900">Statistiche Cavi</span>
          </div>
          {revisioneCorrente && (
            <Badge variant="outline" className="text-xs">
              Rev. {revisioneCorrente}
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-8 gap-3 text-sm">

          {/* Total cables */}
          <div className="flex items-center space-x-1.5">
            <Cable className="h-3.5 w-3.5 text-mariner-600" />
            <div>
              <div className="font-semibold text-mariner-900 text-sm">{stats.filteredCount}</div>
              <div className="text-xs text-muted-foreground leading-tight">
                di {stats.totalCavi}
              </div>
            </div>
          </div>

          {/* Installation status */}
          <div className="flex items-center space-x-1.5">
            <CheckCircle className="h-3.5 w-3.5 text-green-600" />
            <div>
              <div className="font-semibold text-green-700 text-sm">{stats.installati}</div>
              <div className="text-xs text-muted-foreground leading-tight">Installati</div>
            </div>
          </div>

          <div className="flex items-center space-x-1.5">
            <Clock className="h-3.5 w-3.5 text-yellow-600" />
            <div>
              <div className="font-semibold text-yellow-700 text-sm">{stats.inCorso}</div>
              <div className="text-xs text-muted-foreground leading-tight">In corso</div>
            </div>
          </div>

          <div className="flex items-center space-x-1.5">
            <AlertTriangle className="h-3.5 w-3.5 text-gray-600" />
            <div>
              <div className="font-semibold text-gray-700 text-sm">{stats.daInstallare}</div>
              <div className="text-xs text-muted-foreground leading-tight">Da installare</div>
            </div>
          </div>

          {/* Connection status */}
          <div className="flex items-center space-x-1.5">
            <Zap className="h-3.5 w-3.5 text-blue-600" />
            <div>
              <div className="font-semibold text-blue-700 text-sm">{stats.collegati}</div>
              <div className="text-xs text-muted-foreground leading-tight">Collegati</div>
            </div>
          </div>

          {/* Certification status */}
          <div className="flex items-center space-x-1.5">
            <Package className="h-3.5 w-3.5 text-purple-600" />
            <div>
              <div className="font-semibold text-purple-700 text-sm">{stats.certificati}</div>
              <div className="text-xs text-muted-foreground leading-tight">Certificati</div>
            </div>
          </div>

          {/* Meters progress */}
          <div className="flex items-center space-x-1.5 md:col-span-2">
            <div className="h-3.5 w-3.5 flex items-center justify-center">
              <div className="h-2 w-2 bg-indigo-600 rounded-full"></div>
            </div>
            <div>
              <div className="font-semibold text-indigo-700 text-sm">
                {stats.metriInstallati.toLocaleString()}m
              </div>
              <div className="text-xs text-muted-foreground leading-tight">
                di {stats.metriTotali.toLocaleString()}m ({stats.percentualeInstallazione.toFixed(1)}%)
              </div>
            </div>
          </div>

          {/* Connection details - only show if there are partial/non-connected cables */}
          {stats.parzialmenteCollegati > 0 && (
            <div className="flex items-center space-x-1.5">
              <div className="h-3.5 w-3.5 flex items-center justify-center">
                <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
              </div>
              <div>
                <div className="font-semibold text-yellow-700 text-sm">{stats.parzialmenteCollegati}</div>
                <div className="text-xs text-muted-foreground leading-tight">Parziali</div>
              </div>
            </div>
          )}

          {stats.nonCollegati > 0 && (
            <div className="flex items-center space-x-1.5">
              <div className="h-3.5 w-3.5 flex items-center justify-center">
                <div className="h-2 w-2 bg-red-500 rounded-full"></div>
              </div>
              <div>
                <div className="font-semibold text-red-700 text-sm">{stats.nonCollegati}</div>
                <div className="text-xs text-muted-foreground leading-tight">Non collegati</div>
              </div>
            </div>
          )}

        </div>

        {/* Compact progress bar */}
        {stats.metriTotali > 0 && (
          <div className="mt-2">
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>Progresso installazione</span>
              <span>{stats.percentualeInstallazione.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-mariner-600 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
