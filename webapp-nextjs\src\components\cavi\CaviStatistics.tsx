'use client'

import { useMemo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Cable, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Zap,
  Package,
  BarChart3
} from 'lucide-react'
import { Cavo } from '@/types'

interface CaviStatisticsProps {
  cavi: Cavo[]
  filteredCavi: Cavo[]
  className?: string
}

export default function CaviStatistics({
  cavi,
  filteredCavi,
  className
}: CaviStatisticsProps) {
  const stats = useMemo(() => {
    const totalCavi = cavi.length
    const filteredCount = filteredCavi.length
    
    // Installation status
    const installati = filteredCavi.filter(c => 
      c.stato_installazione === 'Installato' || 
      (c.metri_posati && c.metri_posati > 0) ||
      (c.metratura_reale && c.metratura_reale > 0)
    ).length
    
    const inCorso = filteredCavi.filter(c => 
      c.stato_installazione === 'In corso'
    ).length
    
    const daInstallare = filteredCount - installati - inCorso
    
    // Connection status
    const collegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 3 // Both sides connected
    }).length
    
    const parzialmenteCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 1 || collegamento === 2 // One side connected
    }).length
    
    const nonCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)
    }).length
    
    // Certification status
    const certificati = filteredCavi.filter(c => 
      c.certificato === true || 
      c.certificato === 'SI' || 
      c.certificato === 'CERTIFICATO'
    ).length
    
    // Meters calculation
    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = filteredCavi.reduce((sum, c) => {
      const metri = c.metri_posati || c.metratura_reale || 0
      return sum + metri
    }, 0)
    
    const percentualeInstallazione = metriTotali > 0 ? (metriInstallati / metriTotali * 100) : 0
    
    return {
      totalCavi,
      filteredCount,
      installati,
      inCorso,
      daInstallare,
      collegati,
      parzialmenteCollegati,
      nonCollegati,
      certificati,
      metriTotali,
      metriInstallati,
      percentualeInstallazione
    }
  }, [cavi, filteredCavi])

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm">
          
          {/* Total cables */}
          <div className="flex items-center space-x-2">
            <Cable className="h-4 w-4 text-mariner-600" />
            <div>
              <div className="font-semibold text-mariner-900">{stats.filteredCount}</div>
              <div className="text-xs text-muted-foreground">
                di {stats.totalCavi} cavi
              </div>
            </div>
          </div>

          {/* Installation status */}
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <div>
              <div className="font-semibold text-green-700">{stats.installati}</div>
              <div className="text-xs text-muted-foreground">Installati</div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-yellow-600" />
            <div>
              <div className="font-semibold text-yellow-700">{stats.inCorso}</div>
              <div className="text-xs text-muted-foreground">In corso</div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-gray-600" />
            <div>
              <div className="font-semibold text-gray-700">{stats.daInstallare}</div>
              <div className="text-xs text-muted-foreground">Da installare</div>
            </div>
          </div>

          {/* Connection status */}
          <div className="flex items-center space-x-2">
            <Zap className="h-4 w-4 text-blue-600" />
            <div>
              <div className="font-semibold text-blue-700">{stats.collegati}</div>
              <div className="text-xs text-muted-foreground">Collegati</div>
            </div>
          </div>

          {/* Certification status */}
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 text-purple-600" />
            <div>
              <div className="font-semibold text-purple-700">{stats.certificati}</div>
              <div className="text-xs text-muted-foreground">Certificati</div>
            </div>
          </div>

          {/* Meters progress */}
          <div className="flex items-center space-x-2 md:col-span-2">
            <BarChart3 className="h-4 w-4 text-indigo-600" />
            <div>
              <div className="font-semibold text-indigo-700">
                {stats.metriInstallati.toLocaleString()}m
              </div>
              <div className="text-xs text-muted-foreground">
                di {stats.metriTotali.toLocaleString()}m ({stats.percentualeInstallazione.toFixed(1)}%)
              </div>
            </div>
          </div>

          {/* Connection details */}
          {stats.parzialmenteCollegati > 0 && (
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 flex items-center justify-center">
                <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
              </div>
              <div>
                <div className="font-semibold text-yellow-700">{stats.parzialmenteCollegati}</div>
                <div className="text-xs text-muted-foreground">Parziali</div>
              </div>
            </div>
          )}

          {stats.nonCollegati > 0 && (
            <div className="flex items-center space-x-2">
              <div className="h-4 w-4 flex items-center justify-center">
                <div className="h-2 w-2 bg-red-500 rounded-full"></div>
              </div>
              <div>
                <div className="font-semibold text-red-700">{stats.nonCollegati}</div>
                <div className="text-xs text-muted-foreground">Non collegati</div>
              </div>
            </div>
          )}

        </div>

        {/* Progress bar */}
        {stats.metriTotali > 0 && (
          <div className="mt-3">
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>Progresso installazione</span>
              <span>{stats.percentualeInstallazione.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-mariner-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
