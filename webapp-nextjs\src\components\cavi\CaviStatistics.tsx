'use client'

import { useMemo } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Cable, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Zap,
  Package,
  BarChart3
} from 'lucide-react'
import { Cavo } from '@/types'

interface CaviStatisticsProps {
  cavi: Cavo[]
  filteredCavi: Cavo[]
  className?: string
  revisioneCorrente?: string
}

export default function CaviStatistics({
  cavi,
  filteredCavi,
  className,
  revisioneCorrente
}: CaviStatisticsProps) {
  const stats = useMemo(() => {
    const totalCavi = cavi.length
    const filteredCount = filteredCavi.length
    
    // Installation status
    const installati = filteredCavi.filter(c => 
      c.stato_installazione === 'Installato' || 
      (c.metri_posati && c.metri_posati > 0) ||
      (c.metratura_reale && c.metratura_reale > 0)
    ).length
    
    const inCorso = filteredCavi.filter(c => 
      c.stato_installazione === 'In corso'
    ).length
    
    const daInstallare = filteredCount - installati - inCorso
    
    // Connection status
    const collegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 3 // Both sides connected
    }).length
    
    const parzialmenteCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 1 || collegamento === 2 // One side connected
    }).length
    
    const nonCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)
    }).length
    
    // Certification status
    const certificati = filteredCavi.filter(c => 
      c.certificato === true || 
      c.certificato === 'SI' || 
      c.certificato === 'CERTIFICATO'
    ).length
    
    // Meters calculation
    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = filteredCavi.reduce((sum, c) => {
      const metri = c.metri_posati || c.metratura_reale || 0
      return sum + metri
    }, 0)

    // Calcolo IAP (Indice di Avanzamento Ponderato) come nella webapp originale
    const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {
      // Pesi per le fasi del progetto
      const Wp = 2.0  // Peso fase Posa
      const Wc = 1.5  // Peso fase Collegamento
      const Wz = 0.5  // Peso fase Certificazione

      // Se non ci sono cavi, ritorna 0
      if (nTot === 0) return 0

      // Calcolo del numeratore (Sforzo Completato)
      const sforzoSoloInstallati = (nInst - nColl) * Wp
      const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)
      const sforzoCertificati = nCert * (Wp + Wc + Wz)
      const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati

      // Calcolo del denominatore (Sforzo Massimo Previsto)
      const denominatore = nTot * (Wp + Wc + Wz)

      // Calcolo finale dell'IAP in percentuale
      const iap = (numeratore / denominatore) * 100

      return Math.round(iap * 100) / 100 // Arrotonda a 2 decimali
    }

    const percentualeInstallazione = calculateIAP(filteredCount, installati, collegati, certificati)
    
    return {
      totalCavi,
      filteredCount,
      installati,
      inCorso,
      daInstallare,
      collegati,
      parzialmenteCollegati,
      nonCollegati,
      certificati,
      metriTotali,
      metriInstallati,
      percentualeInstallazione
    }
  }, [cavi, filteredCavi])

  return (
    <Card className={className}>
      <CardContent className="p-4">
        {/* Header with revision */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4 text-mariner-600" />
            <span className="text-sm font-semibold text-mariner-900">Statistiche Cavi</span>
          </div>
          {revisioneCorrente && (
            <Badge variant="outline" className="text-xs font-medium">
              Rev. {revisioneCorrente}
            </Badge>
          )}
        </div>

        {/* Statistics distributed across full width */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">

          {/* Total cables */}
          <div className="flex items-center space-x-2 bg-mariner-50 px-4 py-3 rounded-lg">
            <Cable className="h-5 w-5 text-mariner-600" />
            <div>
              <div className="font-bold text-mariner-900 text-lg">{stats.filteredCount}</div>
              <div className="text-sm text-mariner-600">di {stats.totalCavi} cavi</div>
            </div>
          </div>

          {/* Installation status */}
          <div className="flex items-center space-x-2 bg-green-50 px-4 py-3 rounded-lg">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <div className="font-bold text-green-700 text-lg">{stats.installati}</div>
              <div className="text-sm text-green-600">installati</div>
            </div>
          </div>

          <div className="flex items-center space-x-2 bg-yellow-50 px-4 py-3 rounded-lg">
            <Clock className="h-5 w-5 text-yellow-600" />
            <div>
              <div className="font-bold text-yellow-700 text-lg">{stats.inCorso}</div>
              <div className="text-sm text-yellow-600">in corso</div>
            </div>
          </div>

          <div className="flex items-center space-x-2 bg-gray-50 px-4 py-3 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-gray-600" />
            <div>
              <div className="font-bold text-gray-700 text-lg">{stats.daInstallare}</div>
              <div className="text-sm text-gray-600">da installare</div>
            </div>
          </div>

          {/* Connection status */}
          <div className="flex items-center space-x-2 bg-blue-50 px-4 py-3 rounded-lg">
            <Zap className="h-5 w-5 text-blue-600" />
            <div>
              <div className="font-bold text-blue-700 text-lg">{stats.collegati}</div>
              <div className="text-sm text-blue-600">collegati</div>
            </div>
          </div>

          {/* Certification status */}
          <div className="flex items-center space-x-2 bg-purple-50 px-4 py-3 rounded-lg">
            <Package className="h-5 w-5 text-purple-600" />
            <div>
              <div className="font-bold text-purple-700 text-lg">{stats.certificati}</div>
              <div className="text-sm text-purple-600">certificati</div>
            </div>
          </div>

          {/* Meters progress */}
          <div className="flex items-center space-x-2 bg-indigo-50 px-4 py-3 rounded-lg">
            <div className="h-5 w-5 flex items-center justify-center">
              <div className="h-3 w-3 bg-indigo-600 rounded-full"></div>
            </div>
            <div>
              <div className="font-bold text-indigo-700 text-lg">{stats.metriInstallati.toLocaleString()}m</div>
              <div className="text-sm text-indigo-600">di {stats.metriTotali.toLocaleString()}m</div>
            </div>
          </div>

        </div>

        {/* IAP Progress bar - Dynamic colors */}
        {stats.filteredCount > 0 && (
          <div className="mt-4 bg-gray-50 p-3 rounded-lg">
            <div className="flex justify-between text-sm font-medium text-gray-700 mb-2">
              <span>IAP - Indice Avanzamento Ponderato</span>
              <span className={`font-bold ${
                stats.percentualeInstallazione >= 80 ? 'text-green-700' :
                stats.percentualeInstallazione >= 50 ? 'text-yellow-700' :
                stats.percentualeInstallazione >= 25 ? 'text-orange-700' : 'text-red-700'
              }`}>
                {stats.percentualeInstallazione.toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full transition-all duration-500 ease-in-out ${
                  stats.percentualeInstallazione >= 80 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                  stats.percentualeInstallazione >= 50 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                  stats.percentualeInstallazione >= 25 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :
                  'bg-gradient-to-r from-red-500 to-red-600'
                }`}
                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)</span>
              <span>{stats.installati}I + {stats.collegati}C + {stats.certificati}Cert</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
