(()=>{var t={};t.id=22,t.ids=[22],t.modules={22:(t,e,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),o=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),o=r(59467);t.exports=function(t,e){return null!=t&&o(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),o=r(55048);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7383:(t,e,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),o=r(52931),i=r(32269);t.exports=function(t){return i(t)?n(t):o(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10034:(t,e,r)=>{var n=r(2984),o=r(22),i=r(46063);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},10090:(t,e,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;t.exports=a?o(a):n},10653:(t,e,r)=>{var n=r(21456),o=r(63979),i=r(7651);t.exports=function(t){return n(t,i,o)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11273:(t,e,r)=>{"use strict";r.d(e,{A:()=>a,q:()=>i});var n=r(43210),o=r(60687);function i(t,e){let r=n.createContext(e),i=t=>{let{children:e,...i}=t,a=n.useMemo(()=>i,Object.values(i));return(0,o.jsx)(r.Provider,{value:a,children:e})};return i.displayName=t+"Provider",[i,function(o){let i=n.useContext(r);if(i)return i;if(void 0!==e)return e;throw Error(`\`${o}\` must be used within \`${t}\``)}]}function a(t,e=[]){let r=[],i=()=>{let e=r.map(t=>n.createContext(t));return function(r){let o=r?.[t]||e;return n.useMemo(()=>({[`__scope${t}`]:{...r,[t]:o}}),[r,o])}};return i.scopeName=t,[function(e,i){let a=n.createContext(i),c=r.length;r=[...r,i];let u=e=>{let{scope:r,children:i,...u}=e,l=r?.[t]?.[c]||a,s=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(l.Provider,{value:s,children:i})};return u.displayName=e+"Provider",[u,function(r,o){let u=o?.[t]?.[c]||a,l=n.useContext(u);if(l)return l;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let r=()=>{let r=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let o=r.reduce((e,{useScope:r,scopeName:n})=>{let o=r(t)[`__scope${n}`];return{...e,...o}},{});return n.useMemo(()=>({[`__scope${e.scopeName}`]:o}),[o])}};return r.scopeName=e.scopeName,r}(i,...e)]}},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},12412:t=>{"use strict";t.exports=require("assert")},14163:(t,e,r)=>{"use strict";r.d(e,{hO:()=>u,sG:()=>c});var n=r(43210),o=r(51215),i=r(8730),a=r(60687),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let r=(0,i.TL)(`Primitive.${e}`),o=n.forwardRef((t,n)=>{let{asChild:o,...i}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:e,{...i,ref:n})});return o.displayName=`Primitive.${e}`,{...t,[e]:o}},{});function u(t,e){t&&o.flushSync(()=>t.dispatchEvent(e))}},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),o=r(27467);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),o=r(46063),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},15909:(t,e,r)=>{var n=r(87506),o=r(66930),i=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},20540:(t,e,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},20623:(t,e,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},21456:(t,e,r)=>{var n=r(41693),o=r(40542);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},21592:(t,e,r)=>{var n=r(42205),o=r(61837);t.exports=function(t,e){return n(o(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},21820:t=>{"use strict";t.exports=require("os")},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),o=r(32269),i=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},25541:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27006:(t,e,r)=>{var n=r(46328),o=r(99525),i=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},28837:(t,e,r)=>{var n=r(57797),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},28947:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},28977:(t,e,r)=>{var n=r(11539),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29021:t=>{"use strict";t.exports=require("fs")},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),o=r(658),i=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),o=r(69619);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},35163:(t,e,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),o=r(92662);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=w&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},38404:(t,e,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40228:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},41547:(t,e,r)=>{var n=r(61548),o=r(90851);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),o=r(85450);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},43641:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>yb});var n={};r.r(n),r.d(n,{scaleBand:()=>nk,scaleDiverging:()=>function t(){var e=iy(cT()(o4));return e.copy=function(){return ck(e,t())},nw.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=iS(cT()).domain([.1,1,10]);return e.copy=function(){return ck(e,t()).base(e.base())},nw.apply(e,arguments)},scaleDivergingPow:()=>cC,scaleDivergingSqrt:()=>cN,scaleDivergingSymlog:()=>function t(){var e=iE(cT());return e.copy=function(){return ck(e,t()).constant(e.constant())},nw.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,o5),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,o5):[0,1],iy(n)},scaleImplicit:()=>nA,scaleLinear:()=>iv,scaleLog:()=>function t(){let e=iS(it()).domain([1,10]);return e.copy=()=>o9(e,t()).base(e.base()),nO.apply(e,arguments),e},scaleOrdinal:()=>nE,scalePoint:()=>nM,scalePow:()=>iC,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=op){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[od(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(ou),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nO.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[od(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nO.apply(iy(c),arguments)},scaleRadial:()=>function t(){var e,r=ie(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iI(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,o5)).map(iI)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nO.apply(i,arguments),iy(i)},scaleSequential:()=>function t(){var e=iy(cE()(o4));return e.copy=function(){return ck(e,t())},nw.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=iS(cE()).domain([1,10]);return e.copy=function(){return ck(e,t()).base(e.base())},nw.apply(e,arguments)},scaleSequentialPow:()=>cM,scaleSequentialQuantile:()=>function t(){var e=[],r=o4;function n(t){if(null!=t&&!isNaN(t*=1))return r((od(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(ou),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return iB(t);if(e>=1)return iD(t);var n,o=(n-1)*e,i=Math.floor(o),a=iD((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?iR:function(t=ou){if(t===ou)return iR;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iL(e,n,r),i(e[o],a)>0&&iL(e,n,o);c<u;){for(iL(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iL(e,n,u):iL(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(iB(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nw.apply(n,arguments)},scaleSequentialSqrt:()=>c_,scaleSequentialSymlog:()=>function t(){var e=iE(cE());return e.copy=function(){return ck(e,t()).constant(e.constant())},nw.apply(e,arguments)},scaleSqrt:()=>iN,scaleSymlog:()=>function t(){var e=iE(it());return e.copy=function(){return o9(e,t()).constant(e.constant())},nO.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[od(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},nO.apply(i,arguments)},scaleTime:()=>cP,scaleUtc:()=>cA,tickFormat:()=>id});var o=r(60687),i=r(43210),a=r.n(i),c=r(44493),u=r(29523),l=r(46657),s=r(63213),f=r(31158),p=r(41862),h=r(93613),d=r(28947),y=r(25541),v=r(58559),m=r(5336),b=r(48730),g=r(40228),x=r(49384),O=r(45603),w=r.n(O),j=r(63866),S=r.n(j),P=r(77822),A=r.n(P),E=r(40491),k=r.n(E),M=r(93490),_=r.n(M),T=function(t){return 0===t?0:t>0?1:-1},C=function(t){return S()(t)&&t.indexOf("%")===t.length-1},N=function(t){return _()(t)&&!A()(t)},I=function(t){return N(t)||S()(t)},D=0,B=function(t){var e=++D;return"".concat(t||"").concat(e)},R=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!N(t)&&!S()(t))return n;if(C(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return A()(r)&&(r=n),o&&r>e&&(r=e),r},L=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},z=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},U=function(t,e){return N(t)&&N(e)?function(r){return t+r*(e-t)}:function(){return e}};function $(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):k()(t,e))===r}):null}var F=function(t,e){return N(t)&&N(e)?t-e:S()(t)&&S()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},q=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},W=r(37456),X=r.n(W),H=r(5231),G=r.n(H),V=r(55048),K=r.n(V),Y=r(93780);function Z(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function J(t){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Q=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],tt=["points","pathLength"],te={svg:["viewBox","children"],polygon:tt,polyline:tt},tr=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],tn=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,i.isValidElement)(t)&&(r=t.props),!K()(r))return null;var n={};return Object.keys(r).forEach(function(t){tr.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},to=function(t,e,r){if(!K()(t)||"object"!==J(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];tr.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},ti=["children"],ta=["children"];function tc(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var tu={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tl=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},ts=null,tf=null,tp=function t(e){if(e===ts&&Array.isArray(tf))return tf;var r=[];return i.Children.forEach(e,function(e){X()(e)||((0,Y.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tf=r,ts=e,r};function th(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tl(t)}):[tl(e)],tp(t).forEach(function(t){var e=k()(t,"type.displayName")||k()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function td(t,e){var r=th(t,e);return r&&r[0]}var ty=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!N(r)&&!(r<=0)&&!!N(n)&&!(n<=0)},tv=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tm=function(t,e,r,n){var o,i=null!=(o=null==te?void 0:te[n])?o:[];return e.startsWith("data-")||!G()(t)&&(n&&i.includes(e)||Q.includes(e))||r&&tr.includes(e)},tb=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,i.isValidElement)(t)&&(n=t.props),!K()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;tm(null==(i=n)?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},tg=function t(e,r){if(e===r)return!0;var n=i.Children.count(e);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tx(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=e[o],c=r[o];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tx(a,c))return!1}return!0},tx=function(t,e){if(X()(t)&&X()(e))return!0;if(!X()(t)&&!X()(e)){var r=t.props||{},n=r.children,o=tc(r,ti),i=e.props||{},a=i.children,c=tc(i,ta);if(n&&a)return Z(o,c)&&tg(n,a);if(!n&&!a)return Z(o,c)}return!1},tO=function(t,e){var r=[],n={};return tp(t).forEach(function(t,o){var i;if((i=t)&&i.type&&S()(i.type)&&tv.indexOf(i.type)>=0)r.push(t);else if(t){var a=tl(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,o);r.push(s),n[a]=!0}}}),r},tw=function(t){var e=t&&t.type;return e&&tu[e]?tu[e]:null};function tj(t){return(tj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tS(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tj(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tE=(0,i.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,c=void 0===o?{width:-1,height:-1}:o,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,h=void 0===p?0:p,d=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,O=t.className,j=t.onResize,S=t.style,P=(0,i.useRef)(null),A=(0,i.useRef)();A.current=j,(0,i.useImperativeHandle)(e,function(){return Object.defineProperty(P.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),P.current},configurable:!0})});var E=function(t){if(Array.isArray(t))return t}(r=(0,i.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tA(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tA(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),k=E[0],M=E[1],_=(0,i.useCallback)(function(t,e){M(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;_(n,o),null==(e=A.current)||e.call(A,n,o)};b>0&&(t=w()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=P.current.getBoundingClientRect();return _(r.width,r.height),e.observe(P.current),function(){e.disconnect()}},[_,b]);var T=(0,i.useMemo)(function(){var t=k.containerWidth,e=k.containerHeight;if(t<0||e<0)return null;q(C(l)||C(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),q(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=C(l)?t:l,o=C(f)?e:f;n&&n>0&&(r?o=r/n:o&&(r=o*n),y&&o>y&&(o=y)),q(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,l,f,h,d,n);var c=!Array.isArray(v)&&tl(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,i.cloneElement)(t,tP({width:r,height:o},c?{style:tP({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,d,h,k,l]);return a().createElement("div",{id:g?"".concat(g):void 0,className:(0,x.A)("recharts-responsive-container",O),style:tP(tP({},void 0===S?{}:S),{},{width:l,height:f,minWidth:h,minHeight:d,maxHeight:y}),ref:P},T)}),tk=r(34990),tM=r.n(tk),t_=r(85938),tT=r.n(t_);function tC(t,e){if(!t)throw Error("Invariant failed")}var tN=["children","width","height","viewBox","className","style","title","desc"];function tI(){return(tI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tD(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,c=t.style,u=t.title,l=t.desc,s=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tN),f=o||{width:r,height:n,x:0,y:0},p=(0,x.A)("recharts-surface",i);return a().createElement("svg",tI({},tb(s,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,l),e)}var tB=["children","className"];function tR(){return(tR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tL=a().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tB),i=(0,x.A)("recharts-layer",n);return a().createElement("g",tR({className:i},tb(o,!0),{ref:e}),r)});function tz(t){return(tz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tU(){return(tU=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t$(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tF(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tz(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tW(t){return Array.isArray(t)&&I(t[0])&&I(t[1])?t.join(" ~ "):t}var tX=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,c=t.labelStyle,u=t.payload,l=t.formatter,s=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,h=t.label,d=t.labelFormatter,y=t.accessibilityLayer,v=tq({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=tq({margin:0},void 0===c?{}:c),b=!X()(h),g=b?h:"",O=(0,x.A)("recharts-default-tooltip",f),w=(0,x.A)("recharts-tooltip-label",p);return b&&d&&null!=u&&(g=d(h,u)),a().createElement("div",tU({className:O,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:m},a().isValidElement(g)?g:"".concat(g)),function(){if(u&&u.length){var t=(s?tT()(u,s):u).map(function(t,e){if("none"===t.type)return null;var n=tq({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||l||tW,c=t.value,s=t.name,f=c,p=s;if(o&&null!=f&&null!=p){var h=o(c,s,t,e,u);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return t$(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return t$(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},I(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,I(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tH(t){return(tH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tG(t,e,r){var n;return(n=function(t,e){if("object"!=tH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tH(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tV="recharts-tooltip-wrapper",tK={visibility:"hidden"};function tY(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&N(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function tZ(t){return(tZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tQ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tJ(Object(r),!0).forEach(function(e){t5(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t0(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t0=function(){return!!t})()}function t1(t){return(t1=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t2(t,e){return(t2=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t5(t,e,r){return(e=t3(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t3(t){var e=function(t,e){if("object"!=tZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tZ(e)?e:e+""}var t4=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=t1(e),t5(t=function(t,e){if(e&&("object"===tZ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,t0()?Reflect.construct(e,n||[],t1(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),t5(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(o=null==(i=t.props.coordinate)?void 0:i.y)?o:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&t2(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,c,u,l,s,f,p,h,d,y,v,m,b,g,O=this,w=this.props,j=w.active,S=w.allowEscapeViewBox,P=w.animationDuration,A=w.animationEasing,E=w.children,k=w.coordinate,M=w.hasPayload,_=w.isAnimationActive,T=w.offset,C=w.position,I=w.reverseDirection,D=w.useTranslate3d,B=w.viewBox,R=w.wrapperStyle,L=(p=(t={allowEscapeViewBox:S,coordinate:k,offsetTopLeft:T,position:C,reverseDirection:I,tooltipBox:this.state.lastBoundingBox,useTranslate3d:D,viewBox:B}).allowEscapeViewBox,h=t.coordinate,d=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&h?(r=(e={translateX:s=tY({allowEscapeViewBox:p,coordinate:h,key:"x",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=tY({allowEscapeViewBox:p,coordinate:h,key:"y",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,l={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=tK,{cssProperties:l,cssClasses:(i=(o={translateX:s,translateY:f,coordinate:h}).coordinate,c=o.translateX,u=o.translateY,(0,x.A)(tV,tG(tG(tG(tG({},"".concat(tV,"-right"),N(c)&&i&&N(i.x)&&c>=i.x),"".concat(tV,"-left"),N(c)&&i&&N(i.x)&&c<i.x),"".concat(tV,"-bottom"),N(u)&&i&&N(i.y)&&u>=i.y),"".concat(tV,"-top"),N(u)&&i&&N(i.y)&&u<i.y)))}),z=L.cssClasses,U=L.cssProperties,$=tQ(tQ({transition:_&&j?"transform ".concat(P,"ms ").concat(A):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&j&&M?"visible":"hidden",position:"absolute",top:0,left:0},R);return a().createElement("div",{tabIndex:-1,className:z,style:$,ref:function(t){O.wrapperNode=t}},E)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t3(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent),t6={isSsr:!0,get:function(t){return t6[t]},set:function(t,e){if("string"==typeof t)t6[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){t6[e]=t[e]})}}},t8=r(36315),t7=r.n(t8);function t9(t,e,r){return!0===e?t7()(t,r):G()(e)?t7()(t,e):t}function et(t){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ee(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function er(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ee(Object(r),!0).forEach(function(e){ea(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ee(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function en(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(en=function(){return!!t})()}function eo(t){return(eo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ei(t,e){return(ei=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ea(t,e,r){return(e=ec(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ec(t){var e=function(t,e){if("object"!=et(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=et(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==et(e)?e:e+""}function eu(t){return t.dataKey}var el=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=eo(t),function(t,e){if(e&&("object"===et(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,en()?Reflect.construct(t,e||[],eo(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ei(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=t9(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,eu));var O=x.length>0;return a().createElement(t4,{allowEscapeViewBox:o,animationDuration:i,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:O,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=er(er({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(tX,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ec(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);ea(el,"displayName","Tooltip"),ea(el,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!t6.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var es=r(69433),ef=r.n(es);let ep=Math.cos,eh=Math.sin,ed=Math.sqrt,ey=Math.PI,ev=2*ey,em={draw(t,e){let r=ed(e/ey);t.moveTo(r,0),t.arc(0,0,r,0,ev)}},eb=ed(1/3),eg=2*eb,ex=eh(ey/10)/eh(7*ey/10),eO=eh(ev/10)*ex,ew=-ep(ev/10)*ex,ej=ed(3),eS=ed(3)/2,eP=1/ed(12),eA=(eP/2+1)*3;function eE(t){return function(){return t}}let ek=Math.PI,eM=2*ek,e_=eM-1e-6;function eT(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eC{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eT:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eT;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t*=1,e*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=o*Math.tan((ek-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,o,i){if(t*=1,e*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%eM+eM),f>e_?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ek)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eN(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eC(e)}function eI(t){return(eI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eC.prototype,ed(3),ed(3);var eD=["type","size","sizeType"];function eB(){return(eB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eR(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=eI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eI(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var ez={symbolCircle:em,symbolCross:{draw(t,e){let r=ed(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=ed(e/eg),n=r*eb;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=ed(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=ed(.8908130915292852*e),n=eO*r,o=ew*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=ev*e/5,a=ep(i),c=eh(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-ed(e/(3*ej));t.moveTo(0,2*r),t.lineTo(-ej*r,-r),t.lineTo(ej*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=ed(e/eA),n=r/2,o=r*eP,i=r*eP+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-eS*o,eS*n+-.5*o),t.lineTo(-.5*n-eS*i,eS*n+-.5*i),t.lineTo(-.5*a-eS*i,eS*a+-.5*i),t.lineTo(-.5*n+eS*o,-.5*o-eS*n),t.lineTo(-.5*n+eS*i,-.5*i-eS*n),t.lineTo(-.5*a+eS*i,-.5*i-eS*a),t.closePath()}}},eU=Math.PI/180,e$=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eU;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eF=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,c=t.sizeType,u=void 0===c?"area":c,l=eL(eL({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,eD)),{},{type:n,size:i,sizeType:u}),s=l.className,f=l.cx,p=l.cy,h=tb(l,!0);return f===+f&&p===+p&&i===+i?a().createElement("path",eB({},h,{className:(0,x.A)("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=ez["symbol".concat(ef()(n))]||em,(function(t,e){let r=null,n=eN(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:eE(t||em),e="function"==typeof e?e:eE(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:eE(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:eE(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(e$(i,u,n))())})):null};function eq(t){return(eq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eW(){return(eW=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eF.registerSymbol=function(t,e){ez["symbol".concat(ef()(t))]=e};function eH(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eH=function(){return!!t})()}function eG(t){return(eG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eV(t,e){return(eV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eK(t,e,r){return(e=eY(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eY(t){var e=function(t,e){if("object"!=eq(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eq(e)?e:e+""}var eZ=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=eG(t),function(t,e){if(e&&("object"===eq(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eH()?Reflect.construct(t,e||[],eG(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&eV(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eX(Object(r),!0).forEach(function(e){eK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,a().cloneElement(t.legendIcon,i)}return a().createElement(eF,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},l={display:"horizontal"===o?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,f=(0,x.A)(eK(eK({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=G()(e.value)?null:e.value;q(!G()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return a().createElement("li",eW({className:f,style:l,key:"legend-item-".concat(r)},to(t.props,e,r)),a().createElement(tD,{width:n,height:n,viewBox:u,style:s},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:h}},o?o(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eY(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i.PureComponent);function eJ(t){return(eJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eK(eZ,"displayName","Legend"),eK(eZ,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eQ=["ref"];function e0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function e1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e0(Object(r),!0).forEach(function(e){e6(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function e2(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e8(n.key),n)}}function e5(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e5=function(){return!!t})()}function e3(t){return(e3=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e4(t,e){return(e4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e6(t,e,r){return(e=e8(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e8(t){var e=function(t,e){if("object"!=eJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eJ(e)?e:e+""}function e7(t){return t.value}var e9=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=e3(e),e6(t=function(t,e){if(e&&("object"===eJ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,e5()?Reflect.construct(e,r||[],e3(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&e4(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?e1({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),e1(e1({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=e1(e1({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,eQ);return a().createElement(eZ,r)}(r,e1(e1({},this.props),{},{payload:t9(u,c,e7)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=e1(e1({},this.defaultProps),t.props).layout;return"vertical"===r&&N(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&e2(n.prototype,e),r&&e2(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function rt(){return(rt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}e6(e9,"displayName","Legend"),e6(e9,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var re=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,x.A)("recharts-dot",o);return e===+e&&r===+r&&n===+n?a().createElement("circle",rt({},tb(t,!1),tn(t),{className:i,cx:e,cy:r,r:n})):null},rr=r(87955),rn=r.n(rr),ro=Object.getOwnPropertyNames,ri=Object.getOwnPropertySymbols,ra=Object.prototype.hasOwnProperty;function rc(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function ru(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function rl(t){return ro(t).concat(ri(t))}var rs=Object.hasOwn||function(t,e){return ra.call(t,e)};function rf(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rp=Object.getOwnPropertyDescriptor,rh=Object.keys;function rd(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function ry(t,e){return rf(t.getTime(),e.getTime())}function rv(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rm(t,e){return t===e}function rb(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rg(t,e,r){var n=rh(t),o=n.length;if(rh(e).length!==o)return!1;for(;o-- >0;)if(!rA(t,e,r,n[o]))return!1;return!0}function rx(t,e,r){var n,o,i,a=rl(t),c=a.length;if(rl(e).length!==c)return!1;for(;c-- >0;)if(!rA(t,e,r,n=a[c])||(o=rp(t,n),i=rp(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rO(t,e){return rf(t.valueOf(),e.valueOf())}function rw(t,e){return t.source===e.source&&t.flags===e.flags}function rj(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rS(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rP(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rA(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rs(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rE=Array.isArray,rk="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rM=Object.assign,r_=Object.prototype.toString.call.bind(Object.prototype.toString),rT=rC();function rC(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?rx:rd,areDatesEqual:ry,areErrorsEqual:rv,areFunctionsEqual:rm,areMapsEqual:n?rc(rb,rx):rb,areNumbersEqual:rf,areObjectsEqual:n?rx:rg,arePrimitiveWrappersEqual:rO,areRegExpsEqual:rw,areSetsEqual:n?rc(rj,rx):rj,areTypedArraysEqual:n?rx:rS,areUrlsEqual:rP};if(r&&(o=rM({},o,r(o))),e){var i=ru(o.areArraysEqual),a=ru(o.areMapsEqual),c=ru(o.areObjectsEqual),u=ru(o.areSetsEqual);o=rM({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&i(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rE(t))return r(t,e,d);if(null!=rk&&rk(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=r_(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?o(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rN(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rI(t){return(rI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rB(t){return(rB="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rR(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rR(Object(r),!0).forEach(function(e){rz(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rR(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rz(t,e,r){var n;return(n=function(t,e){if("object"!==rB(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rB(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rB(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rC({strict:!0}),rC({circular:!0}),rC({circular:!0,strict:!0}),rC({createInternalComparator:function(){return rf}}),rC({strict:!0,createInternalComparator:function(){return rf}}),rC({circular:!0,createInternalComparator:function(){return rf}}),rC({circular:!0,createInternalComparator:function(){return rf},strict:!0});var rU=function(t){return t},r$=function(t,e){return Object.keys(e).reduce(function(r,n){return rL(rL({},r),{},rz({},n,t(n,e[n])))},{})},rF=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rq=function(t,e,r,n,o,i,a,c){};function rW(t,e){if(t){if("string"==typeof t)return rX(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rX(t,e)}}function rX(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rH=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rG=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rV=function(t,e){return function(r){return rG(rH(t,e),r)}},rK=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rW(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else rq(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rq([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rV(i,c),h=rV(a,u),d=(t=i,e=c,function(r){var n;return rG([].concat(function(t){if(Array.isArray(t))return rX(t)}(n=rH(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rW(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},rY=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rZ=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rK(n);case"spring":return rY();default:if("cubic-bezier"===n.split("(")[0])return rK(n);rq(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rq(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rJ(t){return(rJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rQ(t){return function(t){if(Array.isArray(t))return r3(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||r5(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r0(Object(r),!0).forEach(function(e){r2(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r2(t,e,r){var n;return(n=function(t,e){if("object"!==rJ(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rJ(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r5(t,e){if(t){if("string"==typeof t)return r3(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r3(t,e)}}function r3(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r4=function(t,e,r){return t+(e-t)*r},r6=function(t){return t.from!==t.to},r8=function t(e,r,n){var o=r$(function(t,r){if(r6(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||r5(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return r1(r1({},r),{},{from:i,velocity:a})}return r},r);return n<1?r$(function(t,e){return r6(e)?r1(r1({},e),{},{velocity:r4(e.velocity,o[t].velocity,n),from:r4(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let r7=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return r1(r1({},r),{},r2({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return r1(r1({},r),{},r2({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=r8(r,l,a),o(r1(r1(r1({},t),e),r$(function(t,e){return e.from},l))),i=n,Object.values(l).filter(r6).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=r$(function(t,e){return r4.apply(void 0,rQ(e).concat([r(c)]))},u);if(o(r1(r1(r1({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=r$(function(t,e){return r4.apply(void 0,rQ(e).concat([r(1)]))},u);o(r1(r1(r1({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function r9(t){return(r9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var nt=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function ne(t){return function(t){if(Array.isArray(t))return nr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nr(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nr(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function no(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nn(Object(r),!0).forEach(function(e){ni(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ni(t,e,r){return(e=na(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function na(t){var e=function(t,e){if("object"!==r9(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r9(e)?e:String(e)}function nc(t,e){return(nc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nu(t,e){if(e&&("object"===r9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nl(t)}function nl(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ns(t){return(ns=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var nf=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&nc(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=ns(o);return t=e?Reflect.construct(r,arguments,ns(this).constructor):r.apply(this,arguments),nu(this,t)});function o(t,e){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),i=r.props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nl(r)),r.changeStyle=r.changeStyle.bind(nl(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nu(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nu(r);r.state={style:c?ni({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?ni({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rT(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?ni({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(no(no({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=r7(r,n,rZ(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(ne(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(ne(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=rF(p,i,c),d=no(no(no({},f.style),u),{},{transition:h});return[].concat(ne(t),[d,i,s]).filter(rU)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rD(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rD(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);return"number"==typeof i?void rN(t.bind(null,a),i):(t(i),void rN(t.bind(null,a)))}"object"===rI(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,o,i=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?ni({},c,u):u,v=rF(Object.keys(y),a,l);d.start([s,i,no(no({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,nt)),c=i.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,i.cloneElement)(t,no(no({},o),{},{style:no(no({},void 0===r?{}:r),u),className:n}))};return 1===c?l(i.Children.only(e)):a().createElement("div",null,i.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,na(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(i.PureComponent);function np(t){return(np="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nh(){return(nh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nd(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ny(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ny(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=np(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=np(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==np(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ny(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}nf.displayName="Animate",nf.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nf.propTypes={from:rn().oneOfType([rn().object,rn().string]),to:rn().oneOfType([rn().object,rn().string]),attributeName:rn().string,duration:rn().number,begin:rn().number,easing:rn().oneOfType([rn().string,rn().func]),steps:rn().arrayOf(rn().shape({duration:rn().number.isRequired,style:rn().object.isRequired,easing:rn().oneOfType([rn().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),rn().func]),properties:rn().arrayOf("string"),onAnimationEnd:rn().func})),children:rn().oneOfType([rn().node,rn().func]),isActive:rn().bool,canBegin:rn().bool,onAnimationEnd:rn().func,shouldReAnimate:rn().bool,onAnimationStart:rn().func,onAnimationReStart:rn().func};var nm=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},nb=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},ng={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nx=function(t){var e,r=nv(nv({},ng),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nd(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nd(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.width,p=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var O=(0,x.A)("recharts-rectangle",d);return g?a().createElement(nf,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,o=t.height,i=t.x,u=t.y;return a().createElement(nf,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},a().createElement("path",nh({},tb(r,!0),{className:O,d:nm(i,u,e,o,h),ref:n})))}):a().createElement("path",nh({},tb(r,!0),{className:O,d:nm(l,s,f,p,h)}))};function nO(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nw(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nj extends Map{constructor(t,e=nP){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nS(this,t))}has(t){return super.has(nS(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nS({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nP(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nA=Symbol("implicit");function nE(){var t=new nj,e=[],r=[],n=nA;function o(o){let i=t.get(o);if(void 0===i){if(n!==nA)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nj,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return nE(e,r).unknown(n)},nO.apply(o,arguments),o}function nk(){var t,e,r=nE().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nk(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nO.apply(f(),arguments)}function nM(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nk.apply(null,arguments).paddingInner(1))}function n_(t){return(n_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nT(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=n_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=n_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==n_(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nN(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nI={widthCache:{},cacheCount:0},nD={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nB="recharts_measurement_span",nR=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||t6.isSsr)return{width:0,height:0};var n=(Object.keys(e=nC({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nI.widthCache[o])return nI.widthCache[o];try{var i=document.getElementById(nB);i||((i=document.createElement("span")).setAttribute("id",nB),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nC(nC({},nD),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nI.widthCache[o]=u,++nI.cacheCount>2e3&&(nI.cacheCount=0,nI.widthCache={}),u}catch(t){return{width:0,height:0}}};function nL(t){return(nL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nz(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nU(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nU(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function n$(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nL(e)?e:e+""}(n.key),n)}}var nF=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nq=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nW=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nX=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nH={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nG=Object.keys(nH),nV=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nW.test(e)||(this.num=NaN,this.unit=""),nG.includes(e)&&(this.num=t*nH[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nz(null!=(e=nX.exec(t))?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&n$(r.prototype,t),e&&n$(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nK(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nz(null!=(r=nF.exec(e))?r:[],4),o=n[1],i=n[2],a=n[3],c=nV.parse(null!=o?o:""),u=nV.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nF,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nz(null!=(s=nq.exec(e))?s:[],4),p=f[1],h=f[2],d=f[3],y=nV.parse(null!=p?p:""),v=nV.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nq,m.toString())}return e}var nY=/\(([^()]*)\)/;function nZ(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nz(nY.exec(e),2)[1];e=e.replace(nY,nK(r))}return e}(e),e=nK(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nJ=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nQ=["dx","dy","angle","className","breakAll"];function n0(){return(n0=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function n1(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function n2(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return n5(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n5(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n5(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var n3=/[ \f\n\r\t\v\u2028\u2029]+/,n4=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];X()(e)||(o=r?e.toString().split(""):e.toString().split(n3));var i=o.map(function(t){return{word:t,width:nR(t,n).width}}),a=r?0:nR("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},n6=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=N(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(n4({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=n2(h(m-1),2),g=b[0],x=b[1],O=n2(h(m),1)[0];if(g||O||(d=m+1),g&&O&&(y=m-1),!g&&O){i=x;break}v++}return i||p},n8=function(t){return[{words:X()(t)?[]:t.toString().split(n3)}]},n7=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!t6.isSsr){var c=n4({breakAll:i,children:n,style:o});if(!c)return n8(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return n6({breakAll:i,children:n,maxLines:a,style:o},u,l,e,r)}return n8(n)},n9="#808080",ot=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,c=void 0===o?0:o,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,h=void 0!==p&&p,d=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?n9:v,b=n1(t,nJ),g=(0,i.useMemo)(function(){return n7({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:h,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,h,b.style,b.width]),O=b.dx,w=b.dy,j=b.angle,S=b.className,P=b.breakAll,A=n1(b,nQ);if(!I(n)||!I(c))return null;var E=n+(N(O)?O:0),k=c+(N(w)?w:0);switch(void 0===y?"end":y){case"start":e=nZ("calc(".concat(f,")"));break;case"middle":e=nZ("calc(".concat((g.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:e=nZ("calc(".concat(g.length-1," * -").concat(l,")"))}var M=[];if(h){var _=g[0].width,T=b.width;M.push("scale(".concat((N(T)?T/_:1)/_,")"))}return j&&M.push("rotate(".concat(j,", ").concat(E,", ").concat(k,")")),M.length&&(A.transform=M.join(" ")),a().createElement("text",n0({},tb(A,!0),{x:E,y:k,className:(0,x.A)("recharts-text",S),textAnchor:void 0===d?"start":d,fill:m.includes("url")?n9:m}),g.map(function(t,r){var n=t.words.join(P?"":" ");return a().createElement("tspan",{x:E,dy:0===r?e:l,key:"".concat(n,"-").concat(r)},n)}))};let oe=Math.sqrt(50),or=Math.sqrt(10),on=Math.sqrt(2);function oo(t,e,r){let n,o,i,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=oe?10:u>=or?5:u>=on?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?oo(t,e,2*r):[n,o,i]}function oi(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?oo(e,t,r):oo(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function oa(t,e,r){return oo(t*=1,e*=1,r*=1)[2]}function oc(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?oa(e,t,r):oa(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function ou(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ol(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function os(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=ou,r=(e,r)=>ou(t(e),r),n=(e,r)=>t(e)-r):(e=t===ou||t===ol?t:of,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function of(){return 0}function op(t){return null===t?NaN:+t}let oh=os(ou),od=oh.right;function oy(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function ov(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function om(){}oh.left,os(op).center;var ob="\\s*([+-]?\\d+)\\s*",og="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ox="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",oO=/^#([0-9a-f]{3,8})$/,ow=RegExp(`^rgb\\(${ob},${ob},${ob}\\)$`),oj=RegExp(`^rgb\\(${ox},${ox},${ox}\\)$`),oS=RegExp(`^rgba\\(${ob},${ob},${ob},${og}\\)$`),oP=RegExp(`^rgba\\(${ox},${ox},${ox},${og}\\)$`),oA=RegExp(`^hsl\\(${og},${ox},${ox}\\)$`),oE=RegExp(`^hsla\\(${og},${ox},${ox},${og}\\)$`),ok={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function oM(){return this.rgb().formatHex()}function o_(){return this.rgb().formatRgb()}function oT(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=oO.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?oC(e):3===r?new oD(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?oN(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?oN(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=ow.exec(t))?new oD(e[1],e[2],e[3],1):(e=oj.exec(t))?new oD(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=oS.exec(t))?oN(e[1],e[2],e[3],e[4]):(e=oP.exec(t))?oN(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=oA.exec(t))?o$(e[1],e[2]/100,e[3]/100,1):(e=oE.exec(t))?o$(e[1],e[2]/100,e[3]/100,e[4]):ok.hasOwnProperty(t)?oC(ok[t]):"transparent"===t?new oD(NaN,NaN,NaN,0):null}function oC(t){return new oD(t>>16&255,t>>8&255,255&t,1)}function oN(t,e,r,n){return n<=0&&(t=e=r=NaN),new oD(t,e,r,n)}function oI(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof om||(o=oT(o)),o)?new oD((o=o.rgb()).r,o.g,o.b,o.opacity):new oD:new oD(t,e,r,null==n?1:n)}function oD(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function oB(){return`#${oU(this.r)}${oU(this.g)}${oU(this.b)}`}function oR(){let t=oL(this.opacity);return`${1===t?"rgb(":"rgba("}${oz(this.r)}, ${oz(this.g)}, ${oz(this.b)}${1===t?")":`, ${t})`}`}function oL(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function oz(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oU(t){return((t=oz(t))<16?"0":"")+t.toString(16)}function o$(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oq(t,e,r,n)}function oF(t){if(t instanceof oq)return new oq(t.h,t.s,t.l,t.opacity);if(t instanceof om||(t=oT(t)),!t)return new oq;if(t instanceof oq)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oq(a,c,u,t.opacity)}function oq(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oW(t){return(t=(t||0)%360)<0?t+360:t}function oX(t){return Math.max(0,Math.min(1,t||0))}function oH(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function oG(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}oy(om,oT,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:oM,formatHex:oM,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oF(this).formatHsl()},formatRgb:o_,toString:o_}),oy(oD,oI,ov(om,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oD(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oD(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oD(oz(this.r),oz(this.g),oz(this.b),oL(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:oB,formatHex:oB,formatHex8:function(){return`#${oU(this.r)}${oU(this.g)}${oU(this.b)}${oU((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oR,toString:oR})),oy(oq,function(t,e,r,n){return 1==arguments.length?oF(t):new oq(t,e,r,null==n?1:n)},ov(om,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oq(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oq(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oD(oH(t>=240?t-240:t+120,o,n),oH(t,o,n),oH(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oq(oW(this.h),oX(this.s),oX(this.l),oL(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oL(this.opacity);return`${1===t?"hsl(":"hsla("}${oW(this.h)}, ${100*oX(this.s)}%, ${100*oX(this.l)}%${1===t?")":`, ${t})`}`}}));let oV=t=>()=>t;function oK(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):oV(isNaN(t)?e:t)}let oY=function t(e){var r,n=1==(r=+e)?oK:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):oV(isNaN(t)?e:t)};function o(t,e){var r=n((t=oI(t)).r,(e=oI(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=oK(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function oZ(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oI(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}oZ(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return oG((r-n/e)*e,a,o,i,c)}}),oZ(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return oG((r-n/e)*e,o,i,a,c)}});function oJ(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var oQ=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,o0=RegExp(oQ.source,"g");function o1(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?oV(e):("number"===o?oJ:"string"===o?(n=oT(e))?(e=n,oY):function(t,e){var r,n,o,i,a,c=oQ.lastIndex=o0.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=oQ.exec(t))&&(i=o0.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:oJ(o,i)})),c=o0.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof oT?oY:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=o1(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=o1(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:oJ:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function o2(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function o5(t){return+t}var o3=[0,1];function o4(t){return t}function o6(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function o8(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=o6(o,n),i=r(a,i)):(n=o6(n,o),i=r(i,a)),function(t){return i(n(t))}}function o7(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=o6(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=od(t,e,1,n)-1;return i[r](o[r](e))}}function o9(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function it(){var t,e,r,n,o,i,a=o3,c=o3,u=o1,l=o4;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==o4&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?o7:o8,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),oJ)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,o5),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=o2,s()},f.clamp=function(t){return arguments.length?(l=!!t||o4,s()):l!==o4},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function ie(){return it()(o4,o4)}var ir=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function io(t){var e;if(!(e=ir.exec(t)))throw Error("invalid format: "+t);return new ii({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function ii(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function ia(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function ic(t){return(t=ia(Math.abs(t)))?t[1]:NaN}function iu(t,e){var r=ia(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}io.prototype=ii.prototype,ii.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let il={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>iu(100*t,e),r:iu,s:function(t,e){var r=ia(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(cz=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+ia(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function is(t){return t}var ip=Array.prototype.map,ih=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function id(t,e,r,n){var o,i,a,c=oc(t,e,r);switch((n=io(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ic(u)/3)))-ic(Math.abs(c))))||(n.precision=a),cF(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,ic(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-ic(o))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-ic(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return c$(n)}function iy(t){var e=t.domain;return t.ticks=function(t){var r=e();return oi(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return id(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=oa(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function iv(){var t=ie();return t.copy=function(){return o9(t,iv())},nO.apply(t,arguments),iy(t)}function im(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function ib(t){return Math.log(t)}function ig(t){return Math.exp(t)}function ix(t){return-Math.log(-t)}function iO(t){return-Math.exp(-t)}function iw(t){return isFinite(t)?+("1e"+t):t<0?0:t}function ij(t){return(e,r)=>-t(-e,r)}function iS(t){let e,r,n=t(ib,ig),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?iw:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=ij(e),r=ij(r),t(ix,iO)):t(ib,ig),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a,c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=oi(u,l,h))}else d=oi(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=io(o)).precision||(o.trim=!0),o=c$(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(im(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function iP(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function iA(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function iE(t){var e=1,r=t(iP(1),iA(e));return r.constant=function(r){return arguments.length?t(iP(e=+r),iA(e)):e},iy(r)}function ik(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function iM(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function i_(t){return t<0?-t*t:t*t}function iT(t){var e=t(o4,o4),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(o4,o4):.5===r?t(iM,i_):t(ik(r),ik(1/r)):r},iy(e)}function iC(){var t=iT(it());return t.copy=function(){return o9(t,iC()).exponent(t.exponent())},nO.apply(t,arguments),t}function iN(){return iC.apply(null,arguments).exponent(.5)}function iI(t){return Math.sign(t)*t*t}function iD(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function iB(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}c$=(cU=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?is:(e=ip.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?is:(n=ip.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=io(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):il[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=il[b],w=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?ih[8+cz/3]:"")+j+(S&&"("===n?")":""),w){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=o(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=io(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(ic(e)/3))),o=Math.pow(10,-n),i=ih[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cF=cU.formatPrefix;function iR(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function iL(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let iz=new Date,iU=new Date;function i$(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a,c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>i$(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(iz.setTime(+e),iU.setTime(+n),t(iz),t(iU),Math.floor(r(iz,iU))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let iF=i$(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);iF.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i$(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):iF:null,iF.range;let iq=i$(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());iq.range;let iW=i$(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iW.range;let iX=i$(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iX.range;let iH=i$(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iH.range;let iG=i$(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());iG.range;let iV=i$(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);iV.range;let iK=i$(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);iK.range;let iY=i$(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function iZ(t){return i$(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}iY.range;let iJ=iZ(0),iQ=iZ(1),i0=iZ(2),i1=iZ(3),i2=iZ(4),i5=iZ(5),i3=iZ(6);function i4(t){return i$(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}iJ.range,iQ.range,i0.range,i1.range,i2.range,i5.range,i3.range;let i6=i4(0),i8=i4(1),i7=i4(2),i9=i4(3),at=i4(4),ae=i4(5),ar=i4(6);i6.range,i8.range,i7.range,i9.range,at.range,ae.range,ar.range;let an=i$(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());an.range;let ao=i$(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());ao.range;let ai=i$(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());ai.every=t=>isFinite(t=Math.floor(t))&&t>0?i$(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,ai.range;let aa=i$(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ac(t,e,r,n,o,i){let a=[[iq,1,1e3],[iq,5,5e3],[iq,15,15e3],[iq,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=os(([,,t])=>t).right(a,o);if(i===a.length)return t.every(oc(e/31536e6,r/31536e6,n));if(0===i)return iF.every(Math.max(oc(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}aa.every=t=>isFinite(t=Math.floor(t))&&t>0?i$(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,aa.range;let[au,al]=ac(aa,ao,i6,iY,iG,iX),[as,af]=ac(ai,an,iJ,iV,iH,iW);function ap(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ah(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ad(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var ay={"-":"",_:" ",0:"0"},av=/^\s*\d+/,am=/^%/,ab=/[\\^$*+?|[\]().{}]/g;function ag(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function ax(t){return t.replace(ab,"\\$&")}function aO(t){return RegExp("^(?:"+t.map(ax).join("|")+")","i")}function aw(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function aj(t,e,r){var n=av.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aS(t,e,r){var n=av.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function aP(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aA(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aE(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function ak(t,e,r){var n=av.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function a_(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aT(t,e,r){var n=av.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aC(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aN(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=av.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aR(t,e,r){var n=av.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aL(t,e,r){var n=av.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function az(t,e,r){var n=av.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aU(t,e,r){var n=am.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function a$(t,e,r){var n=av.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aF(t,e,r){var n=av.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aq(t,e){return ag(t.getDate(),e,2)}function aW(t,e){return ag(t.getHours(),e,2)}function aX(t,e){return ag(t.getHours()%12||12,e,2)}function aH(t,e){return ag(1+iV.count(ai(t),t),e,3)}function aG(t,e){return ag(t.getMilliseconds(),e,3)}function aV(t,e){return aG(t,e)+"000"}function aK(t,e){return ag(t.getMonth()+1,e,2)}function aY(t,e){return ag(t.getMinutes(),e,2)}function aZ(t,e){return ag(t.getSeconds(),e,2)}function aJ(t){var e=t.getDay();return 0===e?7:e}function aQ(t,e){return ag(iJ.count(ai(t)-1,t),e,2)}function a0(t){var e=t.getDay();return e>=4||0===e?i2(t):i2.ceil(t)}function a1(t,e){return t=a0(t),ag(i2.count(ai(t),t)+(4===ai(t).getDay()),e,2)}function a2(t){return t.getDay()}function a5(t,e){return ag(iQ.count(ai(t)-1,t),e,2)}function a3(t,e){return ag(t.getFullYear()%100,e,2)}function a4(t,e){return ag((t=a0(t)).getFullYear()%100,e,2)}function a6(t,e){return ag(t.getFullYear()%1e4,e,4)}function a8(t,e){var r=t.getDay();return ag((t=r>=4||0===r?i2(t):i2.ceil(t)).getFullYear()%1e4,e,4)}function a7(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ag(e/60|0,"0",2)+ag(e%60,"0",2)}function a9(t,e){return ag(t.getUTCDate(),e,2)}function ct(t,e){return ag(t.getUTCHours(),e,2)}function ce(t,e){return ag(t.getUTCHours()%12||12,e,2)}function cr(t,e){return ag(1+iK.count(aa(t),t),e,3)}function cn(t,e){return ag(t.getUTCMilliseconds(),e,3)}function co(t,e){return cn(t,e)+"000"}function ci(t,e){return ag(t.getUTCMonth()+1,e,2)}function ca(t,e){return ag(t.getUTCMinutes(),e,2)}function cc(t,e){return ag(t.getUTCSeconds(),e,2)}function cu(t){var e=t.getUTCDay();return 0===e?7:e}function cl(t,e){return ag(i6.count(aa(t)-1,t),e,2)}function cs(t){var e=t.getUTCDay();return e>=4||0===e?at(t):at.ceil(t)}function cf(t,e){return t=cs(t),ag(at.count(aa(t),t)+(4===aa(t).getUTCDay()),e,2)}function cp(t){return t.getUTCDay()}function ch(t,e){return ag(i8.count(aa(t)-1,t),e,2)}function cd(t,e){return ag(t.getUTCFullYear()%100,e,2)}function cy(t,e){return ag((t=cs(t)).getUTCFullYear()%100,e,2)}function cv(t,e){return ag(t.getUTCFullYear()%1e4,e,4)}function cm(t,e){var r=t.getUTCDay();return ag((t=r>=4||0===r?at(t):at.ceil(t)).getUTCFullYear()%1e4,e,4)}function cb(){return"+0000"}function cg(){return"%"}function cx(t){return+t}function cO(t){return Math.floor(t/1e3)}function cw(t){return new Date(t)}function cj(t){return t instanceof Date?+t:+new Date(+t)}function cS(t,e,r,n,o,i,a,c,u,l){var s=ie(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cj)):p().map(cw)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(im(r,t)):s},s.copy=function(){return o9(s,cS(t,e,r,n,o,i,a,c,u,l))},s}function cP(){return nO.apply(cS(as,af,ai,an,iJ,iV,iH,iW,iq,cW).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cA(){return nO.apply(cS(au,al,aa,ao,i6,iK,iG,iX,iq,cX).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cE(){var t,e,r,n,o,i=0,a=1,c=o4,u=!1;function l(e){return null==e||isNaN(e*=1)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(o1),l.rangeRound=s(o2),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function ck(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cM(){var t=iT(cE());return t.copy=function(){return ck(t,cM()).exponent(t.exponent())},nw.apply(t,arguments)}function c_(){return cM.apply(null,arguments).exponent(.5)}function cT(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=o4,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=o1);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c*=1),e=i(u*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(o1),h.rangeRound=d(o2),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cC(){var t=iT(cT());return t.copy=function(){return ck(t,cC()).exponent(t.exponent())},nw.apply(t,arguments)}function cN(){return cC.apply(null,arguments).exponent(.5)}function cI(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cD(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cB(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cR(t,e){return t[e]}function cL(t){let e=[];return e.key=t,e}cW=(cq=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=aO(o),s=aw(o),f=aO(i),p=aw(i),h=aO(a),d=aw(a),y=aO(c),v=aw(c),m=aO(u),b=aw(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aq,e:aq,f:aV,g:a4,G:a8,H:aW,I:aX,j:aH,L:aG,m:aK,M:aY,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cx,s:cO,S:aZ,u:aJ,U:aQ,V:a1,w:a2,W:a5,x:null,X:null,y:a3,Y:a6,Z:a7,"%":cg},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:a9,e:a9,f:co,g:cy,G:cm,H:ct,I:ce,j:cr,L:cn,m:ci,M:ca,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cx,s:cO,S:cc,u:cu,U:cl,V:cf,w:cp,W:ch,x:null,X:null,y:cd,Y:cv,Z:cb,"%":cg},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aN,e:aN,f:az,g:aM,G:ak,H:aD,I:aD,j:aI,L:aL,m:aC,M:aB,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aT,Q:a$,s:aF,S:aR,u:aS,U:aP,V:aA,w:aj,W:aE,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aM,Y:ak,Z:a_,"%":aU};function w(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=ay[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=ad(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ah(ad(i.y,0,1))).getUTCDay())>4||0===o?i8.ceil(n):i8(n),n=iK.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=ap(ad(i.y,0,1))).getDay())>4||0===o?iQ.ceil(n):iQ(n),n=iV.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?ah(ad(i.y,0,1)).getUTCDay():ap(ad(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ah(i)):ap(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in ay?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cq.parse,cX=cq.utcFormat,cq.utcParse,Array.prototype.slice;var cz,cU,c$,cF,cq,cW,cX,cH,cG,cV=r(90453),cK=r.n(cV),cY=r(15883),cZ=r.n(cY),cJ=r(21592),cQ=r.n(cJ),c0=r(71967),c1=r.n(c0),c2=!0,c5="[DecimalError] ",c3=c5+"Invalid argument: ",c4=c5+"Exponent out of range: ",c6=Math.floor,c8=Math.pow,c7=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c9=c6(1286742750677284.5),ut={};function ue(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),c2?uf(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,c2?uf(e,f):e}function ur(t,e,r){if(t!==~~t||t<e||t>r)throw Error(c3+t)}function un(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=uu(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=uu(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}ut.absoluteValue=ut.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},ut.comparedTo=ut.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},ut.decimalPlaces=ut.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},ut.dividedBy=ut.div=function(t){return uo(this,new this.constructor(t))},ut.dividedToIntegerBy=ut.idiv=function(t){var e=this.constructor;return uf(uo(this,new e(t),0,1),e.precision)},ut.equals=ut.eq=function(t){return!this.cmp(t)},ut.exponent=function(){return ua(this)},ut.greaterThan=ut.gt=function(t){return this.cmp(t)>0},ut.greaterThanOrEqualTo=ut.gte=function(t){return this.cmp(t)>=0},ut.isInteger=ut.isint=function(){return this.e>this.d.length-2},ut.isNegative=ut.isneg=function(){return this.s<0},ut.isPositive=ut.ispos=function(){return this.s>0},ut.isZero=function(){return 0===this.s},ut.lessThan=ut.lt=function(t){return 0>this.cmp(t)},ut.lessThanOrEqualTo=ut.lte=function(t){return 1>this.cmp(t)},ut.logarithm=ut.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cG))throw Error(c5+"NaN");if(this.s<1)throw Error(c5+(this.s?"NaN":"-Infinity"));return this.eq(cG)?new r(0):(c2=!1,e=uo(ul(this,o),ul(t,o),o),c2=!0,uf(e,n))},ut.minus=ut.sub=function(t){return t=new this.constructor(t),this.s==t.s?up(this,t):ue(this,(t.s=-t.s,t))},ut.modulo=ut.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c5+"NaN");return this.s?(c2=!1,e=uo(this,t,0,1).times(t),c2=!0,this.minus(e)):uf(new r(this),n)},ut.naturalExponential=ut.exp=function(){return ui(this)},ut.naturalLogarithm=ut.ln=function(){return ul(this)},ut.negated=ut.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},ut.plus=ut.add=function(t){return t=new this.constructor(t),this.s==t.s?ue(this,t):up(this,(t.s=-t.s,t))},ut.precision=ut.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(c3+t);if(e=ua(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},ut.squareRoot=ut.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(c5+"NaN")}for(t=ua(this),c2=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=un(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=c6((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(uo(this,i,a+2)).times(.5),un(i.d).slice(0,a)===(e=un(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(uf(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return c2=!0,uf(n,r)},ut.times=ut.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,c2?uf(t,s.precision):t},ut.toDecimalPlaces=ut.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(ur(t,0,1e9),void 0===e?e=n.rounding:ur(e,0,8),uf(r,t+ua(r)+1,e))},ut.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=uh(n,!0):(ur(t,0,1e9),void 0===e?e=o.rounding:ur(e,0,8),r=uh(n=uf(new o(n),t+1,e),!0,t+1)),r},ut.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?uh(this):(ur(t,0,1e9),void 0===e?e=o.rounding:ur(e,0,8),r=uh((n=uf(new o(this),t+ua(this)+1,e)).abs(),!1,t+ua(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},ut.toInteger=ut.toint=function(){var t=this.constructor;return uf(new t(this),ua(this)+1,t.rounding)},ut.toNumber=function(){return+this},ut.toPower=ut.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(cG);if(!(c=new u(c)).s){if(t.s<1)throw Error(c5+"Infinity");return c}if(c.eq(cG))return c;if(n=u.precision,t.eq(cG))return uf(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(o=new u(cG),e=Math.ceil(n/7+4),c2=!1;r%2&&ud((o=o.times(c)).d,e),0!==(r=c6(r/2));)ud((c=c.times(c)).d,e);return c2=!0,t.s<0?new u(cG).div(o):uf(o,n)}}else if(i<0)throw Error(c5+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,c2=!1,o=t.times(ul(c,n+12)),c2=!0,(o=ui(o)).s=i,o},ut.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=ua(o),n=uh(o,r<=i.toExpNeg||r>=i.toExpPos)):(ur(t,1,1e9),void 0===e?e=i.rounding:ur(e,0,8),r=ua(o=uf(new i(o),t,e)),n=uh(o,t<=r||r<=i.toExpNeg,t)),n},ut.toSignificantDigits=ut.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(ur(t,1,1e9),void 0===e?e=r.rounding:ur(e,0,8)),uf(new r(this),t,e)},ut.toString=ut.valueOf=ut.val=ut.toJSON=ut[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=ua(this),e=this.constructor;return uh(this,t<=e.toExpNeg||t>=e.toExpPos)};var uo=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,S,P=n.constructor,A=n.s==o.s?1:-1,E=n.d,k=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(c5+"Division by zero");for(l=0,u=n.e-o.e,j=k.length,O=E.length,d=(h=new P(A)).d=[];k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--u,(b=null==i?i=P.precision:a?i+(ua(n)-ua(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/k|0,s=g%k|0;else{for((s=1e7/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,O=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=k.slice()).unshift(0),w=k[0],k[1]>=1e7/2&&++w;do s=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(k,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:k,p))):(0==s&&(c=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(s++,r(y,j<v?S:k,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,uf(h,a?i+ua(h)+1:i)}}();function ui(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(ua(t)>16)throw Error(c4+ua(t));if(!t.s)return new l(cG);for(null==e?(c2=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(c8(2,u))/Math.LN10*2+5|0,r=n=o=new l(cG),l.precision=a;;){if(n=uf(n.times(t),a),r=r.times(++c),un((i=o.plus(uo(n,r,a))).d).slice(0,a)===un(o.d).slice(0,a)){for(;u--;)o=uf(o.times(o),a);return l.precision=s,null==e?(c2=!0,uf(o,s)):o}o=i}}function ua(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function uc(t,e,r){if(e>t.LN10.sd())throw c2=!0,r&&(t.precision=r),Error(c5+"LN10 precision limit exceeded");return uf(new t(t.LN10),e)}function uu(t){for(var e="";t--;)e+="0";return e}function ul(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(c5+(p.s?"NaN":"-Infinity"));if(p.eq(cG))return new d(0);if(null==e?(c2=!1,l=y):l=e,p.eq(10))return null==e&&(c2=!0),uc(d,l);if(d.precision=l+=10,n=(r=un(h)).charAt(0),!(15e14>Math.abs(i=ua(p))))return u=uc(d,l+2,y).times(i+""),p=ul(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(c2=!0,uf(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=un((p=p.times(t)).d)).charAt(0),f++;for(i=ua(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),c=a=p=uo(p.minus(cG),p.plus(cG),l),s=uf(p.times(p),l),o=3;;){if(a=uf(a.times(s),l),un((u=c.plus(uo(a,new d(o),l))).d).slice(0,l)===un(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(uc(d,l+2,y).times(i+""))),c=uo(c,new d(f),l),d.precision=y,null==e?(c2=!0,uf(c,y)):c;c=u,o+=2}}function us(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=c6((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),c2&&(t.e>c9||t.e<-c9))throw Error(c4+r)}else t.s=0,t.e=0,t.d=[0];return t}function uf(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=c8(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/c8(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=ua(t),f.length=1,e=e-i-1,f[0]=c8(10,(7-e%7)%7),t.e=c6(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=c8(10,7-n),f[s]=o>0?(l/c8(10,a-o)%c8(10,o)|0)*i:0),u)for(;;)if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}else{if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(c2&&(t.e>c9||t.e<-c9))throw Error(c4+ua(t));return t}function up(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),c2?uf(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,c2?uf(e,h):e):new p(0)}function uh(t,e,r){var n,o=ua(t),i=un(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+uu(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+uu(-o-1)+i,r&&(n=r-a)>0&&(i+=uu(n))):o>=a?(i+=uu(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+uu(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=uu(n))),t.s<0?"-"+i:i}function ud(t,e){if(t.length>e)return t.length=e,!0}function uy(t){if(!t||"object"!=typeof t)throw Error(c5+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]]))if(c6(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(c3+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(c3+r+": "+n);return this}var cH=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(c3+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return us(this,t.toString())}if("string"!=typeof t)throw Error(c3+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,c7.test(t))us(this,t);else throw Error(c3+t)}if(i.prototype=ut,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=uy,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cG=new cH(1);let uv=cH;function um(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ub=function(t){return t},ug={},ux=function(t){return t===ug},uO=function(t){return function e(){return 0==arguments.length||1==arguments.length&&ux(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uw=function(t){return function t(e,r){return 1===e?r:uO(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==ug}).length;return a>=e?r.apply(void 0,o):t(e-a,uO(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return ux(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return um(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return um(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return um(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uj=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uS=uw(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uP=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return ub;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},uA=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uE=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};uw(function(t,e,r){var n=+t;return n+r*(e-n)}),uw(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),uw(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uk={rangeStep:function(t,e,r){for(var n=new uv(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uv(t).abs().log(10).toNumber())+1}};function uM(t){return function(t){if(Array.isArray(t))return uC(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uT(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u_(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uT(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uT(t,e){if(t){if("string"==typeof t)return uC(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uC(t,e)}}function uC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uN(t){var e=u_(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uI(t,e,r){if(t.lte(0))return new uv(0);var n=uk.getDigitCount(t.toNumber()),o=new uv(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new uv(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new uv(Math.ceil(c))}function uD(t,e,r){var n=1,o=new uv(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new uv(10).pow(uk.getDigitCount(t)-1),o=new uv(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new uv(Math.floor(t)))}else 0===t?o=new uv(Math.floor((e-1)/2)):r||(o=new uv(Math.floor(t)));var a=Math.floor((e-1)/2);return uP(uS(function(t){return o.add(new uv(t-a).mul(n)).toNumber()}),uj)(0,e)}var uB=uE(function(t){var e=u_(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=u_(uN([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uM(uj(0,o-1).map(function(){return 1/0}))):[].concat(uM(uj(0,o-1).map(function(){return-1/0})),[l]);return r>n?uA(s):s}if(u===l)return uD(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uv(0),tickMin:new uv(0),tickMax:new uv(0)};var c=uI(new uv(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new uv(0):(i=new uv(e).add(r).div(2)).sub(new uv(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uv(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new uv(u).mul(c)),tickMax:i.add(new uv(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=uk.rangeStep(h,d.add(new uv(.1).mul(p)),p);return r>n?uA(y):y});uE(function(t){var e=u_(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=u_(uN([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uD(u,o,i);var s=uI(new uv(l).sub(u).div(a-1),i,0),f=uP(uS(function(t){return new uv(u).add(new uv(t).mul(s)).toNumber()}),uj)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uA(f):f});var uR=uE(function(t,e){var r=u_(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=u_(uN([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=uI(new uv(u).sub(c).div(l-1),i,0),f=[].concat(uM(uk.rangeStep(new uv(c),new uv(u).sub(new uv(.99).mul(s)),s)),[u]);return n>o?uA(f):f}),uL=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uz(t){return(uz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uU(){return(uU=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function u$(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uF(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uF=function(){return!!t})()}function uq(t){return(uq=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uW(t,e){return(uW=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uX(t,e,r){return(e=uH(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uH(t){var e=function(t,e){if("object"!=uz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uz(e)?e:e+""}var uG=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=uq(t),function(t,e){if(e&&("object"===uz(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uF()?Reflect.construct(t,e||[],uq(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&uW(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=tb(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uL),!1);"x"===this.props.direction&&"number"!==u.type&&tC(!1);var f=i.map(function(t){var i,f,p=c(t,o),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return u$(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u$(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=u.scale,x=d+e,O=x+n,w=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:O,x2:S,y2:w}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:O,x2:j,y2:w})}else if("horizontal"===r){var P=l.scale,A=h+e,E=A-n,k=A+n,M=P(y-i),_=P(y+f);m.push({x1:E,y1:_,x2:k,y2:_}),m.push({x1:A,y1:M,x2:A,y2:_}),m.push({x1:E,y1:M,x2:k,y2:M})}return a().createElement(tL,uU({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uU({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tL,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uH(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function uV(t){return(uV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uK(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=uV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uV(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uX(uG,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uX(uG,"displayName","ErrorBar");var uZ=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=td(r,e9);if(!a)return null;var c=e9.defaultProps,u=void 0!==c?uY(uY({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?uY(uY({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:u8(e),value:i||o,payload:n}}),uY(uY(uY({},u),e9.getWithHeight(a,o)),{},{payload:e,item:a})};function uJ(t){return(uJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uQ(t){return function(t){if(Array.isArray(t))return u0(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u0(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u0(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u0(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u1(Object(r),!0).forEach(function(e){u5(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u5(t,e,r){var n;return(n=function(t,e){if("object"!=uJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uJ(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u3(t,e,r){return X()(t)||X()(e)?r:I(e)?k()(t,e,r):G()(e)?e(t):r}function u4(t,e,r,n){var o=cQ()(t,function(t){return u3(t,e)});if("number"===r){var i=o.filter(function(t){return N(t)||parseFloat(t)});return i.length?[cZ()(i),cK()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!X()(t)}):o).map(function(t){return I(t)||t instanceof Date?t:""})}var u6=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(T(s-l)!==T(f-s)){var h=[];if(T(f-s)===T(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},u8=function(t){var e,r,n=t.type.displayName,o=null!=(e=t.type)&&e.defaultProps?u2(u2({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},u7=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return tl(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?u2(u2({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=X()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:X()(O)?void 0:R(O,r,0)})}}return i},u9=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=R(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(uQ(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=R(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(uQ(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},lt=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=uZ({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&N(t[f]))return u2(u2({},t),{},u5({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&N(t[p]))return u2(u2({},t),{},u5({},p,t[p]+(s||0)))}return t},le=function(t,e,r,n,o){var i=th(e.props.children,uG).filter(function(t){var e;return e=t.props.direction,!!X()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=u3(e,r);if(X()(n))return t;var o=Array.isArray(n)?[cZ()(n),cK()(n)]:[n,n],i=a.reduce(function(t,r){var n=u3(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},lr=function(t,e,r,n,o){var i=e.map(function(e){return le(t,e,r,o,n)}).filter(function(t){return!X()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},ln=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&le(t,e,i,n)||u4(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},lo=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},li=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},la=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*T(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!A()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},lc=new WeakMap,lu=function(t,e){if("function"!=typeof e)return t;lc.has(t)||lc.set(t,new WeakMap);var r=lc.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ll=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nk(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:iv(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nM(),realScaleType:"point"}:"category"===i?{scale:nk(),realScaleType:"band"}:{scale:iv(),realScaleType:"linear"};if(S()(o)){var u="scale".concat(ef()(o));return{scale:(n[u]||nM)(),realScaleType:n[u]?u:"point"}}return G()(o)?{scale:o}:{scale:nM(),realScaleType:"point"}},ls=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},lf=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lp=function(t,e){if(!e||2!==e.length||!N(e[0])||!N(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!N(t[0])||t[0]<r)&&(o[0]=r),(!N(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},lh={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=A()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cI(t,e)}},none:cI,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cI(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cI(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=A()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},ld=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=lh[r];return(function(){var t=eE([]),e=cB,r=cI,n=cR;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cL),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cD(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:eE(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:eE(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?cB:"function"==typeof t?t:eE(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cI:t,o):r},o})().keys(n).value(function(t,e){return+u3(t,e,0)}).order(cB).offset(o)(t)},ly=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!=(o=e.type)&&o.defaultProps?u2(u2({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(I(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[B("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return u2(u2({},t),{},u5({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return u2(u2({},e),{},u5({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:ld(t,a.items,o)}))},{})),u2(u2({},e),{},u5({},i,c))},{})},lv=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=uB(u,o,a);return t.domain([cZ()(l),cK()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:uR(t.domain(),o,a)}:null},lm=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=u3(i,e.dataKey,e.domain[a]);return X()(c)?null:e.scale(c)-o/2+n},lb=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},lg=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?u2(u2({},t.type.defaultProps),t.props):t.props).stackId;if(I(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},lx=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[cZ()(e.concat([t[0]]).filter(N)),cK()(e.concat([t[1]]).filter(N))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lO=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lw=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lj=function(t,e,r){if(G()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(N(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lO.test(t[0])){var o=+lO.exec(t[0])[1];n[0]=e[0]-o}else G()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(N(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lw.test(t[1])){var i=+lw.exec(t[1])[1];n[1]=e[1]+i}else G()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lS=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tT()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lP=function(t,e,r){return!t||!t.length||c1()(t,k()(r,"type.defaultProps.domain"))?e:t},lA=function(t,e){var r=t.type.defaultProps?u2(u2({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return u2(u2({},tb(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:u8(t),value:u3(e,n),type:c,payload:e,chartType:u,hide:l})};function lE(t){return(lE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lk(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lM(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lk(Object(r),!0).forEach(function(e){l_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lk(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l_(t,e,r){var n;return(n=function(t,e){if("object"!=lE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lE(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lT=["Webkit","Moz","O","ms"],lC=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lT.reduce(function(t,n){return lM(lM({},t),{},l_({},n+r,e))},{});return n[t]=e,n};function lN(t){return(lN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lI(){return(lI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lD(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lD(Object(r),!0).forEach(function(e){l$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lD(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lR(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lF(n.key),n)}}function lL(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lL=function(){return!!t})()}function lz(t){return(lz=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lU(t,e){return(lU=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function l$(t,e,r){return(e=lF(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lF(t){var e=function(t,e){if("object"!=lN(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lN(e)?e:e+""}var lq=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nM().domain(tM()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lW=function(t){return t.changedTouches&&!!t.changedTouches.length},lX=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=lz(r),l$(e=function(t,e){if(e&&("object"===lN(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lL()?Reflect.construct(r,o||[],lz(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),l$(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),l$(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),l$(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),l$(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),l$(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),l$(e,"handleSlideDragStart",function(t){var r=lW(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&lU(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,u),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=u3(r[t],o,t);return G()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lW(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===t};this.setState(l$(l$({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(l$({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,c=t.data,u=t.children,l=t.padding,s=i.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:o,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=lB(lB({},tb(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(o=h[y])?void 0:o.name);return a().createElement(tL,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:u,y:n,width:l,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tL,{className:"recharts-brush-texts"},a().createElement(ot,lI({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+o/2},f),this.getTextOfTick(e)),a().createElement(ot,lI({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!N(o)||!N(i)||!N(c)||!N(u)||c<=0||u<=0)return null;var m=(0,x.A)("recharts-brush",r),b=1===a().Children.count(n),g=lC("userSelect","none");return a().createElement(tL,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,c=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):G()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lB({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lq({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&lR(n.prototype,e),r&&lR(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function lH(t){return(lH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lG(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lG(Object(r),!0).forEach(function(e){lK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lG(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lK(t,e,r){var n;return(n=function(t,e){if("object"!=lH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lH(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}l$(lX,"displayName","Brush"),l$(lX,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var lZ=Math.PI/180,lJ=function(t,e,r,n){return{x:t+Math.cos(-lZ*n)*r,y:e+Math.sin(-lZ*n)*r}},lQ=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},l0=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l1=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=l0({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},l2=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},l5=function(t,e){var r,n=l1({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=l2(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lV(lV({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},l3=function(t){return(0,i.isValidElement)(t)||G()(t)||"boolean"==typeof t?"":t.className};function l4(t){return(l4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var l6=["offset"];function l8(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l7(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=l4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l4(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function st(){return(st=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var se=function(t){var e=t.value,r=t.formatter,n=X()(t.children)?e:t.children;return G()(r)?r(n):n},sr=function(t,e,r){var n,o,i=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c.cx,f=c.cy,p=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+h)/2,b=T(y-d)*Math.min(Math.abs(y-d),360),g=b>=0?1:-1;"insideStart"===i?(n=d+g*u,o=v):"insideEnd"===i?(n=y-g*u,o=!v):"end"===i&&(n=y+g*u,o=v),o=b<=0?o:!o;var O=lJ(s,f,m,n),w=lJ(s,f,m,n+(o?1:-1)*359),j="M".concat(O.x,",").concat(O.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!o,",\n    ").concat(w.x,",").concat(w.y),S=X()(t.id)?B("recharts-radial-line-"):t.id;return a().createElement("text",st({},r,{dominantBaseline:"central",className:(0,x.A)("recharts-radial-bar-label",l)}),a().createElement("defs",null,a().createElement("path",{id:S,d:j})),a().createElement("textPath",{xlinkHref:"#".concat(S)},e))},sn=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=lJ(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=lJ(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},so=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===o)return l9(l9({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return l9(l9({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return l9(l9({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return l9(l9({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?l9({x:i+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?l9({x:i+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?l9({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?l9({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?l9({x:i+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?l9({x:i+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?l9({x:i+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?l9({x:i+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):K()(o)&&(N(o.x)||C(o.x))&&(N(o.y)||C(o.y))?l9({x:i+R(o.x,c),y:a+R(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):l9({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function si(t){var e,r=t.offset,n=l9({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,l6)),o=n.viewBox,c=n.position,u=n.value,l=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!o||X()(u)&&X()(l)&&!(0,i.isValidElement)(s)&&!G()(s))return null;if((0,i.isValidElement)(s))return(0,i.cloneElement)(s,n);if(G()(s)){if(e=(0,i.createElement)(s,n),(0,i.isValidElement)(e))return e}else e=se(n);var h="cx"in o&&N(o.cx),d=tb(n,!0);if(h&&("insideStart"===c||"insideEnd"===c||"end"===c))return sr(n,e,d);var y=h?sn(n):so(n);return a().createElement(ot,st({className:(0,x.A)("recharts-label",void 0===f?"":f)},d,y,{breakAll:p}),e)}si.displayName="Label";var sa=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(N(d)&&N(y)){if(N(s)&&N(f))return{x:s,y:f,width:d,height:y};if(N(p)&&N(h))return{x:p,y:h,width:d,height:y}}return N(s)&&N(f)?{x:s,y:f,width:0,height:0}:N(e)&&N(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};si.parseViewBox=sa,si.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var c=t.children,u=sa(t),l=th(c,si).map(function(t,r){return(0,i.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!o)return l;return[(r=t.label,n=e||u,!r?null:!0===r?a().createElement(si,{key:"label-implicit",viewBox:n}):I(r)?a().createElement(si,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===si?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(si,{key:"label-implicit",content:r,viewBox:n}):G()(r)?a().createElement(si,{key:"label-implicit",content:r,viewBox:n}):K()(r)?a().createElement(si,st({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return l8(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return l8(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l8(t,e)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var sc=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},su=r(69691),sl=r.n(su),ss=r(47212),sf=r.n(ss),sp=function(t){return null};sp.displayName="Cell";var sh=r(5359),sd=r.n(sh);function sy(t){return(sy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sv=["valueAccessor"],sm=["data","dataKey","clockWise","id","textBreakAll"];function sb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sg(){return(sg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sx(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sy(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sy(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sw(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sj=function(t){return Array.isArray(t.value)?sd()(t.value):t.value};function sS(t){var e=t.valueAccessor,r=void 0===e?sj:e,n=sw(t,sv),o=n.data,i=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=sw(n,sm);return o&&o.length?a().createElement(tL,{className:"recharts-label-list"},o.map(function(t,e){var n=X()(i)?r(t,e):u3(t&&t.payload,i),o=X()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(si,sg({},tb(t,!0),s,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:si.parseViewBox(X()(c)?t:sO(sO({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}sS.displayName="LabelList",sS.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=th(t.children,sS).map(function(t,r){return(0,i.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?a().createElement(sS,{key:"labelList-implicit",data:e}):a().isValidElement(r)||G()(r)?a().createElement(sS,{key:"labelList-implicit",data:e,content:r}):K()(r)?a().createElement(sS,sg({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sb(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sb(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sb(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sP=r(38404),sA=r.n(sP),sE=r(98451),sk=r.n(sE);function sM(t){return(sM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s_(){return(s_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sT(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sN(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sC(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sM(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sM(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sM(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sI=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sD={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sB=function(t){var e,r=sN(sN({},sD),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sT(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sT(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],u=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var g=(0,x.A)("recharts-trapezoid",d);return b?a().createElement(nf,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,o=t.lowerWidth,i=t.height,u=t.x,l=t.y;return a().createElement(nf,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",s_({},tb(r,!0),{className:g,d:sI(u,l,e,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",s_({},tb(r,!0),{className:g,d:sI(l,s,f,p,h)})))};function sR(t){return(sR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sL(){return(sL=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sz(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sU(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sz(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sR(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sz(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var s$=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/lZ,f=u?o:o+i*s;return{center:lJ(e,r,l,f),circleTangency:lJ(e,r,n,f),lineTangency:lJ(e,r,l*Math.cos(s*lZ),u?o-i*s:o),theta:s}},sF=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=t.endAngle,c=T(a-i)*Math.min(Math.abs(a-i),359.999),u=i+c,l=lJ(e,r,o,i),s=lJ(e,r,o,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=lJ(e,r,n,i),h=lJ(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sq=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=T(l-u),f=s$({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=s$({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sF({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var O=s$({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),w=O.circleTangency,j=O.lineTangency,S=O.theta,P=s$({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),A=P.circleTangency,E=P.lineTangency,k=P.theta,M=c?Math.abs(u-l):Math.abs(u-l)-S-k;if(M<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(w.x,",").concat(w.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sW={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sX=function(t){var e,r=sU(sU({},sW),t),n=r.cx,o=r.cy,i=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,l=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,h=r.className;if(c<i||f===p)return null;var d=(0,x.A)("recharts-sector",h),y=c-i,v=R(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?sq({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):sF({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",sL({},tb(r,!0),{className:d,d:e,role:"img"}))},sH=["option","shapeType","propTransformer","activeClassName","isActive"];function sG(t){return(sG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sV(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sG(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sY(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(nx,r);case"trapezoid":return a().createElement(sB,r);case"sector":return a().createElement(sX,r);case"symbols":if("symbols"===e)return a().createElement(eF,r);break;default:return null}}function sZ(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sH);if((0,i.isValidElement)(r))e=(0,i.cloneElement)(r,sK(sK({},l),(0,i.isValidElement)(r)?r.props:r));else if(G()(r))e=r(l);else if(sA()(r)&&!sk()(r)){var s=(void 0===o?function(t,e){return sK(sK({},e),t)}:o)(r,l);e=a().createElement(sY,{shapeType:n,elementProps:s})}else e=a().createElement(sY,{shapeType:n,elementProps:l});return u?a().createElement(tL,{className:void 0===c?"recharts-active-shape":c},e):e}function sJ(t,e){return null!=e&&"trapezoids"in t.props}function sQ(t,e){return null!=e&&"sectors"in t.props}function s0(t,e){return null!=e&&"points"in t.props}function s1(t,e){var r,n,o=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return o&&i}function s2(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function s5(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var s3=["x","y"];function s4(t){return(s4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s6(){return(s6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s8(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s4(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s9(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,s3),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return s7(s7(s7(s7(s7({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function ft(t){return a().createElement(sZ,s6({shapeType:"rectangle",propTransformer:s9,activeClassName:"recharts-active-bar"},t))}var fe=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||tC(!1),e)}},fr=["value","background"];function fn(t){return(fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fo(){return(fo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fi(Object(r),!0).forEach(function(e){ff(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fp(n.key),n)}}function fu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fu=function(){return!!t})()}function fl(t){return(fl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fs(t,e){return(fs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ff(t,e,r){return(e=fp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fp(t){var e=function(t,e){if("object"!=fn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fn(e)?e:e+""}var fh=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=fl(e),ff(t=function(t,e){if(e&&("object"===fn(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fu()?Reflect.construct(e,r||[],fl(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),ff(t,"id",B("recharts-bar-")),ff(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),ff(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fs(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,c=r.activeBar,u=tb(this.props,!1);return t&&t.map(function(t,r){var l=r===i,s=fa(fa(fa({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tL,fo({className:"recharts-bar-rectangle"},to(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(ft,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(nf,{begin:i,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=s&&s[e];if(r){var i=U(r.x,t.x),a=U(r.y,t.y),c=U(r.width,t.width),u=U(r.height,t.height);return fa(fa({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var l=U(0,t.height)(o);return fa(fa({},t),{},{y:t.y+t.height-l,height:l})}var f=U(0,t.width)(o);return fa(fa({},t),{},{width:f})});return a().createElement(tL,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!c1()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tb(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,fr);if(!c)return null;var l=fa(fa(fa(fa(fa({},u),{},{fill:"#eee"},c),i),to(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(ft,fo({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,c=r.layout,u=th(r.children,uG);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:u3(t,e)}};return a().createElement(tL,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,y=(0,x.A)("recharts-bar",n),v=o&&o.allowDataOverflow,m=i&&i.allowDataOverflow,b=v||m,g=X()(h)?this.id:h;return a().createElement(tL,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(g)},a().createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,a().createElement(tL,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||d)&&sS.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&fc(n.prototype,e),r&&fc(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function fd(t){return(fd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fy(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fg(n.key),n)}}function fv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fv(Object(r),!0).forEach(function(e){fb(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fb(t,e,r){return(e=fg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fg(t){var e=function(t,e){if("object"!=fd(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fd(e)?e:e+""}ff(fh,"displayName","Bar"),ff(fh,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!t6.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),ff(fh,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=lf(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?fa(fa({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:i,O=l?x.scale.domain():null,w=lb({numericAxis:x}),j=th(b,sp),S=f.map(function(t,e){l?f=lp(l[s+e],O):Array.isArray(f=u3(t,m))||(f=[w,f]);var n=fe(g,fh.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],E=P[1];p=lm({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),y=null!=(S=null!=E?E:A)?S:void 0,v=h.size;var k=A-E;if(b=Number.isNaN(k)?0:k,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=T(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var _=[i.scale(f[0]),i.scale(f[1])],C=_[0],N=_[1];if(p=C,y=lm({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),v=N-C,b=h.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var I=T(v||n)*(Math.abs(n)-Math.abs(v));v+=I}}return fa(fa(fa({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lA(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fa({data:S,layout:d},p)});var fx=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fO=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fy(r.prototype,t),e&&fy(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fb(fO,"EPS",1e-4);var fw=function(t){var e=Object.keys(t).reduce(function(e,r){return fm(fm({},e),{},fb({},r,fO.create(t[r])))},{});return fm(fm({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return sl()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return sf()(t,function(t,r){return e[r].isInRange(t)})}})},fj=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fS(){return(fS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fP(t){return(fP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fA(Object(r),!0).forEach(function(e){fT(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fA(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fk(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fk=function(){return!!t})()}function fM(t){return(fM=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f_(t,e){return(f_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fT(t,e,r){return(e=fC(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fC(t){var e=function(t,e){if("object"!=fP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fP(e)?e:e+""}var fN=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fw({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return sc(t,"discard")&&!i.isInRange(a)?null:a},fI=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fM(t),function(t,e){if(e&&("object"===fP(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fk()?Reflect.construct(t,e||[],fM(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f_(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,c=t.clipPathId,u=I(e),l=I(n);if(q(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=fN(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=fE(fE({clipPath:sc(this.props,"hidden")?"url(#".concat(c,")"):void 0},tb(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tL,{className:(0,x.A)("recharts-reference-dot",y)},r.renderDot(d,v),si.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fC(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fT(fI,"displayName","ReferenceDot"),fT(fI,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fT(fI,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):G()(t)?t(e):a().createElement(re,fS({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fD=r(67367),fB=r.n(fD),fR=r(22964),fL=r.n(fR),fz=r(86451),fU=r.n(fz)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),f$=(0,i.createContext)(void 0),fF=(0,i.createContext)(void 0),fq=(0,i.createContext)(void 0),fW=(0,i.createContext)({}),fX=(0,i.createContext)(void 0),fH=(0,i.createContext)(0),fG=(0,i.createContext)(0),fV=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fU(o);return a().createElement(f$.Provider,{value:r},a().createElement(fF.Provider,{value:n},a().createElement(fW.Provider,{value:o},a().createElement(fq.Provider,{value:s},a().createElement(fX.Provider,{value:i},a().createElement(fH.Provider,{value:l},a().createElement(fG.Provider,{value:u},c)))))))},fK=function(t){var e=(0,i.useContext)(f$);null==e&&tC(!1);var r=e[t];return null==r&&tC(!1),r},fY=function(){var t=(0,i.useContext)(fF);return fL()(t,function(t){return sf()(t.domain,Number.isFinite)})||L(t)},fZ=function(t){var e=(0,i.useContext)(fF);null==e&&tC(!1);var r=e[t];return null==r&&tC(!1),r},fJ=function(){return(0,i.useContext)(fG)},fQ=function(){return(0,i.useContext)(fH)};function f0(t){return(f0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f1(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f1=function(){return!!t})()}function f2(t){return(f2=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f5(t,e){return(f5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function f3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f4(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f3(Object(r),!0).forEach(function(e){f6(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function f6(t,e,r){return(e=f8(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f8(t){var e=function(t,e){if("object"!=f0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f0(e)?e:e+""}function f7(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f9(){return(f9=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var pt=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):G()(t)?t(e):a().createElement("line",f9({},e,{className:"recharts-reference-line-line"}))},pe=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(sc(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(sc(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return sc(u,"discard")&&fB()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pr(t){var e,r=t.x,n=t.y,o=t.segment,c=t.xAxisId,u=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,i.useContext)(fX),h=fK(c),d=fZ(u),y=(0,i.useContext)(fq);if(!p||!y)return null;q(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=pe(fw({x:h.scale,y:d.scale}),I(r),I(n),o&&2===o.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return f7(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f7(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,O=b.y,w=m[1],j=w.x,S=w.y,P=f4(f4({clipPath:sc(t,"hidden")?"url(#".concat(p,")"):void 0},tb(t,!0)),{},{x1:g,y1:O,x2:j,y2:S});return a().createElement(tL,{className:(0,x.A)("recharts-reference-line",s)},pt(l,P),si.renderCallByParent(t,fx({x:(e={x1:g,y1:O,x2:j,y2:S}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var pn=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f2(t),function(t,e){if(e&&("object"===f0(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f1()?Reflect.construct(t,e||[],f2(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f5(r,t),e=[{key:"render",value:function(){return a().createElement(pr,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f8(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function po(){return(po=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pi(t){return(pi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pa(Object(r),!0).forEach(function(e){pf(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pa(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}f6(pn,"displayName","ReferenceLine"),f6(pn,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pu=function(){return!!t})()}function pl(t){return(pl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ps(t,e){return(ps=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pf(t,e,r){return(e=pp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pp(t){var e=function(t,e){if("object"!=pi(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pi(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pi(e)?e:e+""}var ph=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fw({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!sc(o,"discard")||f.isInRange(p)&&f.isInRange(h)?fx(p,h):null},pd=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=pl(t),function(t,e){if(e&&("object"===pi(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pu()?Reflect.construct(t,e||[],pl(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ps(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;q(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=I(e),f=I(n),p=I(o),h=I(i),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=ph(s,f,p,h,this.props);if(!y&&!d)return null;var v=sc(this.props,"hidden")?"url(#".concat(l,")"):void 0;return a().createElement(tL,{className:(0,x.A)("recharts-reference-area",c)},r.renderRect(d,pc(pc({clipPath:v},tb(this.props,!0)),y)),si.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pp(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function py(t){return function(t){if(Array.isArray(t))return pv(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pv(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pv(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pv(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pf(pd,"displayName","ReferenceArea"),pf(pd,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pf(pd,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):G()(t)?t(e):a().createElement(nx,po({},e,{className:"recharts-reference-area-rect"}))});var pm=function(t,e,r,n,o){var i=th(t,pn),a=th(t,fI),c=[].concat(py(i),py(a)),u=th(t,pd),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&sc(e.props,"extendDomain")&&N(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&sc(e.props,"extendDomain")&&N(e.props[p])&&N(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return N(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pb=r(11117),pg=new(r.n(pb)()),px="recharts.syncMouseEvents";function pO(t){return(pO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pw(t,e,r){return(e=pj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pj(t){var e=function(t,e){if("object"!=pO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pO(e)?e:e+""}var pS=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");pw(this,"activeIndex",0),pw(this,"coordinateList",[]),pw(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pj(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pP(){}function pA(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pE(t){this._context=t}function pk(t){this._context=t}function pM(t){this._context=t}pE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pA(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pA(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pk.prototype={areaStart:pP,areaEnd:pP,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pA(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pM.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pA(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class p_{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pT(t){this._context=t}function pC(t){this._context=t}function pN(t){return new pC(t)}pT.prototype={areaStart:pP,areaEnd:pP,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pI(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pD(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pB(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pR(t){this._context=t}function pL(t){this._context=new pz(t)}function pz(t){this._context=t}function pU(t){this._context=t}function p$(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pF(t,e){this._context=t,this._t=e}function pq(t){return t[0]}function pW(t){return t[1]}function pX(t,e){var r=eE(!0),n=null,o=pN,i=null,a=eN(c);function c(c){var u,l,s,f=(c=cD(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?pq:eE(t),e="function"==typeof e?e:void 0===e?pW:eE(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eE(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eE(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eE(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pH(t,e,r){var n=null,o=eE(!0),i=null,a=pN,c=null,u=eN(l);function l(l){var s,f,p,h,d,y=(l=cD(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return pX().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?pq:eE(+t),e="function"==typeof e?e:void 0===e?eE(0):eE(+e),r="function"==typeof r?r:void 0===r?pW:eE(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:eE(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:eE(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eE(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:eE(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:eE(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eE(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:eE(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function pG(t){return(pG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pV(){return(pV=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pY(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pK(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=pG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pG(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pC.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pR.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pB(this,this._t0,pD(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pB(this,pD(this,r=pI(this,t,e)),r);break;default:pB(this,this._t0,r=pI(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pL.prototype=Object.create(pR.prototype)).point=function(t,e){pR.prototype.point.call(this,e,t)},pz.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},pU.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=p$(t),o=p$(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pF.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var pZ={curveBasisClosed:function(t){return new pk(t)},curveBasisOpen:function(t){return new pM(t)},curveBasis:function(t){return new pE(t)},curveBumpX:function(t){return new p_(t,!0)},curveBumpY:function(t){return new p_(t,!1)},curveLinearClosed:function(t){return new pT(t)},curveLinear:pN,curveMonotoneX:function(t){return new pR(t)},curveMonotoneY:function(t){return new pL(t)},curveNatural:function(t){return new pU(t)},curveStep:function(t){return new pF(t,.5)},curveStepAfter:function(t){return new pF(t,1)},curveStepBefore:function(t){return new pF(t,0)}},pJ=function(t){return t.x===+t.x&&t.y===+t.y},pQ=function(t){return t.x},p0=function(t){return t.y},p1=function(t,e){if(G()(t))return t;var r="curve".concat(ef()(t));return("curveMonotone"===r||"curveBump"===r)&&e?pZ["".concat(r).concat("vertical"===e?"Y":"X")]:pZ[r]||pN},p2=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=p1(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pJ(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pJ(t)}):i,p=s.map(function(t,e){return pY(pY({},t),{},{base:f[e]})});return(e="vertical"===a?pH().y(p0).x1(pQ).x0(function(t){return t.base.x}):pH().x(pQ).y1(p0).y0(function(t){return t.base.y})).defined(pJ).curve(l),e(p)}return(e="vertical"===a&&N(i)?pH().y(p0).x1(pQ).x0(i):N(i)?pH().x(pQ).y1(p0).y0(i):pX().x(pQ).y(p0)).defined(pJ).curve(l),e(s)},p5=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?p2(t):n;return a().createElement("path",pV({},tb(t,!1),tn(t),{className:(0,x.A)("recharts-curve",e),d:i,ref:o}))};function p3(t){return(p3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p4=["x","y","top","left","width","height","className"];function p6(){return(p6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var p7=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,c=void 0===i?0:i,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p8(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p3(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,p4));return N(r)&&N(o)&&N(f)&&N(h)&&N(c)&&N(l)?a().createElement("path",p6({},tb(y,!0),{className:(0,x.A)("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(o,"h").concat(f)})):null};function p9(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[lJ(e,r,n,o),lJ(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function ht(t){return(ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function he(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?he(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=ht(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ht(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ht(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):he(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hn(t){var e,r,n,o,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(n=a.type.defaultProps)?void 0:n.cursor;if(!a||!v||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var m=p5;if("ScatterChart"===y)o=l,m=p7;else if("BarChart"===y)e=h/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},m=nx;else if("radial"===d){var b=p9(l),g=b.cx,O=b.cy,w=b.radius;o={cx:g,cy:O,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=sX}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return p9(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=lJ(c,u,l,f),h=lJ(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(d,l,f)},m=p5;var j=hr(hr(hr(hr({stroke:"#ccc",pointerEvents:"none"},f),o),tb(v,!1)),{},{payload:s,payloadIndex:p,className:(0,x.A)("recharts-tooltip-cursor",v.className)});return(0,i.isValidElement)(v)?(0,i.cloneElement)(v,j):(0,i.createElement)(m,j)}var ho=["item"],hi=["children","className","width","height","style","compact","title","desc"];function ha(t){return(ha="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hc(){return(hc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hu(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hd(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hl(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hs=function(){return!!t})()}function hf(t){return(hf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hp(t,e){return(hp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hh(t){return function(t){if(Array.isArray(t))return hy(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hd(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hd(t,e){if(t){if("string"==typeof t)return hy(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hy(t,e)}}function hy(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hv(Object(r),!0).forEach(function(e){hb(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hb(t,e,r){return(e=hg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hg(t){var e=function(t,e){if("object"!=ha(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ha(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ha(e)?e:e+""}var hx={xAxis:["bottom","top"],yAxis:["left","right"]},hO={width:"100%",height:"100%"},hw={x:0,y:0};function hj(t){return t}var hS=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return hm(hm(hm({},n),lJ(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return hm(hm(hm({},n),lJ(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hw},hP=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hh(t),hh(r)):t},[]);return i.length>0?i:t&&t.length&&N(n)&&N(o)?t.slice(n,o+1):[]};function hA(t){return"number"===t?[0,"auto"]:void 0}var hE=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=hP(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!=(u=c.props.data)?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?$(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(hh(o),[lA(c,l)]):o},[])},hk=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=u6(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hE(t,e,l,s),p=hS(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hM=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=lo(l,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hm(hm({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,O=h[i];if(e[O])return e;var w=hP(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i])===O}),dataStartIndex:c,dataEndIndex:u}),j=w.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&N(n)&&N(o))return!0}return!1})(h.domain,v,d)&&(A=lj(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(k=u4(w,y,"category")));var S=hA(d);if(!A||0===A.length){var P,A,E,k,M,_=null!=(M=h.domain)?M:S;if(y){if(A=u4(w,y,d),"category"===d&&p){var T=z(A);m&&T?(E=A,A=tM()(0,j)):m||(A=lP(_,A,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hh(t),[e])},[]))}else if("category"===d)A=m?A.filter(function(t){return""!==t&&!X()(t)}):lP(_,A,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||X()(e)?t:[].concat(hh(t),[e])},[]);else if("number"===d){var C=lr(w,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===O&&(x||!o)}),y,o,l);C&&(A=C)}p&&("number"===d||"auto"!==b)&&(k=u4(w,y,"category"))}else A=p?tM()(0,j):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:lx(a[O].stackGroups,c,u):ln(w,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,l,!0);"number"===d?(A=pm(s,A,O,o,g),_&&(A=lj(_,A,v))):"category"===d&&_&&A.every(function(t){return _.indexOf(t)>=0})&&(A=_)}return hm(hm({},e),{},hb({},O,hm(hm({},h),{},{axisType:o,domain:A,categoricalDomain:k,duplicateDomain:E,originalDomain:null!=(P=h.domain)?P:S,isCategorical:p,layout:l})))},{})},h_=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hP(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=lo(l,o),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hm(hm({},e.type.defaultProps),e.props):e.props)[i],m=hA("number");return t[v]?t:(d++,y=h?tM()(0,p):a&&a[v]&&a[v].hasStack?pm(s,y=lx(a[v].stackGroups,c,u),v,o):pm(s,y=lj(m,ln(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null==(e=t.type.defaultProps)?void 0:e[i],o="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),hm(hm({},t),{},hb({},v,hm(hm({axisType:o},n.defaultProps),{},{hide:!0,orientation:k()(hx,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hT=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=th(l,o),p={};return f&&f.length?p=hM(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=h_(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},hC=function(t){var e=L(t),r=la(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tT()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lS(e,r)}},hN=function(t){var e=t.children,r=t.defaultShowTooltip,n=td(e,lX),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hI=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hD=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=td(s,lX),h=td(s,e9),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hm(hm({},t),{},hb({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:hm(hm({},t),{},hb({},n,k()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hm(hm({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||lX.defaultProps.height),h&&e&&(v=lt(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hm(hm({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},hB=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hI(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tl(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hP(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?hm(hm({},r.type.defaultProps),r.props):r.props,O=x.dataKey,w=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||tC(!1);var i=n[o];return hm(hm({},t),{},hb(hb({},r.axisType,i),"".concat(r.axisType,"Ticks"),la(i)))},{}),A=P[v],E=P["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&lg(r,n[j].stackGroups),M=tl(r.type).indexOf("Bar")>=0,_=lS(A,E),T=[],C=m&&u7({barSize:u,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(M){var N,I,D=X()(w)?h:w,B=null!=(N=null!=(I=lS(A,E,!0))?I:D)?N:0;T=u9({barGap:f,barCategoryGap:p,bandSize:B!==_?B:_,sizeList:C[S],maxBarSize:D}),B!==_&&(T=T.map(function(t){return hm(hm({},t),{},{position:hm(hm({},t.position),{},{offset:t.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:hm(hm({},R(hm(hm({},P),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:_,barPosition:T,offset:o,stackedData:k,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},hb(hb(hb({key:r.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",i)),childIndex:tp(t.children).indexOf(r),item:r})}),b},d=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!ty({props:o}))return null;var u=o.children,s=o.layout,p=o.stackOffset,d=o.data,y=o.reverseStackOrder,v=hI(s),m=v.numericAxisName,b=v.cateAxisName,g=th(u,r),x=ly(d,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),O=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hm(hm({},t),{},hb({},r,hT(o,hm(hm({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=hD(hm(hm({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=f(o,O[t],w,t.replace("Map",""),e)});var j=hC(O["".concat(b,"Map")]),S=h(o,hm(hm({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:w}));return hm(hm({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},y=function(t){var r;function n(t){var r,o,c,u,l;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return u=n,l=[t],u=hf(u),hb(c=function(t,e){if(e&&("object"===ha(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hs()?Reflect.construct(u,l||[],hf(this).constructor):u.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hb(c,"accessibilityManager",new pS),hb(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;c.setState(hm({legendBBox:t},d({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:o},hm(hm({},c.state),{},{legendBBox:t}))))}}),hb(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),hb(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return hm({dataStartIndex:e,dataEndIndex:r},d({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hb(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=hm(hm({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;G()(n)&&n(r,t)}}),hb(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?hm(hm({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;G()(n)&&n(r,t)}),hb(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hb(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),hb(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),hb(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;G()(r)&&r(e,t)}),hb(c,"handleOuterEvent",function(t){var e,r,n=tw(t),o=k()(c.props,"".concat(n));n&&G()(o)&&o(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),hb(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=hm(hm({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;G()(n)&&n(r,t)}}),hb(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;G()(e)&&e(c.getMouseInfo(t),t)}),hb(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;G()(e)&&e(c.getMouseInfo(t),t)}),hb(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hb(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),hb(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),hb(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;G()(e)&&e(c.getMouseInfo(t),t)}),hb(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;G()(e)&&e(c.getMouseInfo(t),t)}),hb(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pg.emit(px,c.props.syncId,t,c.eventEmitterSymbol)}),hb(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,o=c.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(hm({dataStartIndex:i,dataEndIndex:a},d({props:c.props,dataStartIndex:i,dataEndIndex:a,updateId:o},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=hm(hm({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,x=hE(c.state,c.props.data,s),O=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:hw;c.setState(hm(hm({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else c.setState(t)}),hb(c,"renderCursor",function(t){var r,n=c.state,o=n.isTooltipActive,i=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!=(r=t.props.active)?r:o,d=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(hn,{key:y,activeCoordinate:i,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hb(c,"renderPolarAxis",function(t,e,r){var n=k()(t,"type.axisType"),o=k()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?hm(hm({},a),t.props):t.props,l=o&&o[u["".concat(n,"Id")]];return(0,i.cloneElement)(t,hm(hm({},l),{},{className:(0,x.A)(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:la(l,!0)}))}),hb(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=L(u),f=L(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(t,{polarAngles:Array.isArray(n)?n:la(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:la(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hb(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,o=e.height,a=c.props.margin||{},u=uZ({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=hl(u,ho);return(0,i.cloneElement)(l,hm(hm({},f),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),hb(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,o=td(r,el);if(!o)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=o.props.active)?t:u;return(0,i.cloneElement)(o,{viewBox:hm(hm({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hb(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,o=c.state,a=o.offset,u=o.dataStartIndex,l=o.dataEndIndex,s=o.updateId;return(0,i.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lu(c.handleBrushChange,t.props.onChange),data:n,x:N(t.props.x)?t.props.x:a.left,y:N(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:N(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hb(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,o=c.state,a=o.xAxisMap,u=o.yAxisMap,l=o.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,i.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hb(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hm(hm({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hm(hm({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:u8(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tb(s,!1)),tn(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(s,hm(hm({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),hb(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var o=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=td(c.props.children,el),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hm(hm({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,O=m.activeShape,w=!!(!g&&u&&p&&(b||x||O)),j={};"axis"!==o&&p&&"click"===p.props.trigger?j={onClick:lu(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(j={onMouseLeave:lu(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lu(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,i.cloneElement)(t,hm(hm({},n.props),j));if(w)if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var P="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());E=$(d,P,f),k=y&&v&&$(v,P,f)}else E=null==d?void 0:d[s],k=y&&v&&v[s];if(O||x){var A=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,i.cloneElement)(t,hm(hm(hm({},n.props),j),{},{activeIndex:A})),null,null]}if(!X()(E))return[S].concat(hh(c.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:s,isRange:y})))}else{var E,k,M,_=(null!=(M=c.getItemByXY(c.state.activeCoordinate))?M:{graphicalItem:S}).graphicalItem,T=_.item,C=void 0===T?t:T,N=_.childIndex,I=hm(hm(hm({},n.props),j),{},{activeIndex:N});return[(0,i.cloneElement)(C,I),null,null]}return y?[S,null,null]:[S,null]}),hb(c,"renderCustomized",function(t,e,r){return(0,i.cloneElement)(t,hm(hm({key:"recharts-customized-".concat(r)},c.props),c.state))}),hb(c,"renderMap",{CartesianGrid:{handler:hj,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:hj},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:hj},YAxis:{handler:hj},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:B("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=w()(c.triggeredAfterMouseMove,null!=(o=t.throttleDelay)?o:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hp(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=td(e,el);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hE(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hm(hm({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tg([td(t.children,el)],[td(this.props.children,el)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=td(this.props.children,el);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hk(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=L(u).scale,h=L(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return hm(hm({},o),{},{xValue:d,yValue:y},f)}return f?hm(hm({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?l5({x:o,y:i},L(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=td(t,el),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hm(hm({},tn(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pg.on(px,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pg.removeListener(px,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===tl(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hu(e,2),n=r[0],o=r[1];return hm(hm({},t),{},hb({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hu(e,2),n=r[0],o=r[1];return hm(hm({},t),{},hb({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hm(hm({},u.type.defaultProps),u.props):u.props,s=tl(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return nb(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return l5(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sJ(a,n)||sQ(a,n)||s0(a,n)){var h=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sJ(i,o)?e="trapezoids":sQ(i,o)?e="sectors":s0(i,o)&&(e="points"),e),u=sJ(i,o)?null==(r=o.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:sQ(i,o)?null==(n=o.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:s0(i,o)?o.payload:{},l=a.filter(function(t,e){var r=c1()(u,t),n=i.props[c].filter(function(t){var e;return(sJ(i,o)?e=s1:sQ(i,o)?e=s2:s0(i,o)&&(e=s5),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hm(hm({},a),{},{childIndex:d}),payload:s0(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!ty(this))return null;var n=this.props,o=n.children,i=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=tb(hl(n,hi),!1);if(s)return a().createElement(fV,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tD,hc({},h,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),tO(o,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!=(t=this.props.tabIndex)?t:0,h.role=null!=(e=this.props.role)?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return a().createElement(fV,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",hc({className:(0,x.A)("recharts-wrapper",i),style:hm({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(t){r.container=t}}),a().createElement(tD,hc({},h,{width:c,height:u,title:f,desc:p,style:hO}),this.renderClipPath(),tO(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hg(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);hb(y,"displayName",e),hb(y,"defaultProps",hm({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hb(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hN(t);return hm(hm(hm({},p),{},{updateId:0},d(hm(hm({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!Z(l,e.prevMargin)){var h=hN(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=hm(hm({},hk(e,n,c)),{},{updateId:e.updateId+1}),m=hm(hm(hm({},h),y),v);return hm(hm(hm({},m),d(hm({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!tg(o,e.prevChildren)){var b,g,x,O,w=td(o,lX),j=w&&null!=(b=null==(g=w.props)?void 0:g.startIndex)?b:s,S=w&&null!=(x=null==(O=w.props)?void 0:O.endIndex)?x:f,P=X()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hm(hm({updateId:P},d(hm(hm({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),hb(y,"renderActiveDot",function(t,e,r){var n;return n=(0,i.isValidElement)(t)?(0,i.cloneElement)(t,e):G()(t)?t(e):a().createElement(re,e),a().createElement(tL,{className:"recharts-active-dot",key:r},n)});var v=(0,i.forwardRef)(function(t,e){return a().createElement(y,hc({},t,{ref:e}))});return v.displayName=y.displayName,v};function hR(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e)if(void 0!==r&&!0!==r(t[o]))return;else n.push(t[o]);return n}function hL(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function hz(t){return(hz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hU(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=hz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hz(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hF(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(N(h)||t6.isSsr)return hR(l,("number"==typeof h&&N(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nR(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var o,i=G()(d)?d(t.value,n):t.value;return"width"===b?(o=nR(i,{fontSize:e,letterSpacing:r}),fj({width:o.width+g.width,height:o.height+g.height},v)):nR(i,{fontSize:e,letterSpacing:r})[b]},O=l.length>=2?T(l[1].coordinate-l[0].coordinate):1,w=(n="width"===b,o=s.x,i=s.y,a=s.width,c=s.height,1===O?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:hR(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===l||hL(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+o),l+=s)}())return i.v;return[]}(O,w,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=h$(h$({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hL(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=h$(h$({},s),{},{isShow:!0}))}for(var h=i?c-1:c,d=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=h$(h$({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=h$(h$({},i),{},{tickCoord:i.coordinate});hL(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=h$(h$({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(O,w,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=h$(h$({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=h$(h$({},l),{},{tickCoord:l.coordinate});hL(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=h$(h$({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(O,w,x,l,f)).filter(function(t){return t.isShow})}var hq=["viewBox"],hW=["viewBox"],hX=["ticks"];function hH(t){return(hH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hG(){return(hG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hV(Object(r),!0).forEach(function(e){h1(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hY(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hZ(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h2(n.key),n)}}function hJ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hJ=function(){return!!t})()}function hQ(t){return(hQ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h0(t,e){return(h0=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h1(t,e,r){return(e=h2(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h2(t){var e=function(t,e){if("object"!=hH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hH(e)?e:e+""}var h5=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=hQ(r),(e=function(t,e){if(e&&("object"===hH(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hJ()?Reflect.construct(r,o||[],hQ(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&h0(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=hY(t,hq),o=this.props,i=o.viewBox,a=hY(o,hW);return!Z(r,i)||!Z(n,a)||!Z(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=N(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+!d*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,c=t.mirror,u=t.axisLine,l=hK(hK(hK({},tb(this.props,!1)),tb(u,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var s=+("top"===i&&!c||"bottom"===i&&c);l=hK(hK({},l),{},{x1:e,y1:r+s*o,x2:e+n,y2:r+s*o})}else{var f=+("left"===i&&!c||"right"===i&&c);l=hK(hK({},l),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+o})}return a().createElement("line",hG({},l,{className:(0,x.A)("recharts-cartesian-axis-line",k()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,c=i.tickLine,u=i.stroke,l=i.tick,s=i.tickFormatter,f=i.unit,p=hF(hK(hK({},this.props),{},{ticks:t}),e,r),h=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=tb(this.props,!1),v=tb(l,!1),m=hK(hK({},y),{},{fill:"none"},tb(c,!1)),b=p.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,b=r.tick,g=hK(hK(hK(hK({textAnchor:h,verticalAnchor:d},y),{},{stroke:"none",fill:u},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:s});return a().createElement(tL,hG({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},to(o.props,t,e)),c&&a().createElement("line",hG({},m,i,{className:(0,x.A)("recharts-cartesian-axis-tick-line",k()(c,"className"))})),l&&n.renderTickItem(l,g,"".concat(G()(s)?s(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=hY(u,hX),f=l;return(G()(i)&&(f=i(l&&l.length>0?this.props:s)),n<=0||o<=0||!f||!f.length)?null:a().createElement(tL,{className:(0,x.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),si.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):G()(t)?t(e):a().createElement(ot,hG({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&hZ(n.prototype,e),r&&hZ(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function h3(t){return(h3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}h1(h5,"displayName","CartesianAxis"),h1(h5,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function h4(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h4=function(){return!!t})()}function h6(t){return(h6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h8(t,e){return(h8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h7(t,e,r){return(e=h9(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h9(t){var e=function(t,e){if("object"!=h3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h3(e)?e:e+""}function dt(){return(dt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function de(t){var e=t.xAxisId,r=fJ(),n=fQ(),o=fK(e);return null==o?null:a().createElement(h5,dt({},o,{className:(0,x.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return la(t,!0)}}))}var dr=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=h6(t),function(t,e){if(e&&("object"===h3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h4()?Reflect.construct(t,e||[],h6(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&h8(r,t),e=[{key:"render",value:function(){return a().createElement(de,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h9(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function dn(t){return(dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}h7(dr,"displayName","XAxis"),h7(dr,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function di(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(di=function(){return!!t})()}function da(t){return(da=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dc(t,e){return(dc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function du(t,e,r){return(e=dl(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dl(t){var e=function(t,e){if("object"!=dn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dn(e)?e:e+""}function ds(){return(ds=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var df=function(t){var e=t.yAxisId,r=fJ(),n=fQ(),o=fZ(e);return null==o?null:a().createElement(h5,ds({},o,{className:(0,x.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return la(t,!0)}}))},dp=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=da(t),function(t,e){if(e&&("object"===dn(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,di()?Reflect.construct(t,e||[],da(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dc(r,t),e=[{key:"render",value:function(){return a().createElement(df,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dl(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);du(dp,"displayName","YAxis"),du(dp,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dh=hB({chartName:"BarChart",GraphicalChild:fh,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:dr},{axisType:"yAxis",AxisComp:dp}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!td(u,fh);return l.reduce(function(i,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(F);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*E/2),"no-gap"===y.padding){var k=R(t.barCategoryGap,A*E),M=A*E/2;u=M-k-(M-k)/E*k}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,O&&(l=[l[1],l[0]]);var _=ll(y,o,f),T=_.scale,C=_.realScaleType;T.domain(m).range(l),ls(T);var N=lv(T,fm(fm({},y),{},{realScaleType:C}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[w]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[w]-d*y.width,h=r.top);var I=fm(fm(fm({},y),N),{},{realScaleType:C,x:p,y:h,scale:T,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return I.bandSize=lS(I,N),y.hide||"xAxis"!==n?y.hide||(s[w]+=(d?-1:1)*I.width):s[w]+=(d?-1:1)*I.height,fm(fm({},i),{},fb({},a,I))},{})}}),dd=["x1","y1","x2","y2","key"],dy=["offset"];function dv(t){return(dv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function db(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dm(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=dv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dv(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dm(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dg(){return(dg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dx(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var dO=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:o,ry:u,width:i,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dw(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(G()(t))r=t(e);else{var n=e.x1,o=e.y1,i=e.x2,c=e.y2,u=e.key,l=tb(dx(e,dd),!1),s=(l.offset,dx(l,dy));r=a().createElement("line",dg({},s,{x1:n,y1:o,x2:i,y2:c,fill:"none",key:u}))}return r}function dj(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,i=t.horizontalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dw(o,db(db({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function dS(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,i=t.verticalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dw(o,db(db({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function dP(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:o+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:i,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function dA(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,i=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:o+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:i,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dE=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return li(hF(db(db(db({},h5.defaultProps),r),{},{ticks:la(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},dk=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return li(hF(db(db(db({},h5.defaultProps),r),{},{ticks:la(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},dM={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function d_(t){var e,r,n,o,c,u,l=fJ(),s=fQ(),f=(0,i.useContext)(fW),p=db(db({},t),{},{stroke:null!=(e=t.stroke)?e:dM.stroke,fill:null!=(r=t.fill)?r:dM.fill,horizontal:null!=(n=t.horizontal)?n:dM.horizontal,horizontalFill:null!=(o=t.horizontalFill)?o:dM.horizontalFill,vertical:null!=(c=t.vertical)?c:dM.vertical,verticalFill:null!=(u=t.verticalFill)?u:dM.verticalFill,x:N(t.x)?t.x:f.left,y:N(t.y)?t.y:f.top,width:N(t.width)?t.width:f.width,height:N(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=L((0,i.useContext)(f$)),O=fY();if(!N(y)||y<=0||!N(v)||v<=0||!N(h)||h!==+h||!N(d)||d!==+d)return null;var w=p.verticalCoordinatesGenerator||dE,j=p.horizontalCoordinatesGenerator||dk,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&G()(j)){var A=b&&b.length,E=j({yAxis:O?db(db({},O),{},{ticks:A?b:O.ticks}):void 0,width:l,height:s,offset:f},!!A||m);q(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dv(E),"]")),Array.isArray(E)&&(S=E)}if((!P||!P.length)&&G()(w)){var k=g&&g.length,M=w({xAxis:x?db(db({},x),{},{ticks:k?g:x.ticks}):void 0,width:l,height:s,offset:f},!!k||m);q(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dv(M),"]")),Array.isArray(M)&&(P=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dO,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(dj,dg({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:O})),a().createElement(dS,dg({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:O})),a().createElement(dP,dg({},p,{horizontalPoints:S})),a().createElement(dA,dg({},p,{verticalPoints:P})))}d_.displayName="CartesianGrid";var dT=["points","className","baseLinePoints","connectNulls"];function dC(){return(dC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dN(t){return function(t){if(Array.isArray(t))return dI(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return dI(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dI(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dI(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var dD=function(t){return t&&t.x===+t.x&&t.y===+t.y},dB=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){dD(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),dD(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},dR=function(t,e){var r=dB(t);e&&(r=[r.reduce(function(t,e){return[].concat(dN(t),dN(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},dL=function(t,e,r){var n=dR(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(dR(e.reverse(),r).slice(1))},dz=function(t){var e=t.points,r=t.className,n=t.baseLinePoints,o=t.connectNulls,i=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,dT);if(!e||!e.length)return null;var c=(0,x.A)("recharts-polygon",r);if(n&&n.length){var u=i.stroke&&"none"!==i.stroke,l=dL(e,n,o);return a().createElement("g",{className:c},a().createElement("path",dC({},tb(i,!0),{fill:"Z"===l.slice(-1)?i.fill:"none",stroke:"none",d:l})),u?a().createElement("path",dC({},tb(i,!0),{fill:"none",d:dR(e,o)})):null,u?a().createElement("path",dC({},tb(i,!0),{fill:"none",d:dR(n,o)})):null)}var s=dR(e,o);return a().createElement("path",dC({},tb(i,!0),{fill:"Z"===s.slice(-1)?i.fill:"none",className:c,d:s}))};function dU(t){return(dU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d$(){return(d$=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dF(Object(r),!0).forEach(function(e){dV(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dW(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dK(n.key),n)}}function dX(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dX=function(){return!!t})()}function dH(t){return(dH=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dG(t,e){return(dG=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dV(t,e,r){return(e=dK(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dK(t){var e=function(t,e){if("object"!=dU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dU(e)?e:e+""}var dY=Math.PI/180,dZ=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=dH(t),function(t,e){if(e&&("object"===dU(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dX()?Reflect.construct(t,e||[],dH(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&dG(n,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize,c=lJ(r,n,o,t.coordinate),u=lJ(r,n,o+("inner"===i?-1:1)*(a||8),t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*dY);return r>1e-5?"outer"===e?"start":"end":r<-1e-5?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.radius,o=t.axisLine,i=t.axisLineType,c=dq(dq({},tb(this.props,!1)),{},{fill:"none"},tb(o,!1));if("circle"===i)return a().createElement(re,d$({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:r,r:n}));var u=this.props.ticks.map(function(t){return lJ(e,r,n,t.coordinate)});return a().createElement(dz,d$({className:"recharts-polar-angle-axis-line"},c,{points:u}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.tickLine,c=e.tickFormatter,u=e.stroke,l=tb(this.props,!1),s=tb(o,!1),f=dq(dq({},l),{},{fill:"none"},tb(i,!1)),p=r.map(function(e,r){var p=t.getTickLineCoord(e),h=dq(dq(dq({textAnchor:t.getTickTextAnchor(e)},l),{},{stroke:"none",fill:u},s),{},{index:r,payload:e,x:p.x2,y:p.y2});return a().createElement(tL,d$({className:(0,x.A)("recharts-polar-angle-axis-tick",l3(o)),key:"tick-".concat(e.coordinate)},to(t.props,e,r)),i&&a().createElement("line",d$({className:"recharts-polar-angle-axis-tick-line"},f,p)),o&&n.renderTickItem(o,h,c?c(e.value,r):e.value))});return a().createElement(tL,{className:"recharts-polar-angle-axis-ticks"},p)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,n=t.axisLine;return!(r<=0)&&e&&e.length?a().createElement(tL,{className:(0,x.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):G()(t)?t(e):a().createElement(ot,d$({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],e&&dW(n.prototype,e),r&&dW(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);dV(dZ,"displayName","PolarAngleAxis"),dV(dZ,"axisType","angleAxis"),dV(dZ,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var dJ=r(57088),dQ=r.n(dJ),d0=r(10034),d1=r.n(d0),d2=["cx","cy","angle","ticks","axisLine"],d5=["ticks","tick","angle","tickFormatter","stroke"];function d3(t){return(d3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d4(){return(d4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d6(Object(r),!0).forEach(function(e){yn(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d7(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function d9(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yo(n.key),n)}}function yt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(yt=function(){return!!t})()}function ye(t){return(ye=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yr(t,e){return(yr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yn(t,e,r){return(e=yo(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yo(t){var e=function(t,e){if("object"!=d3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d3(e)?e:e+""}var yi=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=ye(t),function(t,e){if(e&&("object"===d3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yt()?Reflect.construct(t,e||[],ye(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&yr(n,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle;return lJ(r.cx,r.cy,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=dQ()(o,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:d1()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=t.axisLine,c=d7(t,d2),u=o.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),l=lJ(e,r,u[0],n),s=lJ(e,r,u[1],n),f=d8(d8(d8({},tb(c,!1)),{},{fill:"none"},tb(i,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return a().createElement("line",d4({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.angle,c=e.tickFormatter,u=e.stroke,l=d7(e,d5),s=this.getTickTextAnchor(),f=tb(l,!1),p=tb(o,!1),h=r.map(function(e,r){var l=t.getTickValueCoord(e),h=d8(d8(d8(d8({textAnchor:s,transform:"rotate(".concat(90-i,", ").concat(l.x,", ").concat(l.y,")")},f),{},{stroke:"none",fill:u},p),{},{index:r},l),{},{payload:e});return a().createElement(tL,d4({className:(0,x.A)("recharts-polar-radius-axis-tick",l3(o)),key:"tick-".concat(e.coordinate)},to(t.props,e,r)),n.renderTickItem(o,h,c?c(e.value,r):e.value))});return a().createElement(tL,{className:"recharts-polar-radius-axis-ticks"},h)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,n=t.tick;return e&&e.length?a().createElement(tL,{className:(0,x.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),si.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):G()(t)?t(e):a().createElement(ot,d4({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],e&&d9(n.prototype,e),r&&d9(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function ya(t){return(ya="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yc(){return(yc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function yu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function yl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yu(Object(r),!0).forEach(function(e){yd(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yu(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ys(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yy(n.key),n)}}function yf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(yf=function(){return!!t})()}function yp(t){return(yp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yh(t,e){return(yh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yd(t,e,r){return(e=yy(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yy(t){var e=function(t,e){if("object"!=ya(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ya(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ya(e)?e:e+""}yn(yi,"displayName","PolarRadiusAxis"),yn(yi,"axisType","radiusAxis"),yn(yi,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var yv=function(t){var e,r;function n(t){var e,r,o;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,o=[t],r=yp(r),yd(e=function(t,e){if(e&&("object"===ya(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,yf()?Reflect.construct(r,o||[],yp(this).constructor):r.apply(this,o)),"pieRef",null),yd(e,"sectorRefs",[]),yd(e,"id",B("recharts-pie-")),yd(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),G()(t)&&t()}),yd(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),G()(t)&&t()}),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&yh(n,t),e=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,r=e.label,o=e.labelLine,i=e.dataKey,c=e.valueKey,u=tb(this.props,!1),l=tb(r,!1),s=tb(o,!1),f=r&&r.offsetRadius||20,p=t.map(function(t,e){var p=(t.startAngle+t.endAngle)/2,h=lJ(t.cx,t.cy,t.outerRadius+f,p),d=yl(yl(yl(yl({},u),t),{},{stroke:"none"},l),{},{index:e,textAnchor:n.getTextAnchor(h.x,t.cx)},h),y=yl(yl(yl(yl({},u),t),{},{fill:"none",stroke:t.fill},s),{},{index:e,points:[lJ(t.cx,t.cy,t.outerRadius,p),h]}),v=i;return X()(i)&&X()(c)?v="value":X()(i)&&(v=c),a().createElement(tL,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,d,u3(t,v)))});return a().createElement(tL,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return t.map(function(r,c){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==t.length)return null;var u=e.isActiveIndex(c),l=i&&e.hasActiveIndex()?i:null,s=yl(yl({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return a().createElement(tL,yc({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},to(e.props,r,c),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),a().createElement(sZ,yc({option:u?n:l,isActive:u,shapeType:"sector"},s)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,o=e.animationBegin,i=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state,s=l.prevSectors,f=l.prevIsAnimationActive;return a().createElement(nf,{begin:o,duration:i,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var n=e.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(t,e){var r=s&&s[e],a=e>0?k()(t,"paddingAngle",0):0;if(r){var c=U(r.endAngle-r.startAngle,t.endAngle-t.startAngle),u=yl(yl({},t),{},{startAngle:i+a,endAngle:i+c(n)+a});o.push(u),i=u.endAngle}else{var l=U(0,t.endAngle-t.startAngle)(n),f=yl(yl({},t),{},{startAngle:i+a,endAngle:i+l+a});o.push(f),i=f.endAngle}}),a().createElement(tL,null,t.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return r&&e&&e.length&&(!n||!c1()(n,e))?this.renderSectorsWithAnimation():this.renderSectorsStatically(e)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,o=e.className,i=e.label,c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!N(c)||!N(u)||!N(l)||!N(s))return null;var h=(0,x.A)("recharts-pie",o);return a().createElement(tL,{tabIndex:this.props.rootTabIndex,className:h,ref:function(e){t.pieRef=e}},this.renderSectors(),i&&this.renderLabels(n),si.renderCallByParent(this.props,null,!1),(!f||p)&&sS.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(a().isValidElement(t))return a().cloneElement(t,e);if(G()(t))return t(e);var n=(0,x.A)("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return a().createElement(p5,yc({},e,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(t,e,r){if(a().isValidElement(t))return a().cloneElement(t,e);var n=r;if(G()(t)&&(n=t(e),a().isValidElement(n)))return n;var o=(0,x.A)("recharts-pie-label-text","boolean"==typeof t||G()(t)?"":t.className);return a().createElement(ot,yc({},e,{alignmentBaseline:"middle",className:o}),n)}}],e&&ys(n.prototype,e),r&&ys(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);yd(yv,"displayName","Pie"),yd(yv,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!t6.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),yd(yv,"parseDeltaAngle",function(t,e){return T(e-t)*Math.min(Math.abs(e-t),360)}),yd(yv,"getRealPieData",function(t){var e=t.data,r=t.children,n=tb(t,!1),o=th(r,sp);return e&&e.length?e.map(function(t,e){return yl(yl(yl({payload:t},n),t),o&&o[e]&&o[e].props)}):o&&o.length?o.map(function(t){return yl(yl({},n),t.props)}):[]}),yd(yv,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=lQ(o,i);return{cx:n+R(t.cx,o,o/2),cy:r+R(t.cy,i,i/2),innerRadius:R(t.innerRadius,a,0),outerRadius:R(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}}),yd(yv,"getComposedData",function(t){var e,r,n=t.item,o=t.offset,i=void 0!==n.type.defaultProps?yl(yl({},n.type.defaultProps),n.props):n.props,a=yv.getRealPieData(i);if(!a||!a.length)return null;var c=i.cornerRadius,u=i.startAngle,l=i.endAngle,s=i.paddingAngle,f=i.dataKey,p=i.nameKey,h=i.valueKey,d=i.tooltipType,y=Math.abs(i.minAngle),v=yv.parseCoordinateOfPie(i,o),m=yv.parseDeltaAngle(u,l),b=Math.abs(m),g=f;X()(f)&&X()(h)?(q(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):X()(f)&&(q(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=h);var x=a.filter(function(t){return 0!==u3(t,g,0)}).length,O=b-x*y-(b>=360?x:x-1)*s,w=a.reduce(function(t,e){var r=u3(e,g,0);return t+(N(r)?r:0)},0);return w>0&&(e=a.map(function(t,e){var n,o=u3(t,g,0),i=u3(t,p,e),a=(N(o)?o:0)/w,l=(n=e?r.endAngle+T(m)*s*(0!==o):u)+T(m)*((0!==o?y:0)+a*O),f=(n+l)/2,h=(v.innerRadius+v.outerRadius)/2,b=[{name:i,value:o,payload:t,dataKey:g,type:d}],x=lJ(v.cx,v.cy,h,f);return r=yl(yl(yl({percent:a,cornerRadius:c,name:i,tooltipPayload:b,midAngle:f,middleRadius:h,tooltipPosition:x},t),v),{},{value:u3(t,g),startAngle:n,endAngle:l,payload:t,paddingAngle:T(m)*s})})),yl(yl({},v),{},{sectors:e,data:a})});var ym=hB({chartName:"PieChart",GraphicalChild:yv,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:dZ},{axisType:"radiusAxis",AxisComp:yi}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.startAngle,u=t.endAngle,l=R(t.cx,i,i/2),s=R(t.cy,a,a/2),f=lQ(i,a,r),p=R(t.innerRadius,f,0),h=R(t.outerRadius,f,.8*f);return Object.keys(e).reduce(function(t,r){var i,a=e[r],f=a.domain,d=a.reversed;if(X()(a.range))"angleAxis"===n?i=[c,u]:"radiusAxis"===n&&(i=[p,h]),d&&(i=[i[1],i[0]]);else{var y,v=function(t){if(Array.isArray(t))return t}(y=i=a.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next,!1;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(y,2)||function(t,e){if(t){if("string"==typeof t)return lY(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lY(t,e)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=v[0],u=v[1]}var m=ll(a,o),b=m.realScaleType,g=m.scale;g.domain(f).range(i),ls(g);var x=lv(g,lV(lV({},a),{},{realScaleType:b})),O=lV(lV(lV({},a),x),{},{range:i,radius:h,realScaleType:b,scale:g,cx:l,cy:s,innerRadius:p,outerRadius:h,startAngle:c,endAngle:u});return lV(lV({},t),{},lK({},r,O))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});function yb(){let[t,e]=(0,i.useState)("month"),[r,n]=(0,i.useState)(null),[a,x]=(0,i.useState)(null),[O,w]=(0,i.useState)(!0),[j,S]=(0,i.useState)(""),{user:P,cantiere:A}=(0,s.A)(),E=r?[{name:"Installati",value:r.cavi_posati,color:"#22c55e"},{name:"Collegati",value:r.cavi_collegati,color:"#3b82f6"},{name:"Certificati",value:r.cavi_certificati,color:"#f59e0b"},{name:"Da Installare",value:r.totale_cavi-r.cavi_posati,color:"#94a3b8"}]:[];return(0,o.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,o.jsxs)("div",{className:"flex justify-end gap-2 mb-6",children:[["week","month","quarter"].map(r=>(0,o.jsx)(u.$,{variant:t===r?"default":"outline",size:"sm",onClick:()=>e(r),className:"capitalize",children:"week"===r?"Settimana":"month"===r?"Mese":"Trimestre"},r)),(0,o.jsxs)(u.$,{variant:"outline",size:"sm",children:[(0,o.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Esporta PDF"]})]}),O?(0,o.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(p.A,{className:"h-6 w-6 animate-spin"}),(0,o.jsx)("span",{children:"Caricamento report..."})]})}):j?(0,o.jsxs)("div",{className:"p-6 border border-amber-200 rounded-lg bg-amber-50",children:[(0,o.jsxs)("div",{className:"flex items-center mb-4",children:[(0,o.jsx)(h.A,{className:"h-5 w-5 text-amber-600 mr-2"}),(0,o.jsx)("span",{className:"text-amber-800 font-medium",children:"Report in fase di implementazione"})]}),(0,o.jsx)("p",{className:"text-amber-700 mb-4",children:"Gli endpoint per i report non sono ancora implementati nel backend. I report saranno disponibili nelle prossime versioni del sistema."}),(0,o.jsxs)("div",{className:"text-sm text-amber-600",children:[(0,o.jsx)("strong",{children:"Funzionalit\xe0 previste:"}),(0,o.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,o.jsx)("li",{children:"Report avanzamento lavori"}),(0,o.jsx)("li",{children:"Report BOQ (Bill of Quantities)"}),(0,o.jsx)("li",{children:"Report utilizzo bobine"}),(0,o.jsx)("li",{children:"Statistiche di produttivit\xe0"})]})]})]}):r?(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,o.jsxs)(c.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,o.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,o.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento Totale"}),(0,o.jsx)(d.A,{className:"h-4 w-4 text-blue-500"})]}),(0,o.jsxs)(c.Wu,{children:[(0,o.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[r.percentuale_completamento.toFixed(1),"%"]}),(0,o.jsx)(l.k,{value:r.percentuale_completamento,className:"mt-2"}),(0,o.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[r.cavi_posati," di ",r.totale_cavi," cavi"]})]})]}),(0,o.jsxs)(c.Zp,{className:"border-l-4 border-l-green-500",children:[(0,o.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,o.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Media Giornaliera"}),(0,o.jsx)(y.A,{className:"h-4 w-4 text-green-500"})]}),(0,o.jsxs)(c.Wu,{children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:r.media_giornaliera.toFixed(1)}),(0,o.jsx)("p",{className:"text-xs text-slate-500",children:"cavi/giorno"}),(0,o.jsxs)("div",{className:"flex items-center mt-2",children:[(0,o.jsx)(y.A,{className:"h-3 w-3 text-green-500 mr-1"}),(0,o.jsxs)("span",{className:"text-xs text-green-600",children:["Basato su ",r.giorni_lavorativi," giorni"]})]})]})]}),(0,o.jsxs)(c.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,o.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,o.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Completati"}),(0,o.jsx)(v.A,{className:"h-4 w-4 text-purple-500"})]}),(0,o.jsxs)(c.Wu,{children:[(0,o.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[r.percentuale_metri.toFixed(1),"%"]}),(0,o.jsxs)("p",{className:"text-xs text-slate-500",children:[r.metri_posati,"m di ",r.metri_totali,"m"]}),(0,o.jsxs)("div",{className:"flex items-center mt-2",children:[(0,o.jsx)(m.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,o.jsx)("span",{className:"text-xs text-purple-600",children:"Metrature"})]})]})]}),(0,o.jsxs)(c.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,o.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,o.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Stima Completamento"}),(0,o.jsx)(b.A,{className:"h-4 w-4 text-orange-500"})]}),(0,o.jsxs)(c.Wu,{children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:r.stima_completamento}),(0,o.jsx)("p",{className:"text-xs text-slate-500",children:"al ritmo attuale"}),(0,o.jsxs)("div",{className:"flex items-center mt-2",children:[(0,o.jsx)(g.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,o.jsx)("span",{className:"text-xs text-orange-600",children:"Proiezione"})]})]})]})]}):null,(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)(c.Zp,{children:[(0,o.jsxs)(c.aR,{children:[(0,o.jsx)(c.ZB,{children:"Trend Installazioni Settimanali"}),(0,o.jsx)(c.BT,{children:"Confronto installazioni vs target giornaliero"})]}),(0,o.jsx)(c.Wu,{children:(0,o.jsx)(tE,{width:"100%",height:300,children:(0,o.jsxs)(dh,{data:[],children:[(0,o.jsx)(d_,{strokeDasharray:"3 3"}),(0,o.jsx)(dr,{dataKey:"name"}),(0,o.jsx)(dp,{}),(0,o.jsx)(el,{}),(0,o.jsx)(fh,{dataKey:"target",fill:"#e2e8f0",name:"Target"}),(0,o.jsx)(fh,{dataKey:"installati",fill:"#3b82f6",name:"Installati"})]})})})]}),(0,o.jsxs)(c.Zp,{children:[(0,o.jsxs)(c.aR,{children:[(0,o.jsx)(c.ZB,{children:"Distribuzione Stato Cavi"}),(0,o.jsx)(c.BT,{children:"Panoramica dello stato di avanzamento"})]}),(0,o.jsx)(c.Wu,{children:(0,o.jsx)(tE,{width:"100%",height:300,children:(0,o.jsxs)(ym,{children:[(0,o.jsx)(yv,{data:E,cx:"50%",cy:"50%",outerRadius:100,fill:"#8884d8",dataKey:"value",label:({name:t,percent:e})=>`${t} ${(100*e).toFixed(0)}%`,children:E.map((t,e)=>(0,o.jsx)(sp,{fill:t.color},`cell-${e}`))}),(0,o.jsx)(el,{})]})})})]})]}),(0,o.jsxs)(c.Zp,{children:[(0,o.jsxs)(c.aR,{children:[(0,o.jsx)(c.ZB,{children:"Avanzamento per Settore"}),(0,o.jsx)(c.BT,{children:"Stato di completamento dettagliato per ogni settore"})]}),(0,o.jsx)(c.Wu,{children:(0,o.jsx)("div",{className:"space-y-4",children:(0,o.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Dati di avanzamento per settore saranno disponibili quando collegati al backend"})})})]}),(0,o.jsxs)(c.Zp,{children:[(0,o.jsxs)(c.aR,{children:[(0,o.jsx)(c.ZB,{children:"Performance Team"}),(0,o.jsx)(c.BT,{children:"Statistiche dettagliate per ogni squadra di lavoro"})]}),(0,o.jsx)(c.Wu,{children:(0,o.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Statistiche team saranno disponibili quando collegati al backend"})})]})]})})}},44493:(t,e,r)=>{"use strict";r.d(e,{BT:()=>u,Wu:()=>l,ZB:()=>c,Zp:()=>i,aR:()=>a});var n=r(60687);r(43210);var o=r(4780);function i({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...e})}function a({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...e})}function c({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...e})}function u({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...e})}function l({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...e})}},45058:(t,e,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);t.exports=function(t){return i(t)?n(a(t)):o(t)}},45180:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75900)),"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,s=["C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},45603:(t,e,r)=>{var n=r(20540),o=r(55048);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),o=r(66354),i=r(11424);t.exports=function(t,e){return i(o(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},46436:(t,e,r)=>{var n=r(49227),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},46657:(t,e,r)=>{"use strict";r.d(e,{k:()=>O});var n=r(60687),o=r(43210),i=r(11273),a=r(14163),c="Progress",[u,l]=(0,i.A)(c),[s,f]=u(c),p=o.forwardRef((t,e)=>{var r,o;let{__scopeProgress:i,value:c=null,max:u,getValueLabel:l=y,...f}=t;(u||0===u)&&!b(u)&&console.error((r=`${u}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=b(u)?u:100;null===c||g(c,p)||console.error((o=`${c}`,`Invalid prop \`value\` of value \`${o}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=g(c,p)?c:null,d=m(h)?l(h,p):void 0;return(0,n.jsx)(s,{scope:i,value:h,max:p,children:(0,n.jsx)(a.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":m(h)?h:void 0,"aria-valuetext":d,role:"progressbar","data-state":v(h,p),"data-value":h??void 0,"data-max":p,...f,ref:e})})});p.displayName=c;var h="ProgressIndicator",d=o.forwardRef((t,e)=>{let{__scopeProgress:r,...o}=t,i=f(h,r);return(0,n.jsx)(a.sG.div,{"data-state":v(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...o,ref:e})});function y(t,e){return`${Math.round(t/e*100)}%`}function v(t,e){return null==t?"indeterminate":t===e?"complete":"loading"}function m(t){return"number"==typeof t}function b(t){return m(t)&&!isNaN(t)&&t>0}function g(t,e){return m(t)&&!isNaN(t)&&t<=e&&t>=0}d.displayName=h;var x=r(4780);function O({className:t,value:e,...r}){return(0,n.jsx)(p,{"data-slot":"progress",className:(0,x.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...r,children:(0,n.jsx)(d,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(e||0)}%)`}})})}},47212:(t,e,r)=>{var n=r(87270),o=r(30316),i=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),o=r(91928),i=r(48169);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},48730:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49227:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},52823:(t,e,r)=>{var n=r(85406),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},52931:(t,e,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),o=r(32269);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},57088:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(22);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),o=r(40542),i=r(27467);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},66354:(t,e,r)=>{var n=r(85244),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),o=r(34117),i=r(48385);t.exports=function(t){return o(t)?i(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case o:return e}}}(t)===i}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),o=r(99114),i=r(22);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},70440:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>o});var n=r(31658);let o=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},71960:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),o=r(93311),i=r(41132);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},75900:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx","default")},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),o=r(15871);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},80195:(t,e,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),o=r(1944),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},80458:(t,e,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),o=r(35163),i=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},83997:t=>{"use strict";t.exports=require("tty")},84031:(t,e,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),o=r(7383),i=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92311:(t,e,r)=>{Promise.resolve().then(r.bind(r,43641))},92662:(t,e,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),o=r(7651);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),o=r(36959),i=r(2408);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},95746:(t,e,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),o=r(39774),i=r(74610);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},97887:(t,e,r)=>{Promise.resolve().then(r.bind(r,75900))},98451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},99114:(t,e,r)=>{var n=r(12344),o=r(7651);t.exports=function(t,e){return t&&n(t,e,o)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[447,538,658,615],()=>r(45180));module.exports=n})();