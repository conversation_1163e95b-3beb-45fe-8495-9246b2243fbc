import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Rate limiting store (in produzione usare Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Rate limiting middleware
const checkRateLimit = (request: NextRequest, maxRequests: number, windowMs: number): boolean => {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const key = `${ip}-${request.nextUrl.pathname}`
  const now = Date.now()

  const record = rateLimitStore.get(key)

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= maxRequests) {
    return false
  }

  record.count++
  return true
}

// Security headers avanzati
const securityHeaders = {
  'X-XSS-Protection': '1; mode=block',
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: blob:",
    "connect-src 'self' http://localhost:8001",
    "frame-ancestors 'none'"
  ].join('; ')
}

export function middleware(request: NextRequest) {
  const response = NextResponse.next()

  // Applica security headers avanzati
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Rate limiting per API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    if (!checkRateLimit(request, 100, 60000)) {
      return new NextResponse('Rate limit exceeded', {
        status: 429,
        headers: { 'Retry-After': '60' }
      })
    }
  }

  // Rate limiting per login
  if (request.nextUrl.pathname === '/api/auth/login') {
    if (!checkRateLimit(request, 5, 300000)) { // 5 tentativi per 5 minuti
      return new NextResponse('Too many login attempts', {
        status: 429,
        headers: { 'Retry-After': '300' }
      })
    }
  }

  // Blocca User-Agent sospetti
  const userAgent = request.headers.get('user-agent') || ''
  const suspiciousAgents = ['sqlmap', 'nikto', 'nmap', 'burpsuite']

  if (suspiciousAgents.some(agent => userAgent.toLowerCase().includes(agent))) {
    console.warn(`🚨 Blocked suspicious user agent: ${userAgent}`)
    return new NextResponse('Forbidden', { status: 403 })
  }

  // Blocca payload sospetti
  const url = request.nextUrl.toString()
  if (/[<>\"']|union|select|insert|javascript:/gi.test(url)) {
    console.warn(`🚨 Blocked suspicious request: ${url}`)
    return new NextResponse('Bad Request', { status: 400 })
  }

  // Log accessi sensibili
  if (request.nextUrl.pathname.startsWith('/admin')) {
    const ip = request.ip || 'unknown'
    console.log(`🔒 Admin access: ${request.method} ${request.nextUrl.pathname} from ${ip}`)
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}
