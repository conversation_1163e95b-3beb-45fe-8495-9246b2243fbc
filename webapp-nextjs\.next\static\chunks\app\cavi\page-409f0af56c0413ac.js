(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[986],{3469:(e,a,i)=>{Promise.resolve().then(i.bind(i,22835))},22835:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>eo});var t=i(95155),s=i(12115),n=i(35695),l=i(66695),r=i(30285),o=i(55365),c=i(40283),d=i(25731),m=i(26126),u=i(47262),x=i(85127),p=i(62523),h=i(59409),v=i(20547),g=i(59434);let f=v.bL,b=v.l9,j=s.forwardRef((e,a)=>{let{className:i,align:s="center",sideOffset:n=4,...l}=e;return(0,t.jsx)(v.ZL,{children:(0,t.jsx)(v.UC,{ref:a,align:s,sideOffset:n,className:(0,g.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",i),...l})})});j.displayName=v.UC.displayName;var N=i(21492),_=i(39881),y=i(58832),C=i(54416),w=i(66932);function z(e){let{data:a=[],columns:i=[],loading:n=!1,emptyMessage:o="Nessun dato disponibile",onFilteredDataChange:c,renderRow:d,className:u}=e,[p,h]=(0,s.useState)({key:null,direction:null}),[v,z]=(0,s.useState)({}),[S,k]=(0,s.useState)({}),E=e=>[...new Set(a.map(a=>a[e]).filter(Boolean))].sort(),I=(0,s.useMemo)(()=>{let e=[...a];return Object.entries(v).forEach(a=>{let[i,t]=a;!t.value||Array.isArray(t.value)&&0===t.value.length||"string"==typeof t.value&&""===t.value.trim()||(e=e.filter(e=>{let a=e[i];if("select"===t.type)return(Array.isArray(t.value)?t.value:[t.value]).includes(a);if("text"===t.type){let e=t.value.toLowerCase(),i=String(a||"").toLowerCase();return"equals"===t.operator?i===e:i.includes(e)}if("number"===t.type){let e=parseFloat(a),i=parseFloat(t.value);if(isNaN(e)||isNaN(i))return!1;switch(t.operator){case"equals":default:return e===i;case"gt":return e>i;case"lt":return e<i;case"gte":return e>=i;case"lte":return e<=i}}return!0}))}),p.key&&p.direction&&e.sort((e,a)=>{let i=e[p.key],t=a[p.key];if(null==i&&null==t)return 0;if(null==i)return"asc"===p.direction?-1:1;if(null==t)return"asc"===p.direction?1:-1;let s=parseFloat(i),n=parseFloat(t),l=!isNaN(s)&&!isNaN(n),r=0;return r=l?s-n:String(i).localeCompare(String(t)),"asc"===p.direction?r:-r}),e},[a,v,p]);(0,s.useEffect)(()=>{c&&c(I)},[I,c]);let O=e=>{let a=i.find(a=>a.field===e);null!=a&&a.disableSort||h(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},F=(e,a)=>{z(i=>({...i,[e]:{...i[e],...a}}))},T=e=>{z(a=>{let i={...a};return delete i[e],i})},R=e=>p.key!==e?(0,t.jsx)(N.A,{className:"h-3 w-3"}):"asc"===p.direction?(0,t.jsx)(_.A,{className:"h-3 w-3"}):"desc"===p.direction?(0,t.jsx)(y.A,{className:"h-3 w-3"}):(0,t.jsx)(N.A,{className:"h-3 w-3"}),B=Object.keys(v).length>0;return n?(0,t.jsx)(l.Zp,{className:u,children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,t.jsxs)("div",{className:u,children:[B&&(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(v).map(e=>{let[a,s]=e,n=i.find(e=>e.field===a);if(!n)return null;let l=Array.isArray(s.value)?s.value.join(", "):String(s.value);return(0,t.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[n.headerName,": ",l,(0,t.jsx)(r.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>T(a),children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})]},a)}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>{z({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-0",children:(0,t.jsxs)(x.XI,{children:[(0,t.jsx)(x.A0,{children:(0,t.jsx)(x.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:i.map(e=>(0,t.jsx)(x.nd,{className:(0,g.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===e.align&&"text-center","right"===e.align&&"text-right"),style:{width:e.width,...e.headerStyle},children:e.renderHeader?e.renderHeader():(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:e.headerName}),!e.disableSort&&(0,t.jsx)(r.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-mariner-100",onClick:()=>O(e.field),children:R(e.field)}),!e.disableFilter&&(0,t.jsxs)(f,{open:S[e.field],onOpenChange:a=>k(i=>({...i,[e.field]:a})),children:[(0,t.jsx)(b,{asChild:!0,children:(0,t.jsx)(r.$,{variant:"ghost",size:"sm",className:(0,g.cn)("h-auto p-0 hover:bg-mariner-100",v[e.field]&&"text-mariner-600"),children:(0,t.jsx)(w.A,{className:"h-3 w-3"})})}),(0,t.jsx)(j,{className:"w-64",align:"start",children:(0,t.jsx)(A,{column:e,data:a,currentFilter:v[e.field],onFilterChange:a=>F(e.field,a),onClearFilter:()=>T(e.field),getUniqueValues:()=>E(e.field)})})]})]})},e.field))})}),(0,t.jsx)(x.BF,{children:I.length>0?I.map((e,a)=>d?d(e,a):(0,t.jsx)(x.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:i.map(a=>(0,t.jsx)(x.nA,{className:(0,g.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},a)):(0,t.jsx)(x.Hj,{children:(0,t.jsx)(x.nA,{colSpan:i.length,className:"text-center py-8 text-muted-foreground",children:o})})})]})})})]})}function A(e){let{column:a,currentFilter:i,onFilterChange:n,onClearFilter:l,getUniqueValues:o}=e,[c,d]=(0,s.useState)((null==i?void 0:i.value)||""),[m,x]=(0,s.useState)((null==i?void 0:i.operator)||"contains"),v=o(),g="number"!==a.dataType&&v.length<=20,f="number"===a.dataType,b=()=>{g?n({type:"select",value:Array.isArray(c)?c:[c]}):f?n({type:"number",value:c,operator:m}):n({type:"text",value:c,operator:m})};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",a.headerName]}),g?(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:v.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.S,{id:"filter-".concat(e),checked:Array.isArray(c)?c.includes(e):c===e,onCheckedChange:a=>{Array.isArray(c)?d(a?[...c,e]:c.filter(a=>a!==e)):d(a?[e]:[])}}),(0,t.jsx)("label",{htmlFor:"filter-".concat(e),className:"text-sm",children:e})]},e))}):(0,t.jsxs)("div",{className:"space-y-2",children:[f&&(0,t.jsxs)(h.l6,{value:m,onValueChange:x,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"}),(0,t.jsx)(h.eb,{value:"gt",children:"Maggiore di"}),(0,t.jsx)(h.eb,{value:"lt",children:"Minore di"}),(0,t.jsx)(h.eb,{value:"gte",children:"Maggiore o uguale"}),(0,t.jsx)(h.eb,{value:"lte",children:"Minore o uguale"})]})]}),!f&&(0,t.jsxs)(h.l6,{value:m,onValueChange:x,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]}),(0,t.jsx)(p.p,{placeholder:"Cerca ".concat(a.headerName.toLowerCase(),"..."),value:c,onChange:e=>d(e.target.value),onKeyDown:e=>"Enter"===e.key&&b()})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(r.$,{size:"sm",onClick:b,children:"Applica"}),(0,t.jsx)(r.$,{size:"sm",variant:"outline",onClick:l,children:"Pulisci"})]})]})}var S=i(47924),k=i(19145),E=i(18979);function I(e){let{cavi:a=[],onFilteredDataChange:i,loading:n=!1,selectionEnabled:o=!1,onSelectionToggle:c}=e,[d,m]=(0,s.useState)(""),[u,x]=(0,s.useState)("contains"),v=e=>e?e.toString().toLowerCase().trim():"",g=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},f=(0,s.useCallback)((e,a,i)=>{let t=v(a);if(!t)return!0;let s=v(e.id_cavo),{prefix:n,number:l,suffix:r}=g(e.id_cavo||""),o=v(e.tipologia),c=v(e.formazione||e.sezione),d=v(e.utility),m=v(e.sistema),u=v(e.da||e.ubicazione_partenza),x=v(e.a||e.ubicazione_arrivo),p=v(e.utenza_partenza),h=v(e.utenza_arrivo),f=[s,n,l,r,o,c,d,m,u,x,p,h,v(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":v(e.id_bobina)],b=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],j=t.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(j){let e=j[1],a=parseFloat(j[2]);return b.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(t);return!!(!isNaN(N)&&b.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?f.some(e=>e===t):f.some(e=>e.includes(t)))},[]),b=(0,s.useCallback)(()=>{if(!d.trim()){null==i||i(a);return}let e=d.split(",").map(e=>e.trim()).filter(e=>e.length>0),t=[];t="equals"===u?1===e.length?a.filter(a=>f(a,e[0],!0)):a.filter(a=>e.every(e=>f(a,e,!0))):a.filter(a=>e.some(e=>f(a,e,!1))),null==i||i(t)},[d,u,a,i,f]);(0,s.useEffect)(()=>{b()},[b]);let j=e=>{m(e)},N=()=>{m(""),x("contains")};return(0,t.jsx)(l.Zp,{className:"mb-4",children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(S.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(p.p,{placeholder:"Ricerca intelligente cavi...",value:d,onChange:e=>j(e.target.value),disabled:n,className:"pl-10 pr-10"}),d&&(0,t.jsx)(r.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:N,children:(0,t.jsx)(C.A,{className:"h-3 w-3"})})]}),(0,t.jsx)("div",{className:"w-40",children:(0,t.jsxs)(h.l6,{value:u,onValueChange:e=>x(e),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]})}),d&&(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:N,disabled:n,children:"Pulisci"}),c&&(0,t.jsxs)(r.$,{variant:o?"default":"outline",size:"sm",onClick:c,className:"flex items-center gap-2",children:[o?(0,t.jsx)(k.A,{className:"h-4 w-4"}):(0,t.jsx)(E.A,{className:"h-4 w-4"}),o?"Disabilita Selezione":"Abilita Selezione"]})]}),d&&(0,t.jsx)("div",{className:"mt-2 text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,t.jsx)("span",{children:"\uD83D\uDCA1 Suggerimenti:"}),(0,t.jsx)("span",{children:"• Usa virgole per termini multipli"}),(0,t.jsx)("span",{children:"• Cerca per ID, tipologia, formazione, ubicazioni"}),(0,t.jsx)("span",{children:"• Usa >100, <=50 per valori numerici"})]})})]})})}var O=i(5623),F=i(37108),T=i(381),R=i(85339),B=i(71539),D=i(40646),L=i(14186),M=i(3493);function P(e){let{cavi:a=[],loading:i=!1,selectionEnabled:n=!1,selectedCavi:o=[],onSelectionChange:c,onStatusAction:d,onContextMenuAction:p}=e,[h,v]=(0,s.useState)(a),[g,f]=(0,s.useState)(a),[b,j]=(0,s.useState)(n);(0,s.useEffect)(()=>{v(a),f(a)},[a]);let N=e=>{c&&c(e?g.map(e=>e.id_cavo):[])},_=(e,a)=>{c&&c(a?[...o,e]:o.filter(a=>a!==e))},y=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID Cavo",dataType:"text",headerStyle:{fontWeight:"bold"},renderCell:e=>(0,t.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text"},{field:"utility",headerName:"Utility",dataType:"text"},{field:"tipologia",headerName:"Tipologia",dataType:"text"},{field:"formazione",headerName:"Formazione",dataType:"text",align:"right",renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"Metri Teorici",dataType:"number",align:"right",renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"Metri Reali",dataType:"number",align:"right",renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",renderCell:e=>e.da||e.ubicazione_partenza},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",renderCell:e=>e.a||e.ubicazione_arrivo},{field:"id_bobina",headerName:"Bobina",dataType:"text",renderCell:e=>e.id_bobina||"N/A"},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>C(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>A(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>S(e)},{field:"azioni",headerName:"Azioni",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>w(e)},{field:"menu",headerName:"",width:50,disableFilter:!0,disableSort:!0,align:"center",renderCell:e=>(0,t.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>null==p?void 0:p(e,"menu"),children:(0,t.jsx)(O.A,{className:"h-4 w-4"})})}];return b&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"center",renderHeader:()=>(0,t.jsx)(u.S,{checked:o.length===g.length&&g.length>0,onCheckedChange:N}),renderCell:e=>(0,t.jsx)(u.S,{checked:o.includes(e.id_cavo),onCheckedChange:a=>_(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[b,o,g,N,_]),C=e=>{let a=e.comanda_posa,i=e.comanda_partenza,s=e.comanda_arrivo,n=e.comanda_certificazione,l=a||i||s||n;if(l&&"In corso"===e.stato_installazione)return(0,t.jsx)(m.E,{className:"bg-blue-600 text-white cursor-pointer hover:bg-blue-700",onClick:()=>null==d?void 0:d(e,"view_command",l),children:l});let r=e.stato_installazione||"Da installare";switch(r){case"Installato":return(0,t.jsx)(m.E,{className:"bg-green-100 text-green-800",children:"Installato"});case"In corso":return(0,t.jsx)(m.E,{className:"bg-yellow-100 text-yellow-800",children:"In corso"});case"Da installare":return(0,t.jsx)(m.E,{variant:"outline",children:"Da installare"});default:return(0,t.jsx)(m.E,{variant:"outline",children:r})}},w=e=>e.metri_posati>0?(0,t.jsxs)(r.$,{size:"sm",variant:"outline",onClick:()=>null==d?void 0:d(e,"modify_reel"),className:"text-xs",children:[(0,t.jsx)(T.A,{className:"h-3 w-3 mr-1"}),"Modifica Bobina"]}):(0,t.jsxs)(r.$,{size:"sm",variant:"outline",onClick:()=>null==d?void 0:d(e,"insert_meters"),className:"text-xs",children:[(0,t.jsx)(F.A,{className:"h-3 w-3 mr-1"}),"Inserisci Metri"]}),A=e=>{let a,i=e.metri_posati>0||e.metratura_reale>0,s=e.collegamento||e.collegamenti||0;if(!i)return(0,t.jsxs)(r.$,{size:"sm",variant:"outline",disabled:!0,className:"text-xs",children:[(0,t.jsx)(R.A,{className:"h-3 w-3 mr-1"}),"Non disponibile"]});let n,l,o="outline";switch(s){case 0:n="⚪⚪ Collega cavo",l="connect_cable",a=(0,t.jsx)(B.A,{className:"h-3 w-3 mr-1"});break;case 1:n="\uD83D\uDFE2⚪ Completa collegamento",l="connect_arrival",a=(0,t.jsx)(B.A,{className:"h-3 w-3 mr-1"}),o="secondary";break;case 2:n="⚪\uD83D\uDFE2 Completa collegamento",l="connect_departure",a=(0,t.jsx)(B.A,{className:"h-3 w-3 mr-1"}),o="secondary";break;case 3:n="\uD83D\uDFE2\uD83D\uDFE2 Scollega cavo",l="disconnect_cable",a=(0,t.jsx)(D.A,{className:"h-3 w-3 mr-1"}),o="default";break;default:n="Gestisci collegamenti",l="manage_connections",a=(0,t.jsx)(T.A,{className:"h-3 w-3 mr-1"})}return(0,t.jsxs)(r.$,{size:"sm",variant:o,onClick:()=>null==d?void 0:d(e,l),className:"text-xs",children:[a,n]})},S=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?(0,t.jsxs)(r.$,{size:"sm",variant:"default",onClick:()=>null==d?void 0:d(e,"generate_pdf"),className:"text-xs bg-green-600 hover:bg-green-700",children:[(0,t.jsx)(D.A,{className:"h-3 w-3 mr-1"}),"Genera PDF"]}):(0,t.jsxs)(r.$,{size:"sm",variant:"outline",onClick:()=>null==d?void 0:d(e,"create_certificate"),className:"text-xs",children:[(0,t.jsx)(L.A,{className:"h-3 w-3 mr-1"}),"Certifica cavo"]}):(0,t.jsxs)(r.$,{size:"sm",variant:"outline",disabled:!0,className:"text-xs",children:[(0,t.jsx)(R.A,{className:"h-3 w-3 mr-1"}),"Non disponibile"]})};return(0,t.jsxs)("div",{children:[(0,t.jsx)(I,{cavi:a,onFilteredDataChange:e=>{v(e)},loading:i,selectionEnabled:b,onSelectionToggle:()=>{j(!b)}}),(0,t.jsx)(l.Zp,{className:"mb-4",children:(0,t.jsx)(l.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.A,{className:"h-5 w-5 text-mariner-600"}),(0,t.jsxs)("span",{children:["Elenco Cavi (",g.length,")"]})]}),b&&o.length>0&&(0,t.jsxs)(m.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[o.length," selezionati"]})]})})}),(0,t.jsx)(z,{data:h,columns:y,loading:i,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{f(e)},renderRow:(e,a)=>{let i=o.includes(e.id_cavo);return(0,t.jsx)(x.Hj,{className:"".concat(i?"bg-mariner-50":""," hover:bg-mariner-50 cursor-pointer border-b border-mariner-100"),onClick:()=>b&&_(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),null==p||p(e,"context_menu")},children:y.map(a=>(0,t.jsx)(x.nA,{className:"py-2 px-4 ".concat("center"===a.align?"text-center":"right"===a.align?"text-right":""),style:a.cellStyle,onClick:e=>{["stato_installazione","collegamenti","certificato","azioni","menu"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},e.id_cavo)}})]})}var U=i(1243),V=i(72713);function $(e){let{cavi:a,filteredCavi:i,className:n}=e,r=(0,s.useMemo)(()=>{let e=a.length,t=i.length,s=i.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,n=i.filter(e=>"In corso"===e.stato_installazione).length,l=i.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,r=i.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=i.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=i.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=i.reduce((e,a)=>e+(a.metri_teorici||0),0),m=i.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0);return{totalCavi:e,filteredCount:t,installati:s,inCorso:n,daInstallare:t-s-n,collegati:l,parzialmenteCollegati:r,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:d>0?m/d*100:0}},[a,i]);return(0,t.jsx)(l.Zp,{className:n,children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-mariner-900",children:r.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["di ",r.totalCavi," cavi"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-green-700",children:r.installati}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Installati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(L.A,{className:"h-4 w-4 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-yellow-700",children:r.inCorso}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"In corso"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-gray-700",children:r.daInstallare}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Da installare"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(B.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-blue-700",children:r.collegati}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Collegati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-purple-700",children:r.certificati}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Certificati"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 md:col-span-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 text-indigo-600"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-semibold text-indigo-700",children:[r.metriInstallati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["di ",r.metriTotali.toLocaleString(),"m (",r.percentualeInstallazione.toFixed(1),"%)"]})]})]}),r.parzialmenteCollegati>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-4 w-4 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-yellow-700",children:r.parzialmenteCollegati}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Parziali"})]})]}),r.nonCollegati>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-4 w-4 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-red-500 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold text-red-700",children:r.nonCollegati}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Non collegati"})]})]})]}),r.metriTotali>0&&(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,t.jsx)("span",{children:"Progresso installazione"}),(0,t.jsxs)("span",{children:[r.percentualeInstallazione.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-mariner-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(Math.min(r.percentualeInstallazione,100),"%")}})})]})]})})}var J=i(54165),G=i(85057),Z=i(51154);function q(e){let{open:a,onClose:i,cavo:n,onSuccess:l,onError:m}=e,{cantiere:u}=(0,c.A)(),[x,v]=(0,s.useState)(""),[g,f]=(0,s.useState)(""),[b,j]=(0,s.useState)([]),[N,_]=(0,s.useState)(!1),[y,C]=(0,s.useState)(!1),[w,z]=(0,s.useState)("");(0,s.useEffect)(()=>{if(a&&n){var e;A(),v((null==(e=n.metri_teorici)?void 0:e.toString())||""),f(""),z("")}},[a,n]);let A=async()=>{if(n&&u)try{C(!0);let e=(await d.Fw.getBobineCompatibili(u.id_cantiere,{tipologia:n.tipologia,n_conduttori:n.n_conduttori,sezione:n.formazione||n.sezione})).data.map(e=>({id_bobina:e.id_bobina,tipologia:e.tipologia,formazione:e.sezione,metri_residui:e.metri_residui,fornitore:e.fornitore})),a=[{id_bobina:"BOBINA_VUOTA",tipologia:n.tipologia||"",formazione:n.formazione||n.sezione||"",metri_residui:0},...e];j(a)}catch(e){console.error("Errore nel caricamento bobine:",e),j([{id_bobina:"BOBINA_VUOTA",tipologia:n.tipologia||"",formazione:n.formazione||n.sezione||"",metri_residui:0}]),m("Errore nel caricamento delle bobine disponibili")}finally{C(!1)}},S=async()=>{if(!n||!x||!g)return void z("Compilare tutti i campi obbligatori");let e=parseFloat(x);if(isNaN(e)||e<=0)return void z("Inserire un valore valido per i metri posati");if(e>(n.metri_teorici||0))return void z("I metri posati non possono superare i metri teorici");let a=b.find(e=>e.id_bobina===g);if(a&&"BOBINA_VUOTA"!==a.id_bobina&&e>a.metri_residui)return void z("La bobina selezionata ha solo ".concat(a.metri_residui,"m disponibili"));try{if(_(!0),z(""),!u)throw Error("Cantiere non selezionato");await d.At.updateMetriPosati(u.id_cantiere,n.id_cavo,e,"BOBINA_VUOTA"!==g?g:void 0),l("Metri posati aggiornati con successo per il cavo ".concat(n.id_cavo,": ").concat(e,"m")),i()}catch(e){var t,s;console.error("Errore nel salvataggio:",e),m((null==(s=e.response)||null==(t=s.data)?void 0:t.detail)||e.message||"Errore durante il salvataggio dei metri posati")}finally{_(!1)}},k=()=>{N||(v(""),f(""),z(""),i())};return n?(0,t.jsx)(J.lG,{open:a,onOpenChange:k,children:(0,t.jsxs)(J.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(J.c7,{children:[(0,t.jsx)(J.L3,{children:"Inserisci Metri Posati"}),(0,t.jsxs)(J.rr,{children:["Inserisci i metri posati per il cavo ",n.id_cavo]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Sistema:"})," ",n.sistema]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Utility:"})," ",n.utility]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",n.tipologia]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",n.formazione]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Da:"})," ",n.da]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"A:"})," ",n.a]}),(0,t.jsxs)("div",{className:"col-span-2",children:[(0,t.jsx)("strong",{children:"Metri teorici:"})," ",n.metri_teorici,"m"]})]})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"metri",children:"Metri Posati *"}),(0,t.jsx)(p.p,{id:"metri",type:"number",step:"0.1",min:"0",max:n.metri_teorici||0,value:x,onChange:e=>v(e.target.value),placeholder:"Inserisci metri posati"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"bobina",children:"Bobina *"}),y?(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento bobine..."})]}):(0,t.jsxs)(h.l6,{value:g,onValueChange:f,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona bobina"})}),(0,t.jsx)(h.gC,{children:b.map(e=>(0,t.jsx)(h.eb,{value:e.id_bobina,children:(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{children:e.id_bobina}),"BOBINA_VUOTA"!==e.id_bobina&&(0,t.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.metri_residui,"m disponibili",e.fornitore&&" - ".concat(e.fornitore)]})]})},e.id_bobina))})]})]}),w&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:w})]})]}),(0,t.jsxs)(J.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:k,disabled:N,children:"Annulla"}),(0,t.jsxs)(r.$,{onClick:S,disabled:N||!x||!g,children:[N&&(0,t.jsx)(Z.A,{className:"mr-2 h-4 w-4 animate-spin"}),N?"Salvando...":"Salva"]})]})]})}):null}function W(e){let{open:a,onClose:i,cavo:n,onSuccess:l,onError:m}=e,{cantiere:u}=(0,c.A)(),[x,p]=(0,s.useState)(""),[v,g]=(0,s.useState)([]),[f,b]=(0,s.useState)(!1),[j,N]=(0,s.useState)(!1),[_,y]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&n&&(C(),p(n.id_bobina||""),y(""))},[a,n]);let C=async()=>{if(n&&u)try{N(!0);let e=(await d.Fw.getBobineCompatibili(u.id_cantiere,{tipologia:n.tipologia,n_conduttori:n.n_conduttori,sezione:n.formazione||n.sezione})).data.map(e=>({id_bobina:e.id_bobina,tipologia:e.tipologia,formazione:e.sezione,metri_residui:e.metri_residui,fornitore:e.fornitore})),a=[{id_bobina:"BOBINA_VUOTA",tipologia:n.tipologia||"",formazione:n.formazione||n.sezione||"",metri_residui:0},...e];n.id_bobina&&!a.find(e=>e.id_bobina===n.id_bobina)&&a.push({id_bobina:n.id_bobina,tipologia:n.tipologia||"",formazione:n.formazione||n.sezione||"",metri_residui:0}),g(a)}catch(a){console.error("Errore nel caricamento bobine:",a);let e=[{id_bobina:"BOBINA_VUOTA",tipologia:n.tipologia||"",formazione:n.formazione||n.sezione||"",metri_residui:0}];n.id_bobina&&e.push({id_bobina:n.id_bobina,tipologia:n.tipologia||"",formazione:n.formazione||n.sezione||"",metri_residui:0}),g(e),m("Errore nel caricamento delle bobine disponibili")}finally{N(!1)}},w=async()=>{if(!n||!x)return void y("Selezionare una bobina");if(x===n.id_bobina)return void y("La bobina selezionata \xe8 gi\xe0 associata al cavo");let e=v.find(e=>e.id_bobina===x);if(e&&"BOBINA_VUOTA"!==e.id_bobina&&n.metri_posati>e.metri_residui)return void y("La bobina selezionata ha solo ".concat(e.metri_residui,"m disponibili, ma il cavo ha ").concat(n.metri_posati,"m posati"));try{if(b(!0),y(""),!u)throw Error("Cantiere non selezionato");await d.At.updateBobina(u.id_cantiere,n.id_cavo,x);let e="BOBINA_VUOTA"===x?"Bobina vuota assegnata al cavo ".concat(n.id_cavo):"Bobina ".concat(x," assegnata al cavo ").concat(n.id_cavo);l(e),i()}catch(e){var a,t;console.error("Errore nel salvataggio:",e),m((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante la modifica della bobina")}finally{b(!1)}},z=()=>{f||(p(""),y(""),i())};return n?(0,t.jsx)(J.lG,{open:a,onOpenChange:z,children:(0,t.jsxs)(J.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(J.c7,{children:[(0,t.jsxs)(J.L3,{className:"flex items-center space-x-2",children:[(0,t.jsx)(F.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Modifica Bobina"})]}),(0,t.jsxs)(J.rr,{children:["Modifica la bobina associata al cavo ",n.id_cavo]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Sistema:"})," ",n.sistema]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Utility:"})," ",n.utility]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",n.tipologia]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",n.formazione]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri posati:"})," ",n.metri_posati||0,"m"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Bobina attuale:"})," ",n.id_bobina||"Nessuna"]})]})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"bobina",children:"Nuova Bobina *"}),j?(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento bobine..."})]}):(0,t.jsxs)(h.l6,{value:x,onValueChange:p,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona nuova bobina"})}),(0,t.jsx)(h.gC,{children:v.map(e=>(0,t.jsx)(h.eb,{value:e.id_bobina,disabled:e.id_bobina===n.id_bobina,children:(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{children:e.id_bobina}),e.id_bobina===n.id_bobina&&(0,t.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-1 rounded",children:"Attuale"})]}),"BOBINA_VUOTA"!==e.id_bobina&&(0,t.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.metri_residui,"m disponibili",e.fornitore&&" - ".concat(e.fornitore)]})]})},e.id_bobina))})]})]}),"BOBINA_VUOTA"===x&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Stai assegnando una bobina vuota. Questo permetter\xe0 di posare il cavo e associare la bobina reale in un secondo momento."})]}),_&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:_})]})]}),(0,t.jsxs)(J.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:z,disabled:f,children:"Annulla"}),(0,t.jsxs)(r.$,{onClick:w,disabled:f||!x||x===n.id_bobina,children:[f&&(0,t.jsx)(Z.A,{className:"mr-2 h-4 w-4 animate-spin"}),f?"Salvando...":"Salva"]})]})]})}):null}function H(e){let{open:a,onClose:i,cavo:n,onSuccess:l,onError:m}=e,{cantiere:u}=(0,c.A)(),[x,p]=(0,s.useState)(""),[v,g]=(0,s.useState)([]),[f,b]=(0,s.useState)(!1),[j,N]=(0,s.useState)(!1),[_,y]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&n&&(p(""),y(""),C())},[a,n]);let C=async()=>{if(u)try{N(!0);let e=await d.AR.getResponsabili(u.id_cantiere);g(e.data)}catch(e){console.error("Errore nel caricamento responsabili:",e),g([])}finally{N(!1)}},w=async()=>{if(n&&u)try{b(!0),y(""),await d.At.collegaCavo(u.id_cantiere,n.id_cavo,"partenza",x),l("Collegamento lato partenza completato per il cavo ".concat(n.id_cavo)),i()}catch(i){var e,a;console.error("Errore nel collegamento:",i),m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante il collegamento")}finally{b(!1)}},z=async()=>{if(n&&u)try{b(!0),y(""),await d.At.collegaCavo(u.id_cantiere,n.id_cavo,"arrivo",x),l("Collegamento lato arrivo completato per il cavo ".concat(n.id_cavo)),i()}catch(i){var e,a;console.error("Errore nel collegamento:",i),m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante il collegamento")}finally{b(!1)}},A=async e=>{if(n&&u)try{b(!0),y(""),await d.At.scollegaCavo(u.id_cantiere,n.id_cavo,e);let a=e?" lato ".concat(e):"";l("Scollegamento".concat(a," completato per il cavo ").concat(n.id_cavo)),i()}catch(e){var a,t;console.error("Errore nello scollegamento:",e),m((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante lo scollegamento")}finally{b(!1)}};if(!n)return null;let S=(()=>{if(!n)return{stato:"non_collegato",descrizione:"Non collegato"};switch(n.collegamento||n.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}})(),k=(n.metri_posati||n.metratura_reale||0)>0;return(0,t.jsx)(J.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(J.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(J.c7,{children:[(0,t.jsxs)(J.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(B.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",n.id_cavo]}),(0,t.jsxs)(J.rr,{children:["Gestisci i collegamenti del cavo ",n.id_cavo]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Stato Attuale"}),(0,t.jsx)("div",{className:"mt-1 text-lg font-semibold",children:S.descrizione})]}),!k&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Il cavo deve essere installato prima di poter essere collegato."})]}),_&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:_})]}),k&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"responsabile",children:"Responsabile Collegamento"}),(0,t.jsxs)(h.l6,{value:x,onValueChange:p,disabled:j,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:v.map(e=>(0,t.jsxs)(h.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&" - ".concat(e.numero_telefono)]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:["partenza"!==S.stato&&"completo"!==S.stato&&(0,t.jsxs)(r.$,{onClick:w,disabled:f||!x,className:"w-full",children:[f?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Collega Partenza"]}),"arrivo"!==S.stato&&"completo"!==S.stato&&(0,t.jsxs)(r.$,{onClick:z,disabled:f||!x,className:"w-full",children:[f?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Collega Arrivo"]})]}),"non_collegato"!==S.stato&&(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)(r.$,{onClick:()=>A(),disabled:f,variant:"destructive",className:"w-full",children:[f?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"Scollega Completamente"]})})]})]})]}),(0,t.jsx)(J.Es,{children:(0,t.jsx)(r.$,{variant:"outline",onClick:i,disabled:f,children:"Chiudi"})})]})})}let X=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("textarea",{className:(0,g.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:a,...s})});X.displayName="Textarea";var Q=i(69037),Y=i(91788);function K(e){let{open:a,onClose:i,cavo:n,onSuccess:l,onError:m}=e,{cantiere:u}=(0,c.A)(),[x,v]=(0,s.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[g,f]=(0,s.useState)([]),[b,j]=(0,s.useState)(!1),[N,_]=(0,s.useState)(!1),[y,C]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&n&&(v({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),C(""),w())},[a,n]);let w=async()=>{if(u)try{_(!0);let e=await d.AR.getResponsabili(u.id_cantiere);f(e.data)}catch(e){console.error("Errore nel caricamento responsabili:",e),f([])}finally{_(!1)}},z=async()=>{if(n&&u){if(!x.responsabile_certificazione)return void C("Seleziona un responsabile per la certificazione");try{j(!0),C("");let e={id_cavo:n.id_cavo,responsabile_certificazione:x.responsabile_certificazione,data_certificazione:x.data_certificazione,esito_certificazione:x.esito_certificazione,note_certificazione:x.note_certificazione||null};await d.km.createCertificazione(u.id_cantiere,e),l("Certificazione completata per il cavo ".concat(n.id_cavo)),i()}catch(i){var e,a;console.error("Errore nella certificazione:",i),m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la certificazione")}finally{j(!1)}}},A=async()=>{if(n&&u)try{j(!0),C("");let e=await d.km.generatePDF(u.id_cantiere,n.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","certificato_".concat(n.id_cavo,".pdf")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("PDF certificato generato per il cavo ".concat(n.id_cavo))}catch(i){var e,a;console.error("Errore nella generazione PDF:",i),m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la generazione del PDF")}finally{j(!1)}};if(!n)return null;let S=(n.metri_posati||n.metratura_reale||0)>0,k=!!n&&3===(n.collegamento||n.collegamenti||0),E=!!n&&(!0===n.certificato||"SI"===n.certificato||"CERTIFICATO"===n.certificato);return(0,t.jsx)(J.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(J.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(J.c7,{children:[(0,t.jsxs)(J.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",n.id_cavo]}),(0,t.jsxs)(J.rr,{children:["Certifica il cavo ",n.id_cavo," o genera il PDF del certificato"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Stato Cavo"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(S?"bg-green-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-sm",children:S?"Installato":"Non installato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(k?"bg-green-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-sm",children:k?"Collegato":"Non collegato"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(E?"bg-green-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-sm",children:E?"Certificato":"Non certificato"})]})]})]}),!S&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Il cavo deve essere installato prima di poter essere certificato."})]}),!k&&S&&(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Il cavo deve essere completamente collegato prima di poter essere certificato."})]}),y&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:y})]}),E?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."})]}),(0,t.jsxs)(r.$,{onClick:A,disabled:b,className:"w-full",children:[b?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):S&&k&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"responsabile",children:"Responsabile Certificazione *"}),(0,t.jsxs)(h.l6,{value:x.responsabile_certificazione,onValueChange:e=>v(a=>({...a,responsabile_certificazione:e})),disabled:N,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:g.map(e=>(0,t.jsxs)(h.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&" - ".concat(e.numero_telefono)]},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"data",children:"Data Certificazione"}),(0,t.jsx)(p.p,{id:"data",type:"date",value:x.data_certificazione,onChange:e=>v(a=>({...a,data_certificazione:e.target.value}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"esito",children:"Esito Certificazione"}),(0,t.jsxs)(h.l6,{value:x.esito_certificazione,onValueChange:e=>v(a=>({...a,esito_certificazione:e})),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"CONFORME",children:"CONFORME"}),(0,t.jsx)(h.eb,{value:"NON_CONFORME",children:"NON CONFORME"}),(0,t.jsx)(h.eb,{value:"CONFORME_CON_RISERVA",children:"CONFORME CON RISERVA"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(X,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:x.note_certificazione,onChange:e=>v(a=>({...a,note_certificazione:e.target.value})),rows:3})]}),(0,t.jsxs)(r.$,{onClick:z,disabled:b||!x.responsabile_certificazione,className:"w-full",children:[b?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(Q.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo"]})]})]}),(0,t.jsx)(J.Es,{children:(0,t.jsx)(r.$,{variant:"outline",onClick:i,disabled:b,children:"Chiudi"})})]})})}var ee=i(25273),ea=i(17580);function ei(e){let{open:a,onClose:i,caviSelezionati:n,tipoComanda:l,onSuccess:m,onError:u}=e,{cantiere:x}=(0,c.A)(),[p,v]=(0,s.useState)({tipo_comanda:l||"POSA",responsabile:"",note:""}),[g,f]=(0,s.useState)([]),[b,j]=(0,s.useState)(!1),[N,_]=(0,s.useState)(!1),[y,C]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&(v({tipo_comanda:l||"POSA",responsabile:"",note:""}),C(""),w())},[a,l]);let w=async()=>{if(x)try{_(!0);let e=await d.AR.getResponsabili(x.id_cantiere);f(e.data)}catch(e){console.error("Errore nel caricamento responsabili:",e),f([])}finally{_(!1)}},z=async()=>{if(x){if(!p.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===n.length)return void C("Seleziona almeno un cavo per la comanda");try{j(!0),C("");let e={tipo_comanda:p.tipo_comanda,responsabile:p.responsabile,note:p.note||null},a=await d.CV.createComandaWithCavi(x.id_cantiere,e,n);m("Comanda ".concat(a.data.codice_comanda," creata con successo per ").concat(n.length," cavi")),i()}catch(i){var e,a;console.error("Errore nella creazione comanda:",i),u((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la creazione della comanda")}finally{j(!1)}}};return(0,t.jsx)(J.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(J.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(J.c7,{children:[(0,t.jsxs)(J.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ee.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsxs)(J.rr,{children:["Crea una nuova comanda per ",n.length," cavi selezionati"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)(G.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",n.length,")"]}),(0,t.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[n.slice(0,10).map(e=>(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),n.length>10&&(0,t.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",n.length-10," altri..."]})]})})]}),y&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:y})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(h.l6,{value:p.tipo_comanda,onValueChange:e=>v(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,t.jsx)(h.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,t.jsx)(h.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,t.jsx)(h.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,t.jsxs)(h.l6,{value:p.responsabile,onValueChange:e=>v(a=>({...a,responsabile:e})),disabled:N,children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(h.gC,{children:g.map(e=>(0,t.jsx)(h.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ea.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(X,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:p.note,onChange:e=>v(a=>({...a,note:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(p.tipo_comanda)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Responsabile:"})," ",p.responsabile||"Non selezionato"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cavi:"})," ",n.length," selezionati"]}),p.note&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Note:"})," ",p.note]})]})]})]}),(0,t.jsxs)(J.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:i,disabled:b,children:"Annulla"}),(0,t.jsxs)(r.$,{onClick:z,disabled:b||!p.responsabile||0===n.length,children:[b?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ee.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var et=i(29869),es=i(64261);function en(e){let{open:a,onClose:i,tipo:n,onSuccess:l,onError:m}=e,{cantiere:u}=(0,c.A)(),[x,h]=(0,s.useState)(null),[v,g]=(0,s.useState)(""),[f,b]=(0,s.useState)(!1),[j,N]=(0,s.useState)(""),[_,y]=(0,s.useState)(0),C=(0,s.useRef)(null),w=async()=>{if(x&&u){if("cavi"===n&&!v.trim())return void N("Inserisci il codice revisione per l'importazione cavi");try{let e;if(b(!0),N(""),y(0),e="cavi"===n?await d.mg.importCavi(u.id_cantiere,x,v.trim()):await d.mg.importBobine(u.id_cantiere,x),y(100),e.data.success){let a=e.data.details,t=e.data.message;"cavi"===n&&(null==a?void 0:a.cavi_importati)?t+=" (".concat(a.cavi_importati," cavi importati)"):"bobine"===n&&(null==a?void 0:a.bobine_importate)&&(t+=" (".concat(a.bobine_importate," bobine importate)")),l(t),i()}else m(e.data.message||"Errore durante l'importazione")}catch(i){var e,a;console.error("Errore nell'importazione:",i),m((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'importazione del file")}finally{b(!1),y(0)}}},z=()=>{f||(h(null),g(""),N(""),y(0),C.current&&(C.current.value=""),i())},A=()=>"cavi"===n?"Cavi":"Bobine";return(0,t.jsx)(J.lG,{open:a,onOpenChange:z,children:(0,t.jsxs)(J.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(J.c7,{children:[(0,t.jsxs)(J.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(et.A,{className:"h-5 w-5"}),"Importa ",A()," da Excel"]}),(0,t.jsxs)(J.rr,{children:["Carica un file Excel per importare ",A().toLowerCase()," nel cantiere"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===n?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,t.jsx)("span",{children:e})]},a))})]}),j&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:j})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"file",children:"File Excel *"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let i=null==(a=e.target.files)?void 0:a[0];if(i){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(i.type)&&!i.name.toLowerCase().endsWith(".xlsx")&&!i.name.toLowerCase().endsWith(".xls"))return void N("Seleziona un file Excel valido (.xlsx o .xls)");h(i),N("")}},disabled:f,className:"flex-1"}),x&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(D.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),x&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)(es.A,{className:"h-4 w-4 inline mr-1"}),x.name," (",(x.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===n&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(G.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,t.jsx)(p.p,{id:"revisione",value:v,onChange:e=>g(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:f}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),f&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(_,"%")}})})]}),x&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",A()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File:"})," ",x.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Dimensione:"})," ",(x.size/1024/1024).toFixed(2)," MB"]}),"cavi"===n&&v&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Revisione:"})," ",v]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==u?void 0:u.nome_cantiere]})]})]})]}),(0,t.jsxs)(J.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:z,disabled:f,children:"Annulla"}),(0,t.jsxs)(r.$,{onClick:w,disabled:f||!x||"cavi"===n&&!v.trim(),children:[f?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(et.A,{className:"h-4 w-4 mr-2"}),"Importa ",A()]})]})]})})}var el=i(54213);function er(e){let{open:a,onClose:i,onSuccess:n,onError:l}=e,{cantiere:m}=(0,c.A)(),[x,p]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[h,v]=(0,s.useState)(!1),[g,f]=(0,s.useState)(""),b=(e,a)=>{p(i=>({...i,[e]:a}))},j=async()=>{if(m)try{v(!0);let e=await d.mg.exportCavi(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","cavi_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),n("Export cavi completato con successo")}catch(i){var e,a;console.error("Errore nell'export cavi:",i),l((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei cavi")}finally{v(!1)}},N=async()=>{if(m)try{v(!0);let e=await d.mg.exportBobine(m.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","bobine_".concat(m.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),n("Export bobine completato con successo")}catch(i){var e,a;console.error("Errore nell'export bobine:",i),l((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export delle bobine")}finally{v(!1)}},_=async()=>{if(m)try{v(!0),f("");let e=[];x.cavi&&e.push(j()),x.bobine&&e.push(N()),x.comande&&console.log("Export comande - da implementare"),x.certificazioni&&console.log("Export certificazioni - da implementare"),x.responsabili&&console.log("Export responsabili - da implementare"),await Promise.all(e);let a=Object.values(x).filter(Boolean).length;n("Export completato: ".concat(a," file scaricati")),i()}catch(i){var e,a;console.error("Errore nell'export:",i),l((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei dati")}finally{v(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,t.jsx)(el.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,t.jsx)(es.A,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,t.jsx)(es.A,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,t.jsx)(es.A,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,t.jsx)(es.A,{className:"h-4 w-4"}),available:!1}];return(0,t.jsx)(J.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(J.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(J.c7,{children:[(0,t.jsxs)(J.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(Y.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,t.jsxs)(J.rr,{children:["Seleziona i dati da esportare dal cantiere ",null==m?void 0:m.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[g&&(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:g})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.available?"bg-white":"bg-gray-50"),children:[(0,t.jsx)(u.S,{id:e.key,checked:x[e.key],onCheckedChange:a=>b(e.key,a),disabled:!e.available||h}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)(G.J,{htmlFor:e.key,className:"font-medium ".concat(e.available?"":"text-gray-500"),children:[e.label,!e.available&&(0,t.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,t.jsx)("p",{className:"text-sm mt-1 ".concat(e.available?"text-gray-600":"text-gray-400"),children:e.description})]})]},e.key))]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,t.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,t.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,t.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(x).filter(Boolean).length>0&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(G.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==m?void 0:m.nome_cantiere]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(x).filter(Boolean).length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,t.jsxs)(J.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:i,disabled:h,children:"Annulla"}),(0,t.jsxs)(r.$,{onClick:_,disabled:h||0===Object.values(x).filter(Boolean).length,children:[h?(0,t.jsx)(Z.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(Y.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(x).filter(Boolean).length>0?"(".concat(Object.values(x).filter(Boolean).length,")"):""]})]})]})})}function eo(){let{user:e,cantiere:a,isAuthenticated:i,isLoading:m}=(0,c.A)(),u=(0,n.useRouter)(),x=e=>{let{title:a,description:i,variant:t}=e;console.log("".concat("destructive"===t?"❌":"✅"," ").concat(a,": ").concat(i))},[p,h]=(0,s.useState)([]),[v,g]=(0,s.useState)([]),[f,b]=(0,s.useState)(!0),[j,N]=(0,s.useState)(""),[_,y]=(0,s.useState)([]),[C,w]=(0,s.useState)(!1),[z,A]=(0,s.useState)([]);(0,s.useEffect)(()=>{A(p)},[p]);let[S,k]=(0,s.useState)({open:!1,cavo:null}),[E,I]=(0,s.useState)({open:!1,cavo:null}),[O,T]=(0,s.useState)({open:!1,cavo:null}),[B,D]=(0,s.useState)({open:!1,cavo:null}),[L,M]=(0,s.useState)({open:!1}),[U,V]=(0,s.useState)({open:!1}),[J,G]=(0,s.useState)(!1),[X,Q]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[Y,ee]=(0,s.useState)(0);(0,s.useEffect)(()=>{m||i||u.push("/login")},[i,m,u]),(0,s.useEffect)(()=>{ee((null==a?void 0:a.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"))},[a]),(0,s.useEffect)(()=>{Y&&Y>0&&ea()},[Y]);let ea=async()=>{try{b(!0),N(""),console.log("\uD83D\uDD0D Tentativo caricamento cavi per cantiere:",Y),console.log("\uD83D\uDD0D Token presente:",!!localStorage.getItem("token"));try{let e=await d.At.getCavi(Y);console.log("✅ API normale riuscita, cavi ricevuti:",(null==e?void 0:e.length)||0);let a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);h(a),g(i),et(a)}catch(e){console.log("❌ API normale fallita, provo endpoint debug..."),console.error("Errore API normale:",e);try{let e=await fetch("http://localhost:8001/api/cavi/debug/".concat(Y)),a=await e.json();if(console.log("✅ Endpoint debug riuscito:",a),a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),i=a.cavi.filter(e=>e.spare);h(e),g(i),et(e),N("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw console.error("❌ Anche endpoint debug fallito:",a),e}}}catch(i){var e,a;console.error("❌ Errore generale nel caricamento cavi:",i),N("Errore nel caricamento dei cavi: ".concat((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message))}finally{b(!1)}},et=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,t=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,n=e.reduce((e,a)=>e+(a.metri_teorici||0),0),l=e.reduce((e,a)=>e+(a.metri_posati||0),0);Q({totali:a,installati:i,collegati:t,certificati:s,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(t/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:n,metriInstallati:l,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},es=(e,a,i)=>{switch(console.log("Status action:",a,"for cavo:",e.id_cavo,"label:",i),a){case"insert_meters":k({open:!0,cavo:e});break;case"modify_reel":I({open:!0,cavo:e});break;case"view_command":x({title:"Visualizza Comanda",description:"Apertura comanda ".concat(i," per cavo ").concat(e.id_cavo)});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":T({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":D({open:!0,cavo:e})}},el=(e,a)=>{switch(console.log("Context menu action:",a,"for cavo:",e.id_cavo),a){case"view_details":x({title:"Visualizza Dettagli",description:"Apertura dettagli per cavo ".concat(e.id_cavo)});break;case"edit":x({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":x({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":x({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":_.includes(e.id_cavo)?(y(_.filter(a=>a!==e.id_cavo)),x({title:"Cavo Deselezionato",description:"Cavo ".concat(e.id_cavo," deselezionato")})):(y([..._,e.id_cavo]),x({title:"Cavo Selezionato",description:"Cavo ".concat(e.id_cavo," selezionato")}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),x({title:"ID Copiato",description:"ID cavo ".concat(e.id_cavo," copiato negli appunti")});break;case"copy_details":let i="ID: ".concat(e.id_cavo,", Tipologia: ").concat(e.tipologia,", Formazione: ").concat(e.formazione||e.sezione,", Metri: ").concat(e.metri_teorici);navigator.clipboard.writeText(i),x({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":x({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":x({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":M({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":M({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":M({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":M({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":x({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":x({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:x({title:"Azione non implementata",description:"Azione ".concat(a," non ancora implementata")})}},eo=e=>{x({title:"Operazione completata",description:e}),ea()},ec=e=>{x({title:"Errore",description:e,variant:"destructive"})};return m||f?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)(Z.A,{className:"h-8 w-8 animate-spin"})}):Y?j?(0,t.jsxs)("div",{className:"container mx-auto p-6",children:[(0,t.jsxs)(o.Fc,{variant:"destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:j})]}),(0,t.jsx)(r.$,{onClick:ea,className:"mt-4",children:"Riprova"})]}):(0,t.jsxs)("div",{className:"container mx-auto p-6",children:[(0,t.jsx)($,{cavi:p,filteredCavi:z,className:"mb-6"}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(P,{cavi:p,loading:f,selectionEnabled:C,selectedCavi:_,onSelectionChange:y,onStatusAction:es,onContextMenuAction:el})}),v.length>0&&(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(F.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Cavi Spare (",v.length,")"]})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)(P,{cavi:v,loading:f,selectionEnabled:!1,onStatusAction:es,onContextMenuAction:el})})]})}),!1,(0,t.jsx)(q,{open:S.open,onClose:()=>k({open:!1,cavo:null}),cavo:S.cavo,onSuccess:eo,onError:ec}),(0,t.jsx)(W,{open:E.open,onClose:()=>I({open:!1,cavo:null}),cavo:E.cavo,onSuccess:eo,onError:ec}),(0,t.jsx)(H,{open:O.open,onClose:()=>T({open:!1,cavo:null}),cavo:O.cavo,onSuccess:eo,onError:ec}),(0,t.jsx)(K,{open:B.open,onClose:()=>D({open:!1,cavo:null}),cavo:B.cavo,onSuccess:eo,onError:ec}),(0,t.jsx)(ei,{open:L.open,onClose:()=>M({open:!1}),caviSelezionati:_,tipoComanda:L.tipoComanda,onSuccess:eo,onError:ec}),(0,t.jsx)(en,{open:U.open,onClose:()=>V({open:!1}),tipo:U.tipo||"cavi",onSuccess:eo,onError:ec}),(0,t.jsx)(er,{open:J,onClose:()=>G(!1),onSuccess:eo,onError:ec})]}):(0,t.jsxs)("div",{className:"container mx-auto p-6",children:[(0,t.jsxs)(o.Fc,{children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]}),(0,t.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,t.jsx)("h3",{className:"font-bold",children:"Debug Info:"}),(0,t.jsxs)("p",{children:["User: ",e?e.username:"Non autenticato"]}),(0,t.jsxs)("p",{children:["Cantiere context: ",a?a.commessa:"Nessuno"]}),(0,t.jsxs)("p",{children:["Token presente: ",localStorage.getItem("token")?"S\xec":"No"]})]})]})}},26126:(e,a,i)=>{"use strict";i.d(a,{E:()=>o});var t=i(95155);i(12115);var s=i(99708),n=i(74466),l=i(59434);let r=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:a,variant:i,asChild:n=!1,...o}=e,c=n?s.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(r({variant:i}),a),...o})}},30285:(e,a,i)=>{"use strict";i.d(a,{$:()=>o});var t=i(95155);i(12115);var s=i(99708),n=i(74466),l=i(59434);let r=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:i,size:n,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,l.cn)(r({variant:i,size:n,className:a})),...c})}},47262:(e,a,i)=>{"use strict";i.d(a,{S:()=>r});var t=i(95155);i(12115);var s=i(76981),n=i(5196),l=i(59434);function r(e){let{className:a,...i}=e;return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...i,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(n.A,{className:"size-3.5"})})})}},54165:(e,a,i)=>{"use strict";i.d(a,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>r,rr:()=>h,zM:()=>o});var t=i(95155);i(12115);var s=i(15452),n=i(54416),l=i(59434);function r(e){let{...a}=e;return(0,t.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...i}=e;return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...i})}function m(e){let{className:a,children:i,showCloseButton:r=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[i,r&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",a),...i})}function x(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...i})}function p(e){let{className:a,...i}=e;return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",a),...i})}function h(e){let{className:a,...i}=e;return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",a),...i})}},55365:(e,a,i)=>{"use strict";i.d(a,{Fc:()=>o,TN:()=>c});var t=i(95155),s=i(12115),n=i(74466),l=i(59434);let r=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,a)=>{let{className:i,variant:s,...n}=e;return(0,t.jsx)("div",{ref:a,role:"alert",className:(0,l.cn)(r({variant:s}),i),...n})});o.displayName="Alert",s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("h5",{ref:a,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",i),...s})}).displayName="AlertTitle";let c=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("div",{ref:a,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",i),...s})});c.displayName="AlertDescription"},59409:(e,a,i)=>{"use strict";i.d(a,{bq:()=>m,eb:()=>x,gC:()=>u,l6:()=>c,yv:()=>d});var t=i(95155);i(12115);var s=i(38715),n=i(66474),l=i(5196),r=i(47863),o=i(59434);function c(e){let{...a}=e;return(0,t.jsx)(s.bL,{"data-slot":"select",...a})}function d(e){let{...a}=e;return(0,t.jsx)(s.WT,{"data-slot":"select-value",...a})}function m(e){let{className:a,size:i="default",children:l,...r}=e;return(0,t.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":i,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...r,children:[l,(0,t.jsx)(s.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:a,children:i,position:n="popper",...l}=e;return(0,t.jsx)(s.ZL,{children:(0,t.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...l,children:[(0,t.jsx)(p,{}),(0,t.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:i}),(0,t.jsx)(h,{})]})})}function x(e){let{className:a,children:i,...n}=e;return(0,t.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...n,children:[(0,t.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(s.VF,{children:(0,t.jsx)(l.A,{className:"size-4"})})}),(0,t.jsx)(s.p4,{children:i})]})}function p(e){let{className:a,...i}=e;return(0,t.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...i,children:(0,t.jsx)(r.A,{className:"size-4"})})}function h(e){let{className:a,...i}=e;return(0,t.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...i,children:(0,t.jsx)(n.A,{className:"size-4"})})}},59434:(e,a,i)=>{"use strict";i.d(a,{cn:()=>n});var t=i(52596),s=i(39688);function n(){for(var e=arguments.length,a=Array(e),i=0;i<e;i++)a[i]=arguments[i];return(0,s.QP)((0,t.$)(a))}},62523:(e,a,i)=>{"use strict";i.d(a,{p:()=>n});var t=i(95155);i(12115);var s=i(59434);function n(e){let{className:a,type:i,...n}=e;return(0,t.jsx)("input",{type:i,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...n})}},66695:(e,a,i)=>{"use strict";i.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>r,Zp:()=>n,aR:()=>l});var t=i(95155);i(12115);var s=i(59434);function n(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...i})}function l(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...i})}function r(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",a),...i})}function o(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",a),...i})}function c(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",a),...i})}},85057:(e,a,i)=>{"use strict";i.d(a,{J:()=>l});var t=i(95155);i(12115);var s=i(40968),n=i(59434);function l(e){let{className:a,...i}=e;return(0,t.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...i})}},85127:(e,a,i)=>{"use strict";i.d(a,{A0:()=>l,BF:()=>r,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var t=i(95155);i(12115);var s=i(59434);function n(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",a),...i})})}function l(e){let{className:a,...i}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",a),...i})}function r(e){let{className:a,...i}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",a),...i})}function o(e){let{className:a,...i}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...i})}function c(e){let{className:a,...i}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...i})}function d(e){let{className:a,...i}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...i})}}},e=>{var a=a=>e(e.s=a);e.O(0,[848,464,132,606,998,969,380,283,441,684,358],()=>a(3469)),_N_E=e.O()}]);