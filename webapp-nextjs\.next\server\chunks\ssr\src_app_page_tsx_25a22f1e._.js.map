{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (isAuthenticated) {\n        // Reindirizza automaticamente in base al ruolo dell'utente come nel sistema React originale\n        // Breve timeout per evitare reindirizzamenti troppo rapidi\n        const redirectTimer = setTimeout(() => {\n          if (user?.ruolo === 'owner') {\n            router.replace('/admin')\n          } else if (user?.ruolo === 'user') {\n            router.replace('/cantieri')\n          } else if (user?.ruolo === 'cantieri_user') {\n            router.replace('/cavi')\n          } else {\n            router.replace('/cantieri')\n          }\n        }, 300)\n\n        return () => clearTimeout(redirectTimer)\n      } else {\n        // Se non autenticato, reindirizza al login\n        router.replace('/login')\n      }\n    }\n  }, [isAuthenticated, isLoading, user, router])\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento (come nel React originale)\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold text-slate-900 mb-4\">\n          Benvenuto nel Sistema di Gestione Cantieri\n        </h1>\n        <p className=\"text-slate-600 mb-6\">\n          Reindirizzamento in corso...\n        </p>\n        <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto\"></div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,IAAI,iBAAiB;gBACnB,4FAA4F;gBAC5F,2DAA2D;gBAC3D,MAAM,gBAAgB,WAAW;oBAC/B,IAAI,MAAM,UAAU,SAAS;wBAC3B,OAAO,OAAO,CAAC;oBACjB,OAAO,IAAI,MAAM,UAAU,QAAQ;wBACjC,OAAO,OAAO,CAAC;oBACjB,OAAO,IAAI,MAAM,UAAU,iBAAiB;wBAC1C,OAAO,OAAO,CAAC;oBACjB,OAAO;wBACL,OAAO,OAAO,CAAC;oBACjB;gBACF,GAAG;gBAEH,OAAO,IAAM,aAAa;YAC5B,OAAO;gBACL,2CAA2C;gBAC3C,OAAO,OAAO,CAAC;YACjB;QACF;IACF,GAAG;QAAC;QAAiB;QAAW;QAAM;KAAO;IAE7C,6FAA6F;IAC7F,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BAGnC,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}]}