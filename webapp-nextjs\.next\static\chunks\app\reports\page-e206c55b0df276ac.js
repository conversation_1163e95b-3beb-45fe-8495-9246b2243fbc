(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[22],{24944:(e,t,s)=>{"use strict";s.d(t,{k:()=>l});var a=s(95155);s(12115);var r=s(55863),i=s(59434);function l(e){let{className:t,value:s,...l}=e;return(0,a.jsx)(r.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...l,children:(0,a.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(95155);s(12115);var r=s(99708),i=s(74466),l=s(59434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:i,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,l.cn)(n({variant:s,size:i,className:t})),...d})}},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var a=s(52596),r=s(39688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>l});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},79680:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>B});var a=s(95155),r=s(12115),i=s(66695),l=s(30285),n=s(24944),c=s(40283),d=s(91788),o=s(51154),x=s(85339),m=s(16785),u=s(33109),h=s(79397),p=s(40646),j=s(14186),v=s(69074),f=s(83540),g=s(3401),b=s(94754),N=s(96025),y=s(16238),w=s(94517),_=s(83394),k=s(8782),z=s(34e3),Z=s(54811);function B(){let[e,t]=(0,r.useState)("month"),[s,B]=(0,r.useState)(null),[R,A]=(0,r.useState)(null),[S,C]=(0,r.useState)(!0),[I,T]=(0,r.useState)(""),{user:W,cantiere:E}=(0,c.A)();(0,r.useEffect)(()=>{F()},[]);let F=async()=>{try{if(C(!0),T(""),!((null==E?void 0:E.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0")))return void T("Cantiere non selezionato");console.log("Report API endpoints non ancora implementati nel backend"),T("Report API endpoints in fase di implementazione")}catch(s){var e,t;console.error("Errore caricamento report:",s),T((null==(t=s.response)||null==(e=t.data)?void 0:e.detail)||"Errore durante il caricamento dei report")}finally{C(!1)}},P=s?[{name:"Installati",value:s.cavi_posati,color:"#22c55e"},{name:"Collegati",value:s.cavi_collegati,color:"#3b82f6"},{name:"Certificati",value:s.cavi_certificati,color:"#f59e0b"},{name:"Da Installare",value:s.totale_cavi-s.cavi_posati,color:"#94a3b8"}]:[];return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-end gap-2 mb-6",children:[["week","month","quarter"].map(s=>(0,a.jsx)(l.$,{variant:e===s?"default":"outline",size:"sm",onClick:()=>t(s),className:"capitalize",children:"week"===s?"Settimana":"month"===s?"Mese":"Trimestre"},s)),(0,a.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Esporta PDF"]})]}),S?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-6 w-6 animate-spin"}),(0,a.jsx)("span",{children:"Caricamento report..."})]})}):I?(0,a.jsxs)("div",{className:"p-6 border border-amber-200 rounded-lg bg-amber-50",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-amber-600 mr-2"}),(0,a.jsx)("span",{className:"text-amber-800 font-medium",children:"Report in fase di implementazione"})]}),(0,a.jsx)("p",{className:"text-amber-700 mb-4",children:"Gli endpoint per i report non sono ancora implementati nel backend. I report saranno disponibili nelle prossime versioni del sistema."}),(0,a.jsxs)("div",{className:"text-sm text-amber-600",children:[(0,a.jsx)("strong",{children:"Funzionalit\xe0 previste:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"Report avanzamento lavori"}),(0,a.jsx)("li",{children:"Report BOQ (Bill of Quantities)"}),(0,a.jsx)("li",{children:"Report utilizzo bobine"}),(0,a.jsx)("li",{children:"Statistiche di produttivit\xe0"})]})]})]}):s?(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento Totale"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-blue-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[s.percentuale_completamento.toFixed(1),"%"]}),(0,a.jsx)(n.k,{value:s.percentuale_completamento,className:"mt-2"}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[s.cavi_posati," di ",s.totale_cavi," cavi"]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-green-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Media Giornaliera"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:s.media_giornaliera.toFixed(1)}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"cavi/giorno"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(u.A,{className:"h-3 w-3 text-green-500 mr-1"}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["Basato su ",s.giorni_lavorativi," giorni"]})]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Completati"}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-purple-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[s.percentuale_metri.toFixed(1),"%"]}),(0,a.jsxs)("p",{className:"text-xs text-slate-500",children:[s.metri_posati,"m di ",s.metri_totali,"m"]}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-purple-600",children:"Metrature"})]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Stima Completamento"}),(0,a.jsx)(j.A,{className:"h-4 w-4 text-orange-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:s.stima_completamento}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"al ritmo attuale"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(v.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-orange-600",children:"Proiezione"})]})]})]})]}):null,(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Trend Installazioni Settimanali"}),(0,a.jsx)(i.BT,{children:"Confronto installazioni vs target giornaliero"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(f.u,{width:"100%",height:300,children:(0,a.jsxs)(g.E,{data:[],children:[(0,a.jsx)(b.d,{strokeDasharray:"3 3"}),(0,a.jsx)(N.W,{dataKey:"name"}),(0,a.jsx)(y.h,{}),(0,a.jsx)(w.m,{}),(0,a.jsx)(_.y,{dataKey:"target",fill:"#e2e8f0",name:"Target"}),(0,a.jsx)(_.y,{dataKey:"installati",fill:"#3b82f6",name:"Installati"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Distribuzione Stato Cavi"}),(0,a.jsx)(i.BT,{children:"Panoramica dello stato di avanzamento"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(f.u,{width:"100%",height:300,children:(0,a.jsxs)(k.r,{children:[(0,a.jsx)(z.F,{data:P,cx:"50%",cy:"50%",outerRadius:100,fill:"#8884d8",dataKey:"value",label:e=>{let{name:t,percent:s}=e;return"".concat(t," ").concat((100*s).toFixed(0),"%")},children:P.map((e,t)=>(0,a.jsx)(Z.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(w.m,{})]})})})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Avanzamento per Settore"}),(0,a.jsx)(i.BT,{children:"Stato di completamento dettagliato per ogni settore"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Dati di avanzamento per settore saranno disponibili quando collegati al backend"})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Performance Team"}),(0,a.jsx)(i.BT,{children:"Statistiche dettagliate per ogni squadra di lavoro"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Statistiche team saranno disponibili quando collegati al backend"})})]})]})})}},89569:(e,t,s)=>{Promise.resolve().then(s.bind(s,79680))}},e=>{var t=t=>e(e.s=t);e.O(0,[848,464,91,283,441,684,358],()=>t(89569)),_N_E=e.O()}]);