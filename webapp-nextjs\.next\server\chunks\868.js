"use strict";exports.id=868,exports.ids=[868],exports.modules={6211:(e,t,a)=>{a.d(t,{A0:()=>s,BF:()=>l,Hj:()=>i,XI:()=>n,nA:()=>c,nd:()=>d});var r=a(60687);a(43210);var o=a(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,o.cn)("w-full caption-bottom text-sm border-collapse",e),...t})})}function s({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,o.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,o.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,o.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,o.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},22126:(e,t,a)=>{a.d(t,{lG:()=>ee,Cf:()=>eo,rr:()=>ei,Es:()=>es,c7:()=>en,L3:()=>el,zM:()=>et});var r=a(60687),o=a(43210),n=a(70569),s=a(98599),l=a(11273),i=a(96963),d=a(65551),c=a(31355),u=a(32547),f=a(25028),p=a(46059),g=a(14163),m=a(1359),x=a(42247),b=a(63376),v=a(8730),h="Dialog",[j,y]=(0,l.A)(h),[w,N]=j(h),D=e=>{let{__scopeDialog:t,children:a,open:n,defaultOpen:s,onOpenChange:l,modal:c=!0}=e,u=o.useRef(null),f=o.useRef(null),[p,g]=(0,d.i)({prop:n,defaultProp:s??!1,onChange:l,caller:h});return(0,r.jsx)(w,{scope:t,triggerRef:u,contentRef:f,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:g,onOpenToggle:o.useCallback(()=>g(e=>!e),[g]),modal:c,children:a})};D.displayName=h;var R="DialogTrigger",C=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,l=N(R,a),i=(0,s.s)(t,l.triggerRef);return(0,r.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...o,ref:i,onClick:(0,n.m)(e.onClick,l.onOpenToggle)})});C.displayName=R;var k="DialogPortal",[I,_]=j(k,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:a,children:n,container:s}=e,l=N(k,t);return(0,r.jsx)(I,{scope:t,forceMount:a,children:o.Children.map(n,e=>(0,r.jsx)(p.C,{present:a||l.open,children:(0,r.jsx)(f.Z,{asChild:!0,container:s,children:e})}))})};A.displayName=k;var O="DialogOverlay",F=o.forwardRef((e,t)=>{let a=_(O,e.__scopeDialog),{forceMount:o=a.forceMount,...n}=e,s=N(O,e.__scopeDialog);return s.modal?(0,r.jsx)(p.C,{present:o||s.open,children:(0,r.jsx)(z,{...n,ref:t})}):null});F.displayName=O;var E=(0,v.TL)("DialogOverlay.RemoveScroll"),z=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,n=N(O,a);return(0,r.jsx)(x.A,{as:E,allowPinchZoom:!0,shards:[n.contentRef],children:(0,r.jsx)(g.sG.div,{"data-state":H(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),P="DialogContent",B=o.forwardRef((e,t)=>{let a=_(P,e.__scopeDialog),{forceMount:o=a.forceMount,...n}=e,s=N(P,e.__scopeDialog);return(0,r.jsx)(p.C,{present:o||s.open,children:s.modal?(0,r.jsx)(M,{...n,ref:t}):(0,r.jsx)(T,{...n,ref:t})})});B.displayName=P;var M=o.forwardRef((e,t)=>{let a=N(P,e.__scopeDialog),l=o.useRef(null),i=(0,s.s)(t,a.contentRef,l);return o.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,r.jsx)(G,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=o.forwardRef((e,t)=>{let a=N(P,e.__scopeDialog),n=o.useRef(!1),s=o.useRef(!1);return(0,r.jsx)(G,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||a.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(s.current=!0));let r=t.target;a.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),G=o.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:i,...d}=e,f=N(P,a),p=o.useRef(null),g=(0,s.s)(t,p);return(0,m.Oh)(),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,r.jsx)(c.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":H(f.open),...d,ref:g,onDismiss:()=>f.onOpenChange(!1)})}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(U,{titleId:f.titleId}),(0,r.jsx)(X,{contentRef:p,descriptionId:f.descriptionId})]})]})}),q="DialogTitle",W=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,n=N(q,a);return(0,r.jsx)(g.sG.h2,{id:n.titleId,...o,ref:t})});W.displayName=q;var $="DialogDescription",S=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,n=N($,a);return(0,r.jsx)(g.sG.p,{id:n.descriptionId,...o,ref:t})});S.displayName=$;var Z="DialogClose",V=o.forwardRef((e,t)=>{let{__scopeDialog:a,...o}=e,s=N(Z,a);return(0,r.jsx)(g.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>s.onOpenChange(!1))})});function H(e){return e?"open":"closed"}V.displayName=Z;var L="DialogTitleWarning",[J,K]=(0,l.q)(L,{contentName:P,titleName:q,docsSlug:"dialog"}),U=({titleId:e})=>{let t=K(L),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},X=({contentRef:e,descriptionId:t})=>{let a=K("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return o.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Q=a(11860),Y=a(4780);function ee({...e}){return(0,r.jsx)(D,{"data-slot":"dialog",...e})}function et({...e}){return(0,r.jsx)(C,{"data-slot":"dialog-trigger",...e})}function ea({...e}){return(0,r.jsx)(A,{"data-slot":"dialog-portal",...e})}function er({className:e,...t}){return(0,r.jsx)(F,{"data-slot":"dialog-overlay",className:(0,Y.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function eo({className:e,children:t,showCloseButton:a=!0,...o}){return(0,r.jsxs)(ea,{"data-slot":"dialog-portal",children:[(0,r.jsx)(er,{}),(0,r.jsxs)(B,{"data-slot":"dialog-content",className:(0,Y.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,a&&(0,r.jsxs)(V,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(Q.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function en({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,Y.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function es({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,Y.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function el({className:e,...t}){return(0,r.jsx)(W,{"data-slot":"dialog-title",className:(0,Y.cn)("text-lg leading-none font-semibold",e),...t})}function ei({className:e,...t}){return(0,r.jsx)(S,{"data-slot":"dialog-description",className:(0,Y.cn)("text-muted-foreground text-sm",e),...t})}},44493:(e,t,a)=>{a.d(t,{BT:()=>i,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>s});var r=a(60687);a(43210);var o=a(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function s({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var r=a(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80013:(e,t,a)=>{a.d(t,{J:()=>s});var r=a(60687);a(43210);var o=a(78148),n=a(4780);function s({className:e,...t}){return(0,r.jsx)(o.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},84027:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},89667:(e,t,a)=>{a.d(t,{p:()=>n});var r=a(60687);a(43210);var o=a(4780);function n({className:e,type:t,...a}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},99270:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};