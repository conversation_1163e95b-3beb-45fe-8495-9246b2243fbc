import React from 'react'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

interface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'outline' | 'quick'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  glow?: boolean
  icon?: React.ReactNode
  children: React.ReactNode
}

export const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ 
    className, 
    variant = 'primary', 
    size = 'md', 
    loading = false, 
    glow = false,
    icon,
    children, 
    disabled,
    ...props 
  }, ref) => {
    const baseClasses = 'relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none'
    
    const variantClasses = {
      primary: 'btn-primary',
      secondary: 'btn-secondary',
      success: 'btn-success',
      danger: 'btn-danger',
      outline: 'btn-outline',
      quick: 'btn-quick'
    }
    
    const sizeClasses = {
      sm: 'btn-sm',
      md: 'px-6 py-3',
      lg: 'btn-lg'
    }

    const isDisabled = disabled || loading

    return (
      <button
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          glow && variant !== 'quick' && 'btn-glow',
          isDisabled && 'opacity-50 cursor-not-allowed hover:shadow-none',
          className
        )}
        disabled={isDisabled}
        ref={ref}
        {...props}
      >
        {/* Effetto shimmer per hover */}
        <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]" />
        
        {/* Contenuto del pulsante */}
        <span className="relative flex items-center justify-center gap-2">
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin btn-icon" />
          ) : icon ? (
            <span className="btn-icon">{icon}</span>
          ) : null}
          {children}
        </span>
      </button>
    )
  }
)

AnimatedButton.displayName = 'AnimatedButton'

// Componenti specifici per facilità d'uso
export const PrimaryButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (
  <AnimatedButton variant="primary" {...props} />
)

export const SecondaryButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (
  <AnimatedButton variant="secondary" {...props} />
)

export const SuccessButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (
  <AnimatedButton variant="success" {...props} />
)

export const DangerButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (
  <AnimatedButton variant="danger" {...props} />
)

export const OutlineButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (
  <AnimatedButton variant="outline" {...props} />
)

export const QuickButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (
  <AnimatedButton variant="quick" {...props} />
)
