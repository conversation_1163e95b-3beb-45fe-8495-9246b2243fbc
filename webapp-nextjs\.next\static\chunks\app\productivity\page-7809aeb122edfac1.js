(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[451],{18337:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),r=t(12115),i=t(66695),n=t(30285),l=t(26126),c=t(24944),d=t(16785),o=t(71539),x=t(33109),m=t(17580),u=t(69074),f=t(14186),h=t(72713),v=t(79397);function g(){let[e,s]=(0,r.useState)("week");return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end mb-6",children:(0,a.jsx)("div",{className:"flex gap-2",children:["day","week","month"].map(t=>(0,a.jsx)(n.$,{variant:e===t?"default":"outline",size:"sm",onClick:()=>s(t),className:"capitalize",children:"day"===t?"Oggi":"week"===t?"Settimana":"Mese"},t))})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Avanzamento Totale"}),(0,a.jsx)(d.A,{className:"h-4 w-4 text-blue-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[71.2,"%"]}),(0,a.jsx)(c.k,{value:71.2,className:"mt-2"}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[890," di ",1250," cavi"]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-green-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Velocit\xe0 Installazione"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-green-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:12.5}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"cavi/ora per persona"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(x.A,{className:"h-3 w-3 text-green-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-green-600",children:"+12% vs settimana scorsa"})]})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Team Attivi"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-orange-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:8}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"squadre operative"}),(0,a.jsx)("div",{className:"flex items-center mt-2",children:(0,a.jsx)(l.E,{variant:"secondary",className:"text-xs",children:"16 persone"})})]})]}),(0,a.jsxs)(i.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento Stimato"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-purple-500"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:"15 giorni"}),(0,a.jsx)("p",{className:"text-xs text-slate-500",children:"al ritmo attuale"}),(0,a.jsxs)("div",{className:"flex items-center mt-2",children:[(0,a.jsx)(f.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,a.jsx)("span",{className:"text-xs text-purple-600",children:"Aggiornato ora"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}),"Performance Team"]}),(0,a.jsx)(i.BT,{children:"Statistiche dettagliate per squadra"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{name:"Team Alpha",members:4,installed:156,connected:98,certified:67,efficiency:92},{name:"Team Beta",members:3,installed:134,connected:89,certified:54,efficiency:88},{name:"Team Gamma",members:5,installed:189,connected:145,certified:89,efficiency:95},{name:"Team Delta",members:4,installed:167,connected:123,certified:78,efficiency:90}].map((e,s)=>(0,a.jsx)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900",children:e.name}),(0,a.jsxs)(l.E,{variant:e.efficiency>=90?"default":"secondary",children:[e.efficiency,"% efficienza"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm text-slate-600",children:[(0,a.jsxs)("div",{children:["Installati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.installed})]}),(0,a.jsxs)("div",{children:["Collegati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.connected})]}),(0,a.jsxs)("div",{children:["Certificati: ",(0,a.jsx)("span",{className:"font-medium text-slate-900",children:e.certified})]})]}),(0,a.jsx)(c.k,{value:e.efficiency,className:"mt-2 h-2"})]})},s))})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-green-600"}),"Attivit\xe0 Recenti"]}),(0,a.jsx)(i.BT,{children:"Ultime operazioni completate"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{time:"10:30",action:"Installazione completata",details:"Cavo FG16OM4-24 - Settore A",team:"Alpha"},{time:"10:15",action:"Collegamento certificato",details:"Cavo MM-OM3-12 - Settore B",team:"Beta"},{time:"09:45",action:"Nuova installazione",details:"Cavo SM-G652D-48 - Settore C",team:"Gamma"},{time:"09:30",action:"Test completato",details:"Cavo FG16OM4-12 - Settore A",team:"Delta"}].map((e,s)=>(0,a.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-slate-50 rounded-lg",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-slate-900",children:e.action}),(0,a.jsx)("span",{className:"text-xs text-slate-500",children:e.time})]}),(0,a.jsx)("p",{className:"text-sm text-slate-600 truncate",children:e.details}),(0,a.jsx)(l.E,{variant:"outline",className:"mt-1 text-xs",children:e.team})]})]},s))})})]})]})]})})}},24944:(e,s,t)=>{"use strict";t.d(s,{k:()=>n});var a=t(95155);t(12115);var r=t(55863),i=t(59434);function n(e){let{className:s,value:t,...n}=e;return(0,a.jsx)(r.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",s),...n,children:(0,a.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(t||0),"%)")}})})}},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:i=!1,...c}=e,d=i?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...c})}},28872:(e,s,t)=>{Promise.resolve().then(t.bind(t,18337))},30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var a=t(95155);t(12115);var r=t(99708),i=t(74466),n=t(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:t,size:i,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:s})),...d})}},59434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(52596),r=t(39688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=t(95155);t(12115);var r=t(59434);function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",s),...t})}}},e=>{var s=s=>e(e.s=s);e.O(0,[848,915,441,684,358],()=>s(28872)),_N_E=e.O()}]);