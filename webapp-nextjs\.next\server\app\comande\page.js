(()=>{var e={};e.id=484,e.ids=[484],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10281:(e,s,t)=>{Promise.resolve().then(t.bind(t,57842))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43649:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},44493:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=t(60687);t(43210);var r=t(4780);function i({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function l({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...s})}function c({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...s})}},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52137:(e,s,t)=>{Promise.resolve().then(t.bind(t,69018))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57842:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\comande\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx","default")},59316:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["comande",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,57842)),"C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/comande/page",pathname:"/comande",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},69018:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(60687),r=t(43210),i=t(44493),n=t(29523),l=t(96834),c=t(63213);t(62185);var d=t(96474),o=t(6727),x=t(62688);let m=(0,x.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var p=t(5336),u=t(48730),h=t(41862),j=t(43649),v=t(13861),g=t(63143);let f=(0,x.A)("pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);var b=t(41312);function N(){let[e,s]=(0,r.useState)("active"),[t,x]=(0,r.useState)([]),[N,y]=(0,r.useState)([]),[A,w]=(0,r.useState)(!0),[C,_]=(0,r.useState)(""),{user:k,cantiere:E}=(0,c.A)(),S=e=>{switch(e){case"completata":return(0,a.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"in_corso":return(0,a.jsx)(l.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"pianificata":return(0,a.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"Pianificata"});case"sospesa":return(0,a.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Sospesa"});default:return(0,a.jsx)(l.E,{variant:"secondary",children:e})}},z=e=>(0,a.jsx)(l.E,{className:{POSA:"bg-blue-100 text-blue-800",COLLEGAMENTO_PARTENZA:"bg-green-100 text-green-800",COLLEGAMENTO_ARRIVO:"bg-purple-100 text-purple-800",CERTIFICAZIONE:"bg-orange-100 text-orange-800"}[e]||"bg-gray-100 text-gray-800",children:e}),T=t.filter(s=>{switch(e){case"active":return"IN_CORSO"===s.stato||"ASSEGNATA"===s.stato||"CREATA"===s.stato;case"completed":return"COMPLETATA"===s.stato;default:return!0}}),q={totali:t.length,in_corso:t.filter(e=>"IN_CORSO"===e.stato).length,completate:t.filter(e=>"COMPLETATA"===e.stato).length,pianificate:t.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-end mb-6",children:(0,a.jsxs)(n.$,{size:"sm",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:q.totali})]}),(0,a.jsx)(o.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:q.in_corso})]}),(0,a.jsx)(m,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Completate"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:q.completate})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Pianificate"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:q.pianificate})]}),(0,a.jsx)(u.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(i.ZB,{children:"Elenco Comande"}),(0,a.jsx)("div",{className:"flex gap-2",children:[{key:"active",label:"Attive"},{key:"completed",label:"Completate"},{key:"all",label:"Tutte"}].map(t=>(0,a.jsx)(n.$,{variant:e===t.key?"default":"outline",size:"sm",onClick:()=>s(t.key),children:t.label},t.key))})]})}),(0,a.jsx)(i.Wu,{children:A?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})}):C?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-600",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),C]})}):0===T.length?(0,a.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"}):(0,a.jsx)("div",{className:"space-y-4",children:T.map(e=>(0,a.jsx)(i.Zp,{className:"border-l-4 border-l-blue-500",children:(0,a.jsxs)(i.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-slate-900",children:e.codice_comanda}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:e.descrizione||"Nessuna descrizione"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[z(e.tipo_comanda),S(e.stato)]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Responsabile"}),(0,a.jsx)("p",{className:"font-medium",children:e.responsabile||"Non assegnato"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Team"}),(0,a.jsxs)("p",{className:"font-medium",children:[e.numero_componenti_squadra||0," persone"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Creazione"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-slate-500",children:"Scadenza"}),(0,a.jsx)("p",{className:"font-medium",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"Non definita"})]})]}),e.data_completamento&&(0,a.jsx)("div",{className:"mb-3 p-2 bg-green-50 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700",children:["Completata il ",new Date(e.data_completamento).toLocaleDateString("it-IT")]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Dettagli"]}),(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-1"}),"Modifica"]}),"IN_CORSO"===e.stato&&(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(f,{className:"h-4 w-4 mr-1"}),"Sospendi"]}),("CREATA"===e.stato||"ASSEGNATA"===e.stato)&&(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-1"}),"Avvia"]})]})]})},e.codice_comanda))})})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5"}),"Responsabili"]}),(0,a.jsx)(i.BT,{children:"Gestione responsabili di cantiere"})]}),(0,a.jsx)(i.Wu,{children:A?(0,a.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 animate-spin mr-2"}),"Caricamento..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"space-y-4",children:N.map(e=>{let s=t.filter(s=>s.responsabile===e.nome_responsabile&&("IN_CORSO"===s.stato||"ASSEGNATA"===s.stato)).length;return(0,a.jsxs)("div",{className:"p-3 bg-slate-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900",children:e.nome_responsabile}),(0,a.jsxs)(l.E,{variant:s>0?"default":"secondary",children:[s," attive"]})]}),(0,a.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[e.numero_telefono&&(0,a.jsx)("p",{children:e.numero_telefono}),e.mail&&(0,a.jsx)("p",{children:e.mail})]})]},e.id_responsabile)})}),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",className:"w-full mt-4",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Nuovo Responsabile"]})]})})]})})]})]})})}},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(60687);t(43210);var r=t(8730),i=t(24224),n=t(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:s,asChild:t=!1,...i}){let c=t?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:s}),e),...i})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,538,658,615],()=>t(59316));module.exports=a})();