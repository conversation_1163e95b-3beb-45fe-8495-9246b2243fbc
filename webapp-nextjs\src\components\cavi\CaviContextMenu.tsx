'use client'

import { useState } from 'react'
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuSub,
  ContextMenuSubContent,
  ContextMenuSubTrigger,
  ContextMenuTrigger,
} from '@/components/ui/context-menu'
import { Cavo } from '@/types'
import {
  Eye,
  Edit,
  Trash2,
  Copy,
  Plus,
  Minus,
  CheckSquare,
  Square,
  FileText,
  Link,
  Zap,
  Award,
  Settings,
  Users,
  ClipboardList
} from 'lucide-react'

interface CaviContextMenuProps {
  children: React.ReactNode
  cavo: Cavo
  isSelected: boolean
  hasMultipleSelection: boolean
  totalSelectedCount: number
  onAction: (cavo: Cavo, action: string) => void
}

export default function CaviContextMenu({
  children,
  cavo,
  isSelected,
  hasMultipleSelection,
  totalSelectedCount,
  onAction
}: CaviContextMenuProps) {

  const handleAction = (action: string) => {
    onAction(cavo, action)
  }

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent className="w-64">
        
        {/* Sezione comande multiple (solo se ci sono più cavi selezionati) */}
        {hasMultipleSelection && (
          <>
            <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
              📋 Crea Comande Multiple ({totalSelectedCount} cavi)
            </div>
            
            <ContextMenuItem onClick={() => handleAction('create_command_posa')}>
              <Settings className="mr-2 h-4 w-4" />
              Comanda Posa
            </ContextMenuItem>
            
            <ContextMenuItem onClick={() => handleAction('create_command_collegamento_partenza')}>
              <Link className="mr-2 h-4 w-4" />
              Comanda Collegamento Partenza
            </ContextMenuItem>
            
            <ContextMenuItem onClick={() => handleAction('create_command_collegamento_arrivo')}>
              <Zap className="mr-2 h-4 w-4" />
              Comanda Collegamento Arrivo
            </ContextMenuItem>
            
            <ContextMenuItem onClick={() => handleAction('create_command_certificazione')}>
              <Award className="mr-2 h-4 w-4" />
              Comanda Certificazione
            </ContextMenuItem>
            
            <ContextMenuSeparator />
            
            <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
              📋 Gestione Comande Esistenti ({totalSelectedCount} cavi)
            </div>
            
            <ContextMenuItem onClick={() => handleAction('add_multiple_to_command')}>
              <ClipboardList className="mr-2 h-4 w-4" />
              Aggiungi Tutti a Comanda
            </ContextMenuItem>
            
            <ContextMenuItem onClick={() => handleAction('remove_multiple_from_commands')}>
              <Minus className="mr-2 h-4 w-4" />
              Rimuovi Tutti dalle Comande
            </ContextMenuItem>
            
            <ContextMenuSeparator />
          </>
        )}

        {/* Azioni singolo cavo (solo se non c'è selezione multipla) */}
        {!hasMultipleSelection && (
          <>
            <ContextMenuItem onClick={() => handleAction('view_details')}>
              <Eye className="mr-2 h-4 w-4" />
              Visualizza Dettagli
            </ContextMenuItem>
            
            <ContextMenuSeparator />
            
            <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
              📋 Gestione Comande
            </div>
            
            <ContextMenuItem onClick={() => handleAction('add_to_command')}>
              <ClipboardList className="mr-2 h-4 w-4" />
              Aggiungi a Comanda
            </ContextMenuItem>
            
            {(cavo.comanda_posa || cavo.comanda_partenza || cavo.comanda_arrivo || cavo.comanda_certificazione) && (
              <ContextMenuItem onClick={() => handleAction('remove_from_command')}>
                <Minus className="mr-2 h-4 w-4" />
                Rimuovi da Comanda
              </ContextMenuItem>
            )}
            
            <ContextMenuSeparator />
            
            <ContextMenuItem onClick={() => handleAction('edit')}>
              <Edit className="mr-2 h-4 w-4" />
              Modifica
            </ContextMenuItem>
            
            <ContextMenuItem onClick={() => handleAction('delete')} className="text-red-600">
              <Trash2 className="mr-2 h-4 w-4" />
              Elimina
            </ContextMenuItem>
            
            <ContextMenuSeparator />
            
            <ContextMenuItem onClick={() => handleAction('add_new')}>
              <Plus className="mr-2 h-4 w-4" />
              Aggiungi nuovo cavo
            </ContextMenuItem>
            
            <ContextMenuSeparator />
          </>
        )}

        {/* Azioni di selezione (sempre presenti) */}
        <ContextMenuItem onClick={() => handleAction('select')}>
          {isSelected ? (
            <>
              <Square className="mr-2 h-4 w-4" />
              Deseleziona
            </>
          ) : (
            <>
              <CheckSquare className="mr-2 h-4 w-4" />
              Seleziona
            </>
          )}
        </ContextMenuItem>

        {/* Azioni di copia (sempre presenti) */}
        <ContextMenuSeparator />
        
        <ContextMenuItem onClick={() => handleAction('copy_id')}>
          <Copy className="mr-2 h-4 w-4" />
          {hasMultipleSelection ? 'Copia IDs Selezionati' : 'Copia ID'}
          <span className="ml-auto text-xs text-muted-foreground">Ctrl+C</span>
        </ContextMenuItem>
        
        <ContextMenuItem onClick={() => handleAction('copy_details')}>
          <FileText className="mr-2 h-4 w-4" />
          {hasMultipleSelection ? 'Copia Dettagli Selezionati' : 'Copia Dettagli'}
        </ContextMenuItem>

      </ContextMenuContent>
    </ContextMenu>
  )
}
