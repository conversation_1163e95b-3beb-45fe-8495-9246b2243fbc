(()=>{var e={};e.id=849,e.ids=[849],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20554:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["test-password-reset",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\CMS\\webapp-nextjs\\src\\app\\test-password-reset\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\test-password-reset\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-password-reset/page",pathname:"/test-password-reset",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34595:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\test-password-reset\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\test-password-reset\\page.tsx","default")},35866:(e,t,r)=>{Promise.resolve().then(r.bind(r,34595))},37657:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),a=r(43210),i=r(29523),n=r(89667),o=r(44493),d=r(91821);function l(){let[e,t]=(0,a.useState)("<EMAIL>"),[r,l]=(0,a.useState)(null),[c,u]=(0,a.useState)(!1),p=async()=>{u(!0),l(null);try{console.log("Testing password reset for:",e);let t=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,user_type:"user"})}),r=await t.json();l({status:t.status,success:t.ok,data:r,headers:Object.fromEntries(t.headers.entries())}),console.log("Response:",{status:t.status,data:r})}catch(e){console.error("Error:",e),l({success:!1,error:e instanceof Error?e.message:"Unknown error"})}finally{u(!1)}},x=async()=>{u(!0),l(null);try{console.log("Testing backend direct for:",e);let t=await fetch("http://localhost:8001/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,user_type:"user"})}),r=await t.json();l({status:t.status,success:t.ok,data:r,headers:Object.fromEntries(t.headers.entries()),direct:!0}),console.log("Direct Response:",{status:t.status,data:r})}catch(e){console.error("Direct Error:",e),l({success:!1,error:e instanceof Error?e.message:"Unknown error",direct:!0})}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsxs)(o.Zp,{className:"w-full max-w-2xl",children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Test Password Reset System"})}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Email to test:"}),(0,s.jsx)(n.p,{type:"email",value:e,onChange:e=>t(e.target.value),placeholder:"Enter email to test"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(i.$,{onClick:p,disabled:c,className:"flex-1",children:c?"Testing...":"Test via Next.js API"}),(0,s.jsx)(i.$,{onClick:x,disabled:c,variant:"outline",className:"flex-1",children:c?"Testing...":"Test Backend Direct"})]}),r&&(0,s.jsx)(d.Fc,{className:r.success?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,s.jsx)(d.TN,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Status:"})," ",r.status||"N/A"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Success:"})," ",r.success?"Yes":"No"]}),r.direct&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Method:"})," Direct Backend"]}),r.error&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Error:"})," ",r.error]}),r.data&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Response Data:"}),(0,s.jsx)("pre",{className:"mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(r.data,null,2)})]})]})})})]})]})})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70146:(e,t,r)=>{Promise.resolve().then(r.bind(r,37657))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>l});var s=r(60687),a=r(43210),i=r(24224),n=r(4780);let o=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef(({className:e,variant:t,...r},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(o({variant:t}),e),...r}));d.displayName="Alert",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...t}));l.displayName="AlertDescription"},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,538,658,615],()=>r(20554));module.exports=s})();