'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { useAuth } from '@/contexts/AuthContext'
import { parcoCaviApi } from '@/lib/api'
import { ParcoCavo } from '@/types'
import { 
  Package, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Download,
  Upload,
  Loader2
} from 'lucide-react'

export default function ParcoCaviPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [bobine, setBobine] = useState<ParcoCavo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()

  // Carica le bobine dal backend
  useEffect(() => {
    loadBobine()
  }, [])

  const loadBobine = async () => {
    try {
      setIsLoading(true)
      setError('')
      
      const cantiereId = cantiere?.id_cantiere || user?.id_utente
      if (!cantiereId) {
        setError('Cantiere non selezionato')
        return
      }

      const data = await parcoCaviApi.getBobine(cantiereId)
      setBobine(data)
    } catch (error: any) {
      console.error('Errore caricamento bobine:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento delle bobine')
    } finally {
      setIsLoading(false)
    }
  }

  // Ricarica quando cambiano i filtri
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadBobine()
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm, selectedStatus])

  const getStatusBadge = (stato: string, metri_residui: number, metri_totali: number) => {
    const percentualeResidue = metri_totali > 0 ? (metri_residui / metri_totali) * 100 : 0
    
    if (percentualeResidue === 0) {
      return <Badge className="bg-red-100 text-red-800">Esaurita</Badge>
    } else if (percentualeResidue < 20) {
      return <Badge className="bg-orange-100 text-orange-800">Quasi Esaurita</Badge>
    } else if (percentualeResidue < 50) {
      return <Badge className="bg-yellow-100 text-yellow-800">In Uso</Badge>
    } else {
      return <Badge className="bg-green-100 text-green-800">Disponibile</Badge>
    }
  }

  const filteredBobine = bobine.filter(bobina => {
    const matchesSearch = bobina.id_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         bobina.utility?.toLowerCase().includes(searchTerm.toLowerCase())
    
    let matchesStatus = true
    if (selectedStatus !== 'all') {
      const percentualeResidue = bobina.metri_totali > 0 ? (bobina.metri_residui / bobina.metri_totali) * 100 : 0
      switch (selectedStatus) {
        case 'disponibile':
          matchesStatus = percentualeResidue >= 50
          break
        case 'in_uso':
          matchesStatus = percentualeResidue > 0 && percentualeResidue < 50
          break
        case 'esaurita':
          matchesStatus = percentualeResidue === 0
          break
      }
    }
    
    return matchesSearch && matchesStatus
  })

  const stats = {
    totali: bobine.length,
    disponibili: bobine.filter(b => b.metri_totali > 0 ? (b.metri_residui / b.metri_totali) >= 0.5 : false).length,
    in_uso: bobine.filter(b => {
      const perc = b.metri_totali > 0 ? (b.metri_residui / b.metri_totali) : 0
      return perc > 0 && perc < 0.5
    }).length,
    esaurite: bobine.filter(b => b.metri_residui === 0).length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Action buttons */}
        <div className="flex justify-end gap-2 mb-6">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Esporta
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Importa
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Nuova Bobina
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Totali</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
                </div>
                <Package className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Disponibili</p>
                  <p className="text-2xl font-bold text-green-600">{stats.disponibili}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">In Uso</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.in_uso}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Esaurite</p>
                  <p className="text-2xl font-bold text-red-600">{stats.esaurite}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Ricerca e Filtri
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Cerca per ID bobina, numero, tipologia o utility..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                {['all', 'disponibile', 'in_uso', 'esaurita'].map((status) => (
                  <Button
                    key={status}
                    variant={selectedStatus === status ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedStatus(status)}
                  >
                    {status === 'all' ? 'Tutte' : 
                     status === 'disponibile' ? 'Disponibili' :
                     status === 'in_uso' ? 'In Uso' : 'Esaurite'}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bobine Table */}
        <Card>
          <CardHeader>
            <CardTitle>Elenco Bobine ({filteredBobine.length})</CardTitle>
            <CardDescription>
              Gestione completa delle bobine con stato utilizzo e metrature
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID Bobina</TableHead>
                    <TableHead>Numero</TableHead>
                    <TableHead>Utility</TableHead>
                    <TableHead>Tipologia</TableHead>
                    <TableHead>Conduttori/Sezione</TableHead>
                    <TableHead>Metrature</TableHead>
                    <TableHead>Utilizzo</TableHead>
                    <TableHead>Stato</TableHead>
                    <TableHead>Ubicazione</TableHead>
                    <TableHead>Azioni</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Caricamento bobine...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2 text-red-600">
                          <AlertCircle className="h-4 w-4" />
                          {error}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : filteredBobine.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8 text-slate-500">
                        Nessuna bobina trovata
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredBobine.map((bobina) => {
                      const percentualeUtilizzo = bobina.metri_totali > 0 ? 
                        ((bobina.metri_totali - bobina.metri_residui) / bobina.metri_totali) * 100 : 0
                      
                      return (
                        <TableRow key={bobina.id_bobina}>
                          <TableCell className="font-medium">{bobina.id_bobina}</TableCell>
                          <TableCell>{bobina.numero_bobina || '-'}</TableCell>
                          <TableCell>{bobina.utility || '-'}</TableCell>
                          <TableCell>{bobina.tipologia || '-'}</TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>{bobina.n_conduttori || '-'}</div>
                              <div className="text-slate-500">{bobina.sezione || '-'}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div>Residui: <span className="font-medium">{bobina.metri_residui}m</span></div>
                              <div className="text-slate-500">Totali: {bobina.metri_totali}m</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="text-sm font-medium">{Math.round(percentualeUtilizzo)}%</div>
                              <Progress value={percentualeUtilizzo} className="h-2" />
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(bobina.stato_bobina, bobina.metri_residui, bobina.metri_totali)}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{bobina.ubicazione_bobina || 'Non specificata'}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
