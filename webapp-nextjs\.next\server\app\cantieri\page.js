(()=>{var e={};e.id=222,e.ids=[222],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},45900:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>y});var a=s(60687),t=s(43210),i=s(16189),o=s(44493),n=s(29523),l=s(89667),c=s(80013),d=s(6211),m=s(22126),h=s(63213),p=s(62185),u=s(41862),x=s(99270),f=s(96474),b=s(93613),g=s(17313),w=s(64021),v=s(84027),j=s(13861),N=s(41550),C=s(5336),_=s(99891);function y(){let{user:e,isAuthenticated:r,isLoading:s}=(0,h.A)(),y=(0,i.useRouter)(),[z,P]=(0,t.useState)([]),[A,k]=(0,t.useState)(!0),[E,S]=(0,t.useState)(""),[q,$]=(0,t.useState)(""),[I,M]=(0,t.useState)(!1),[F,J]=(0,t.useState)(!1),[G,L]=(0,t.useState)(!1),[R,O]=(0,t.useState)(null),[T,D]=(0,t.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[B,H]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[V,X]=(0,t.useState)("change"),[Z,K]=(0,t.useState)(""),[W,Q]=(0,t.useState)(!1),U=async()=>{try{k(!0);let e=await p._I.getCantieri();P(e)}catch(e){console.error("Errore nel caricamento cantieri:",e),S("Errore nel caricamento dei cantieri")}finally{k(!1)}},Y=async()=>{try{await p._I.createCantiere(T),M(!1),D({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),U()}catch(e){console.error("Errore nella creazione cantiere:",e),S("Errore nella creazione del cantiere")}},ee=async()=>{if(R)try{await p._I.updateCantiere(R.id_cantiere,T),J(!1),O(null),U()}catch(e){console.error("Errore nella modifica cantiere:",e),S("Errore nella modifica del cantiere")}},er=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),y.push(`/cantieri/${e.id_cantiere}`)},es=e=>{O(e),D({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),J(!0)},ea=async()=>{if(R)try{k(!0),S(""),console.log("Recupero diretto password per cantiere:",R.id_cantiere);let e=await fetch(`http://localhost:8001/api/cantieri/${R.id_cantiere}/view-password`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok){let r=await e.json();throw Error(r.detail||"Errore nel recupero password")}let r=await e.json();K(r.password_cantiere),Q(!0),S("")}catch(e){console.error("Errore nel recupero password:",e),S(e instanceof Error?e.message:"Errore nel recupero password"),Q(!1)}finally{k(!1)}},et=async()=>{if(R)try{k(!0),S(""),console.log("Invio password via email per cantiere:",R.id_cantiere);let e=await fetch(`http://localhost:8001/api/cantieri/${R.id_cantiere}/send-password-email`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok){let r=await e.json();throw Error(r.detail||"Errore nell'invio email")}let r=await e.json();alert(r.message||"Password inviata via email con successo"),S("")}catch(e){console.error("Errore nell'invio email:",e),S(e instanceof Error?e.message:"Errore nell'invio email")}finally{k(!1)}},ei=async()=>{if(R){if(B.newPassword!==B.confirmPassword)return void S("Le password non coincidono");if(!B.currentPassword)return void S("Inserisci la password attuale per confermare il cambio");if(!B.newPassword||B.newPassword.length<6)return void S("La nuova password deve essere di almeno 6 caratteri");try{k(!0),S(""),console.log("Cambio password per cantiere:",R.id_cantiere);let e=await fetch(`http://localhost:8001/api/cantieri/${R.id_cantiere}/change-password`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({password_attuale:B.currentPassword,password_nuova:B.newPassword,conferma_password:B.confirmPassword})});if(!e.ok){let r=await e.json();throw Error(r.detail||"Errore nel cambio password")}let r=await e.json();if(r.success)H({currentPassword:"",newPassword:"",confirmPassword:""}),L(!1),S(""),alert(r.message||"Password cambiata con successo");else throw Error(r.message||"Errore nel cambio password")}catch(e){console.error("Errore nel cambio password:",e),S(e instanceof Error?e.message:"Errore nel cambio password")}finally{k(!1)}}},eo=z.filter(e=>e.commessa.toLowerCase().includes(q.toLowerCase())||e.descrizione?.toLowerCase().includes(q.toLowerCase())||e.nome_cliente?.toLowerCase().includes(q.toLowerCase()));return s?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)(u.A,{className:"h-8 w-8 animate-spin"})}):(0,a.jsxs)("div",{className:"container mx-auto p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"relative w-80",children:[(0,a.jsx)(x.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(l.p,{placeholder:"Cerca cantieri...",value:q,onChange:e=>$(e.target.value),className:"pl-8 w-full"})]})}),(0,a.jsxs)(m.lG,{open:I,onOpenChange:M,children:[(0,a.jsx)(m.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,a.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,a.jsx)(l.p,{id:"commessa",value:T.commessa,onChange:e=>D({...T,commessa:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)(l.p,{id:"descrizione",value:T.descrizione,onChange:e=>D({...T,descrizione:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)(l.p,{id:"nome_cliente",value:T.nome_cliente,onChange:e=>D({...T,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,a.jsx)(l.p,{id:"password_cantiere",type:"password",value:T.password_cantiere,onChange:e=>D({...T,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,a.jsx)(m.Es,{children:(0,a.jsx)(n.$,{onClick:Y,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Crea Cantiere"})})]})]})]}),E&&(0,a.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,a.jsx)("span",{className:"text-red-800",children:E})]})}),A?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(u.A,{className:"h-8 w-8 animate-spin"})}):0===eo.length?(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(o.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(g.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:q?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!q&&(0,a.jsxs)(n.$,{onClick:()=>M(!0),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nd,{children:"Commessa"}),(0,a.jsx)(d.nd,{children:"Descrizione"}),(0,a.jsx)(d.nd,{children:"Cliente"}),(0,a.jsx)(d.nd,{children:"Data Creazione"}),(0,a.jsx)(d.nd,{children:"Codice"}),(0,a.jsx)(d.nd,{children:"Password"}),(0,a.jsx)(d.nd,{className:"text-right",children:"Azioni"})]})}),(0,a.jsx)(d.BF,{children:eo.map(e=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{className:"font-medium",children:e.commessa}),(0,a.jsx)(d.nA,{children:e.descrizione}),(0,a.jsx)(d.nA,{children:e.nome_cliente}),(0,a.jsx)(d.nA,{children:new Date(e.data_creazione).toLocaleDateString()}),(0,a.jsx)(d.nA,{children:(0,a.jsx)("code",{className:"text-sm bg-muted px-2 py-1 rounded",children:e.codice_univoco})}),(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("code",{className:"text-sm bg-green-100 text-green-800 px-2 py-1 rounded",children:e.password_cantiere?"••••••••":"Non impostata"}),(0,a.jsx)(n.$,{size:"sm",variant:"ghost",className:"text-blue-600 hover:bg-blue-50 p-1",title:"Gestisci password cantiere",onClick:()=>{O(e),L(!0)},children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]})}),(0,a.jsx)(d.nA,{className:"text-right",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)(n.$,{size:"sm",onClick:()=>es(e),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",title:"Modifica cantiere",children:(0,a.jsx)(v.A,{className:"h-3 w-3"})}),(0,a.jsxs)(n.$,{size:"sm",onClick:()=>er(e),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:["Gestisci",(0,a.jsx)(j.A,{className:"ml-2 h-3 w-3"})]})]})})]},e.id_cantiere))})]})}),(0,a.jsx)(m.lG,{open:F,onOpenChange:J,children:(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,a.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,a.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,a.jsx)(l.p,{id:"edit-commessa",value:T.commessa,onChange:e=>D({...T,commessa:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,a.jsx)(l.p,{id:"edit-descrizione",value:T.descrizione,onChange:e=>D({...T,descrizione:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,a.jsx)(l.p,{id:"edit-nome_cliente",value:T.nome_cliente,onChange:e=>D({...T,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,a.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:T.indirizzo_cantiere,onChange:e=>D({...T,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,a.jsx)(l.p,{id:"edit-citta_cantiere",value:T.citta_cantiere,onChange:e=>D({...T,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,a.jsx)(l.p,{id:"edit-nazione_cantiere",value:T.nazione_cantiere,onChange:e=>D({...T,nazione_cantiere:e.target.value}),className:"col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"edit-password_cantiere",className:"text-right",children:"Password"}),(0,a.jsx)(l.p,{id:"edit-password_cantiere",type:"password",value:T.password_cantiere,onChange:e=>D({...T,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,a.jsxs)(m.Es,{children:[(0,a.jsx)(n.$,{onClick:()=>J(!1),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Annulla"}),(0,a.jsx)(n.$,{onClick:ee,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Salva Modifiche"})]})]})}),(0,a.jsx)(m.lG,{open:G,onOpenChange:e=>{L(e),e||(X("change"),Q(!1),K(""),H({currentPassword:"",newPassword:"",confirmPassword:""}))},children:(0,a.jsxs)(m.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5"}),"Gestione Password - ",R?.commessa]}),(0,a.jsx)(m.rr,{children:"Scegli come gestire la password del cantiere"})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:[(0,a.jsxs)("button",{onClick:()=>X("change"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"change"===V?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(v.A,{className:"inline mr-2 h-4 w-4"}),"Cambia"]}),(0,a.jsxs)("button",{onClick:()=>X("recover"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"recover"===V?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[(0,a.jsx)(N.A,{className:"inline mr-2 h-4 w-4"}),"Recupera"]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:["change"===V&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Cambia Password"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci la password attuale e la nuova password"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"current-password-change",children:"Password Attuale"}),(0,a.jsx)(l.p,{id:"current-password-change",type:"password",placeholder:"Password attuale per conferma",value:B.currentPassword,onChange:e=>H({...B,currentPassword:e.target.value})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"new-password",children:"Nuova Password"}),(0,a.jsx)(l.p,{id:"new-password",type:"password",placeholder:"Inserisci la nuova password",value:B.newPassword,onChange:e=>H({...B,newPassword:e.target.value})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"confirm-password",children:"Conferma Nuova Password"}),(0,a.jsx)(l.p,{id:"confirm-password",type:"password",placeholder:"Conferma la nuova password",value:B.confirmPassword,onChange:e=>H({...B,confirmPassword:e.target.value})})]}),(0,a.jsxs)(n.$,{onClick:ei,disabled:A||!B.currentPassword||!B.newPassword||!B.confirmPassword,className:"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[A?(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})]})]}),"recover"===V&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),"Recupera Password"]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Opzioni per recuperare una password dimenticata"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 border border-blue-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"Recupero Diretto"}),(0,a.jsx)("p",{className:"text-sm text-blue-700 mb-3",children:"Tenta di recuperare la password dal sistema (funziona solo se la password \xe8 stata salvata in formato recuperabile)"}),W&&(0,a.jsxs)("div",{className:"mb-3 p-3 bg-green-50 border border-green-200 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(C.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"font-medium text-green-800",children:"Password Recuperata"})]}),(0,a.jsx)("code",{className:"text-lg font-mono bg-white p-2 rounded border block",children:Z})]}),(0,a.jsxs)(n.$,{onClick:ea,disabled:A,className:"w-full relative overflow-hidden bg-orange-600 hover:bg-orange-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-orange-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[A?(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(_.A,{className:"mr-2 h-4 w-4"}),"Recupera Password"]})]}),(0,a.jsxs)("div",{className:"p-4 border border-green-200 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-green-800 mb-2",children:"Invio via Email"}),(0,a.jsx)("p",{className:"text-sm text-green-700 mb-3",children:"Invia la password all'indirizzo email dell'amministratore del cantiere"}),(0,a.jsxs)(n.$,{onClick:et,disabled:A,className:"w-full relative overflow-hidden bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-green-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[A?(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,a.jsx)(N.A,{className:"mr-2 h-4 w-4"}),"Invia Password via Email"]})]})]})]}),E&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,a.jsx)("p",{className:"text-sm text-red-700 mt-1",children:E})]})]}),(0,a.jsx)(m.Es,{children:(0,a.jsx)(n.$,{variant:"outline",onClick:()=>L(!1),children:"Chiudi"})})]})})]})}},47386:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=s(65239),t=s(48088),i=s(88170),o=s.n(i),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(r,l);let c={children:["",{children:["cantieri",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90910)),"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/cantieri/page",pathname:"/cantieri",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},65087:(e,r,s)=>{Promise.resolve().then(s.bind(s,90910))},74075:e=>{"use strict";e.exports=require("zlib")},75863:(e,r,s)=>{Promise.resolve().then(s.bind(s,45900))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90910:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99891:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[447,538,658,952,615,868],()=>s(47386));module.exports=a})();