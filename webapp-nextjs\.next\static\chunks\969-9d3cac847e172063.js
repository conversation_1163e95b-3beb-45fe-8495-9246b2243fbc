"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[969],{1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3493:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},11275:(e,t,n)=>{n.d(t,{X:()=>l});var r=n(12115),o=n(52712);function l(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},14186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},35152:(e,t,n)=>{n.d(t,{Mz:()=>eZ,i3:()=>eJ,UC:()=>e$,bL:()=>eY,Bk:()=>eM});var r=n(12115);let o=["top","right","bottom","left"],l=Math.min,i=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function S(e,t,n){let r,{reference:o,floating:l}=e,i=g(t),a=m(g(t)),s=v(a),u=p(t),c="y"===i,d=o.x+o.width/2-l.width/2,f=o.y+o.height/2-l.height/2,y=o[s]/2-l[s]/2;switch(u){case"top":r={x:d,y:o.y-l.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-l.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=y*(n&&c?-1:1);break;case"end":r[a]+=y*(n&&c?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:l=[],platform:i}=n,a=l.filter(Boolean),s=await (null==i.isRTL?void 0:i.isRTL(t)),u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=S(u,r,s),f=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:l,fn:m}=a[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[l]:{...p[l],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=S(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function k(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:l,rects:i,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=x(h),v=a[p?"floating"===d?"reference":"floating":d],g=b(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(v)))||n?v:v.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),y="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),S=await (null==l.isElement?void 0:l.isElement(w))&&await (null==l.getScale?void 0:l.getScale(w))||{x:1,y:1},C=b(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:s}):y);return{top:(g.top-C.top+m.top)/S.y,bottom:(C.bottom-g.bottom+m.bottom)/S.y,left:(g.left-C.left+m.left)/S.x,right:(C.right-g.right+m.right)/S.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return o.some(t=>e[t]>=0)}async function T(e,t){let{placement:n,platform:r,elements:o}=e,l=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=p(n),a=h(n),s="y"===g(n),u=["left","top"].includes(i)?-1:1,c=l&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),s?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function j(){return"undefined"!=typeof window}function E(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!j()&&(e instanceof Node||e instanceof P(e).Node)}function M(e){return!!j()&&(e instanceof Element||e instanceof P(e).Element)}function D(e){return!!j()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function H(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function I(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=_(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function O(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=V(),n=M(e)?_(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function V(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function F(e){return["html","body","#document"].includes(E(e))}function _(e){return P(e).getComputedStyle(e)}function W(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===E(e))return e;let t=e.assignedSlot||e.parentNode||H(e)&&e.host||L(e);return H(t)?t.host:t}function G(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=z(t);return F(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&I(n)?n:e(n)}(e),l=o===(null==(r=e.ownerDocument)?void 0:r.body),i=P(o);if(l){let e=K(i);return t.concat(i,i.visualViewport||[],I(o)?o:[],e&&n?G(e):[])}return t.concat(o,G(o,[],n))}function K(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=_(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),l=o?e.offsetWidth:n,i=o?e.offsetHeight:r,s=a(n)!==l||a(r)!==i;return s&&(n=l,r=i),{width:n,height:r,$:s}}function U(e){return M(e)?e:e.contextElement}function X(e){let t=U(e);if(!D(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:l}=q(t),i=(l?a(n.width):n.width)/r,s=(l?a(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}let Y=u(0);function Z(e){let t=P(e);return V()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function $(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),i=U(e),a=u(1);t&&(r?M(r)&&(a=X(r)):a=X(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(i))&&o)?Z(i):u(0),c=(l.left+s.x)/a.x,d=(l.top+s.y)/a.y,f=l.width/a.x,p=l.height/a.y;if(i){let e=P(i),t=r&&M(r)?P(r):r,n=e,o=K(n);for(;o&&r&&t!==n;){let e=X(o),t=o.getBoundingClientRect(),r=_(o),l=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=l,d+=i,o=K(n=P(o))}}return b({width:f,height:p,x:c,y:d})}function J(e,t){let n=W(e).scrollLeft;return t?t.left+n:$(L(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=L(e),o=n.visualViewport,l=r.clientWidth,i=r.clientHeight,a=0,s=0;if(o){l=o.width,i=o.height;let e=V();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:i,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=L(e),n=W(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),s=-n.scrollTop;return"rtl"===_(r).direction&&(a+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:a,y:s}}(L(e));else if(M(t))r=function(e,t){let n=$(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,l=D(e)?X(e):u(1),i=e.clientWidth*l.x,a=e.clientHeight*l.y;return{width:i,height:a,x:o*l.x,y:r*l.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===_(e).position}function en(e,t){if(!D(e)||"fixed"===_(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(O(e))return n;if(!D(e)){let t=z(e);for(;t&&!F(t);){if(M(t)&&!et(t))return t;t=z(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(E(r))&&et(r);)r=en(r,t);return r&&F(r)&&et(r)&&!B(r)?n:r||function(e){let t=z(e);for(;D(t)&&!F(t);){if(B(t))return t;if(O(t))break;t=z(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=L(t),l="fixed"===n,i=$(e,!0,l,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!l)if(("body"!==E(t)||I(o))&&(a=W(t)),r){let e=$(t,!0,l,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o));l&&!r&&o&&(s.x=J(o));let c=!o||r||l?u(0):Q(o,a);return{x:i.left+a.scrollLeft-s.x-c.x,y:i.top+a.scrollTop-s.y-c.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},el={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,l="fixed"===o,i=L(r),a=!!t&&O(t.floating);if(r===i||a&&l)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=D(r);if((f||!f&&!l)&&(("body"!==E(r)||I(i))&&(s=W(r)),D(r))){let e=$(r);c=X(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!i||f||l?u(0):Q(i,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?O(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=G(e,[],!1).filter(e=>M(e)&&"body"!==E(e)),o=null,l="fixed"===_(e).position,i=l?z(e):e;for(;M(i)&&!F(i);){let t=_(i),n=B(i);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||I(i)&&!n&&function e(t,n){let r=z(t);return!(r===n||!M(r)||F(r))&&("fixed"===_(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=z(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=i(r.top,e.top),e.right=l(r.right,e.right),e.bottom=l(r.bottom,e.bottom),e.left=i(r.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:X,isElement:M,isRTL:function(e){return"rtl"===_(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=x(p),w={x:n,y:r},b=m(g(o)),S=v(b),C=await s.getDimensions(d),k="y"===b,R=k?"clientHeight":"clientWidth",A=a.reference[S]+a.reference[b]-w[b]-a.floating[S],T=w[b]-a.reference[b],j=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),E=j?j[R]:0;E&&await (null==s.isElement?void 0:s.isElement(j))||(E=u.floating[R]||a.floating[S]);let P=E/2-C[S]/2-1,L=l(y[k?"top":"left"],P),N=l(y[k?"bottom":"right"],P),M=E-C[S]-N,D=E/2-C[S]/2+(A/2-T/2),H=i(L,l(D,M)),I=!c.arrow&&null!=h(o)&&D!==H&&a.reference[S]/2-(D<L?L:N)-C[S]/2<0,O=I?D<L?D-L:D-M:0;return{[b]:w[b]+O,data:{[b]:H,centerOffset:D-H-O,...I&&{alignmentOffset:O}},reset:I}}}),es=(e,t,n)=>{let r=new Map,o={platform:el,...n},l={...o.platform,_c:r};return C(e,t,{...o,platform:l})};var eu=n(47650),ec="undefined"!=typeof document?r.useLayoutEffect:function(){};function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:l,placement:i,middlewareData:a}=t,s=await T(t,e);return i===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:l+s.y,data:{...s,placement:i}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await k(t,c),v=g(p(o)),y=m(v),w=d[y],x=d[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=i(n,l(w,r))}if(s){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=i(n,l(x,r))}let b=u.fn({...t,[y]:w,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[v]:s}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:l,middlewareData:i}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:n,y:r},d=g(o),h=m(d),v=c[h],y=c[d],w=f(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===h?"height":"width",t=l.reference[h]-l.floating[e]+x.mainAxis,n=l.reference[h]+l.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,S;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=l.reference[d]-l.floating[e]+(t&&(null==(b=i.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=l.reference[d]+l.reference[e]+(t?0:(null==(S=i.offset)?void 0:S[d])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[d]:y}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,l,i;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:x}=t,{mainAxis:b=!0,crossAxis:S=!0,fallbackPlacements:C,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:T=!0,...j}=f(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let E=p(a),P=g(c),L=p(c)===c,N=await (null==d.isRTL?void 0:d.isRTL(x.floating)),M=C||(L||!T?[w(c)]:function(e){let t=w(e);return[y(e),t,y(t)]}(c)),D="none"!==A;!C&&D&&M.push(...function(e,t,n,r){let o=h(e),l=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(l=l.map(e=>e+"-"+o),t&&(l=l.concat(l.map(y)))),l}(c,T,A,N));let H=[c,...M],I=await k(t,j),O=[],B=(null==(r=s.flip)?void 0:r.overflows)||[];if(b&&O.push(I[E]),S){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),l=v(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[l]>t.floating[l]&&(i=w(i)),[i,w(i)]}(a,u,N);O.push(I[e[0]],I[e[1]])}if(B=[...B,{placement:a,overflows:O}],!O.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=H[e];if(t&&("alignment"!==S||P===g(t)||B.every(e=>e.overflows[0]>0&&g(e.placement)===P)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(l=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(R){case"bestFit":{let e=null==(i=B.filter(e=>{if(D){let t=g(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),y=await k(t,v),w=p(s),x=h(s),b="y"===g(s),{width:S,height:C}=u.floating;"top"===w||"bottom"===w?(o=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=w,o="end"===x?"top":"bottom");let R=C-y.top-y.bottom,A=S-y.left-y.right,T=l(C-y[o],R),j=l(S-y[a],A),E=!t.middlewareData.shift,P=T,L=j;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=R),E&&!x){let e=i(y.left,0),t=i(y.right,0),n=i(y.top,0),r=i(y.bottom,0);b?L=S-2*(0!==e||0!==t?e+t:i(y.left,y.right)):P=C-2*(0!==n||0!==r?n+r:i(y.top,y.bottom))}await m({...t,availableWidth:L,availableHeight:P});let N=await c.getDimensions(d.floating);return S!==N.width||C!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=R(await k(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=R(await k(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),eS=(e,t)=>({...em(e),options:[e,t]});var eC=n(63655),ek=n(95155),eR=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...l}=e;return(0,ek.jsx)(eC.sG.svg,{...l,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,ek.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eR.displayName="Arrow";var eA=n(6101),eT=n(46081),ej=n(39033),eE=n(52712),eP=n(11275),eL="Popper",[eN,eM]=(0,eT.A)(eL),[eD,eH]=eN(eL),eI=e=>{let{__scopePopper:t,children:n}=e,[o,l]=r.useState(null);return(0,ek.jsx)(eD,{scope:t,anchor:o,onAnchorChange:l,children:n})};eI.displayName=eL;var eO="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...l}=e,i=eH(eO,n),a=r.useRef(null),s=(0,eA.s)(t,a);return r.useEffect(()=>{i.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,ek.jsx)(eC.sG.div,{...l,ref:s})});eB.displayName=eO;var eV="PopperContent",[eF,e_]=eN(eV),eW=r.forwardRef((e,t)=>{var n,o,a,u,c,d,f,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:S=0,sticky:C="partial",hideWhenDetached:k=!1,updatePositionStrategy:R="optimized",onPlaced:A,...T}=e,j=eH(eV,h),[E,P]=r.useState(null),N=(0,eA.s)(t,e=>P(e)),[M,D]=r.useState(null),H=(0,eP.X)(M),I=null!=(f=null==H?void 0:H.width)?f:0,O=null!=(p=null==H?void 0:H.height)?p:0,B="number"==typeof S?S:{top:0,right:0,bottom:0,left:0,...S},V=Array.isArray(b)?b:[b],F=V.length>0,_={padding:B,boundary:V.filter(eq),altBoundary:F},{refs:W,floatingStyles:z,placement:K,isPositioned:q,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:l,elements:{reference:i,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),x=r.useCallback(e=>{e!==k.current&&(k.current=e,y(e))},[]),b=i||m,S=a||g,C=r.useRef(null),k=r.useRef(null),R=r.useRef(d),A=null!=u,T=eh(u),j=eh(l),E=eh(c),P=r.useCallback(()=>{if(!C.current||!k.current)return;let e={placement:t,strategy:n,middleware:p};j.current&&(e.platform=j.current),es(C.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==E.current};L.current&&!ed(R.current,t)&&(R.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,n,j,E]);ec(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let L=r.useRef(!1);ec(()=>(L.current=!0,()=>{L.current=!1}),[]),ec(()=>{if(b&&(C.current=b),S&&(k.current=S),b&&S){if(T.current)return T.current(b,S,P);P()}},[b,S,P,T,A]);let N=r.useMemo(()=>({reference:C,floating:k,setReference:w,setFloating:x}),[w,x]),M=r.useMemo(()=>({reference:b,floating:S}),[b,S]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ep(M.floating,d.x),r=ep(M.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,M.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:N,elements:M,floatingStyles:D}),[d,P,N,M,D])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=U(e),h=a||u?[...p?G(p):[],...G(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&d?function(e,t){let n,r=null,o=L(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(c||t(),!m||!v)return;let g=s(h),y=s(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-s(o.clientHeight-(h+v))+"px "+-s(p)+"px",threshold:i(0,l(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ei(f,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?$(e):null;return f&&function t(){let r=$(e);y&&!ei(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===R})},elements:{reference:j.anchor},middleware:[ev({mainAxis:v+O,alignmentAxis:y}),x&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?ey():void 0,..._}),x&&ew({..._}),ex({..._,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:l,height:i}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(l,"px")),a.setProperty("--radix-popper-anchor-height","".concat(i,"px"))}}),M&&eS({element:M,padding:w}),eU({arrowWidth:I,arrowHeight:O}),k&&eb({strategy:"referenceHidden",..._})]}),[Y,Z]=eX(K),J=(0,ej.c)(A);(0,eE.N)(()=>{q&&(null==J||J())},[q,J]);let Q=null==(n=X.arrow)?void 0:n.x,ee=null==(o=X.arrow)?void 0:o.y,et=(null==(a=X.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eE.N)(()=>{E&&er(window.getComputedStyle(E).zIndex)},[E]),(0,ek.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:q?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(u=X.transformOrigin)?void 0:u.x,null==(c=X.transformOrigin)?void 0:c.y].join(" "),...(null==(d=X.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,ek.jsx)(eF,{scope:h,placedSide:Y,onArrowChange:D,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,ek.jsx)(eC.sG.div,{"data-side":Y,"data-align":Z,...T,ref:N,style:{...T.style,animation:q?void 0:"none"}})})})});eW.displayName=eV;var ez="PopperArrow",eG={top:"bottom",right:"left",bottom:"top",left:"right"},eK=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e_(ez,n),l=eG[o.placedSide];return(0,ek.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,ek.jsx)(eR,{...r,ref:t,style:{...r.style,display:"block"}})})});function eq(e){return null!==e}eK.displayName=ez;var eU=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,l,i;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=eX(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(l=null==(r=u.arrow)?void 0:r.x)?l:0)+d/2,g=(null!=(i=null==(o=u.arrow)?void 0:o.y)?i:0)+f/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-f,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function eX(e){let[t,n="center"]=e.split("-");return[t,n]}var eY=eI,eZ=eB,e$=eW,eJ=eK},38715:(e,t,n)=>{n.d(t,{UC:()=>eN,In:()=>eP,q7:()=>eD,VF:()=>eI,p4:()=>eH,ZL:()=>eL,bL:()=>eT,wn:()=>eB,PP:()=>eO,l9:()=>ej,WT:()=>eE,LM:()=>eM});var r=n(12115),o=n(47650);function l(e,[t,n]){return Math.min(n,Math.max(t,e))}var i=n(85185),a=n(37328),s=n(6101),u=n(46081),c=n(94315),d=n(19178),f=n(92293),p=n(25519),h=n(61285),m=n(35152),v=n(34378),g=n(63655),y=n(99708),w=n(39033),x=n(5845),b=n(52712),S=n(45503),C=n(95155),k=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});r.forwardRef((e,t)=>(0,C.jsx)(g.sG.span,{...e,ref:t,style:{...k,...e.style}})).displayName="VisuallyHidden";var R=n(38168),A=n(93795),T=[" ","Enter","ArrowUp","ArrowDown"],j=[" ","Enter"],E="Select",[P,L,N]=(0,a.N)(E),[M,D]=(0,u.A)(E,[N,m.Bk]),H=(0,m.Bk)(),[I,O]=M(E),[B,V]=M(E),F=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:l,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:y}=e,w=H(t),[b,S]=r.useState(null),[k,R]=r.useState(null),[A,T]=r.useState(!1),j=(0,c.jH)(d),[L,N]=(0,x.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:E}),[M,D]=(0,x.i)({prop:a,defaultProp:s,onChange:u,caller:E}),O=r.useRef(null),V=!b||y||!!b.closest("form"),[F,_]=r.useState(new Set),W=Array.from(F).map(e=>e.props.value).join(";");return(0,C.jsx)(m.bL,{...w,children:(0,C.jsxs)(I,{required:g,scope:t,trigger:b,onTriggerChange:S,valueNode:k,onValueNodeChange:R,valueNodeHasChildren:A,onValueNodeHasChildrenChange:T,contentId:(0,h.B)(),value:M,onValueChange:D,open:L,onOpenChange:N,dir:j,triggerPointerDownPosRef:O,disabled:v,children:[(0,C.jsx)(P.Provider,{scope:t,children:(0,C.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{_(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),V?(0,C.jsxs)(eC,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:M,onChange:e=>D(e.target.value),disabled:v,form:y,children:[void 0===M?(0,C.jsx)("option",{value:""}):null,Array.from(F)]},W):null]})})};F.displayName=E;var _="SelectTrigger",W=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...l}=e,a=H(n),u=O(_,n),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=L(n),p=r.useRef("touch"),[h,v,y]=eR(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eA(t,e,n);void 0!==r&&u.onValueChange(r.value)}),w=e=>{c||(u.onOpenChange(!0),y()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,C.jsx)(m.Mz,{asChild:!0,...a,children:(0,C.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":ek(u.value)?"":void 0,...l,ref:d,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&w(e)}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(w(),e.preventDefault())})})})});W.displayName=_;var z="SelectValue",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:l,placeholder:i="",...a}=e,u=O(z,n),{onValueNodeHasChildrenChange:c}=u,d=void 0!==l,f=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,C.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:ek(u.value)?(0,C.jsx)(C.Fragment,{children:i}):l})});G.displayName=z;var K=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});K.displayName="SelectIcon";var q=e=>(0,C.jsx)(v.Z,{asChild:!0,...e});q.displayName="SelectPortal";var U="SelectContent",X=r.forwardRef((e,t)=>{let n=O(U,e.__scopeSelect),[l,i]=r.useState();return((0,b.N)(()=>{i(new DocumentFragment)},[]),n.open)?(0,C.jsx)(J,{...e,ref:t}):l?o.createPortal((0,C.jsx)(Y,{scope:e.__scopeSelect,children:(0,C.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,C.jsx)("div",{children:e.children})})}),l):null});X.displayName=U;var[Y,Z]=M(U),$=(0,y.TL)("SelectContent.RemoveScroll"),J=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:l,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S,...k}=e,T=O(U,n),[j,E]=r.useState(null),[P,N]=r.useState(null),M=(0,s.s)(t,e=>E(e)),[D,H]=r.useState(null),[I,B]=r.useState(null),V=L(n),[F,_]=r.useState(!1),W=r.useRef(!1);r.useEffect(()=>{if(j)return(0,R.Eq)(j)},[j]),(0,f.Oh)();let z=r.useCallback(e=>{let[t,...n]=V().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[V,P]),G=r.useCallback(()=>z([D,j]),[z,D,j]);r.useEffect(()=>{F&&G()},[F,G]);let{onOpenChange:K,triggerPointerDownPosRef:q}=T;r.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{var n,r,o,l;e={x:Math.abs(Math.round(t.pageX)-(null!=(o=null==(n=q.current)?void 0:n.x)?o:0)),y:Math.abs(Math.round(t.pageY)-(null!=(l=null==(r=q.current)?void 0:r.y)?l:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():j.contains(n.target)||K(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[j,K,q]),r.useEffect(()=>{let e=()=>K(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[K]);let[X,Z]=eR(e=>{let t=V().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eA(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),J=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==T.value&&T.value===t||r)&&(H(e),r&&(W.current=!0))},[T.value]),et=r.useCallback(()=>null==j?void 0:j.focus(),[j]),en=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==T.value&&T.value===t||r)&&B(e)},[T.value]),er="popper"===o?ee:Q,eo=er===ee?{side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,C.jsx)(Y,{scope:n,content:j,viewport:P,onViewportChange:N,itemRefCallback:J,selectedItem:D,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:G,selectedItemText:I,position:o,isPositioned:F,searchRef:X,children:(0,C.jsx)(A.A,{as:$,allowPinchZoom:!0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(l,e=>{var t;null==(t=T.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,C.jsx)(er,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...k,...eo,onPlaced:()=>_(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,i.m)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>z(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...i}=e,a=O(U,n),u=Z(U,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=L(n),v=r.useRef(!1),y=r.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:S,focusSelectedItem:k}=u,R=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&w&&x&&S){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=S.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,i=n.left-o,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),d=l(i,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{let o=t.right-r.right,i=window.innerWidth-n.right-o,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),d=l(i,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}let i=m(),s=window.innerHeight-20,u=w.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+h+u+parseInt(d.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,y),C=window.getComputedStyle(w),k=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),A=e.top+e.height/2-10,T=x.offsetHeight/2,j=p+h+(x.offsetTop+T);if(j<=A){let e=i.length>0&&x===i[i.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-A,T+(e?R:0)+(f.clientHeight-w.offsetTop-w.offsetHeight)+g);c.style.height=j+t+"px"}else{let e=i.length>0&&x===i[0].ref.current;c.style.top="0px";let t=Math.max(A,p+w.offsetTop+(e?k:0)+T);c.style.height=t+(y-j)+"px",w.scrollTop=j-A+w.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=b+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,c,f,w,x,S,a.dir,o]);(0,b.N)(()=>R(),[R]);let[A,T]=r.useState();(0,b.N)(()=>{f&&T(window.getComputedStyle(f).zIndex)},[f]);let j=r.useCallback(e=>{e&&!0===y.current&&(R(),null==k||k(),y.current=!1)},[R,k]);return(0,C.jsx)(et,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:j,children:(0,C.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:A},children:(0,C.jsx)(g.sG.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...l}=e,i=H(n);return(0,C.jsx)(m.UC,{...i,...l,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,en]=M(U,{}),er="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...l}=e,a=Z(er,n),u=en(er,n),c=(0,s.s)(t,a.onViewportChange),d=r.useRef(0);return(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,C.jsx)(P.Slot,{scope:n,children:(0,C.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...l,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...l.style},onScroll:(0,i.m)(l.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let l=o+e,i=Math.min(r,l),a=l-i;n.style.height=i+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eo.displayName=er;var el="SelectGroup",[ei,ea]=M(el);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,C.jsx)(ei,{scope:n,id:o,children:(0,C.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=el;var es="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(es,n);return(0,C.jsx)(g.sG.div,{id:o.id,...r,ref:t})}).displayName=es;var eu="SelectItem",[ec,ed]=M(eu),ef=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:l=!1,textValue:a,...u}=e,c=O(eu,n),d=Z(eu,n),f=c.value===o,[p,m]=r.useState(null!=a?a:""),[v,y]=r.useState(!1),w=(0,s.s)(t,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,o,l)}),x=(0,h.B)(),b=r.useRef("touch"),S=()=>{l||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,C.jsx)(ec,{scope:n,value:o,disabled:l,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,C.jsx)(P.ItemSlot,{scope:n,value:o,disabled:l,textValue:p,children:(0,C.jsx)(g.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":l||void 0,"data-disabled":l?"":void 0,tabIndex:l?void 0:-1,...u,ref:w,onFocus:(0,i.m)(u.onFocus,()=>y(!0)),onBlur:(0,i.m)(u.onBlur,()=>y(!1)),onClick:(0,i.m)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,i.m)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,i.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,i.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,l){var t;null==(t=d.onItemLeave)||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,i.m)(u.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(j.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ep="SelectItemText",eh=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:l,style:i,...a}=e,u=O(ep,n),c=Z(ep,n),d=ed(ep,n),f=V(ep,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,d.value,d.disabled)}),v=null==p?void 0:p.textContent,y=r.useMemo(()=>(0,C.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:w,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(w(y),()=>x(y)),[w,x,y]),(0,C.jsxs)(C.Fragment,{children:[(0,C.jsx)(g.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});eh.displayName=ep;var em="SelectItemIndicator",ev=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ed(em,n).isSelected?(0,C.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ev.displayName=em;var eg="SelectScrollUpButton",ey=r.forwardRef((e,t)=>{let n=Z(eg,e.__scopeSelect),o=en(eg,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){i(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=eg;var ew="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=Z(ew,e.__scopeSelect),o=en(ew,e.__scopeSelect),[l,i]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),l?(0,C.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ew;var eb=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...l}=e,a=Z("SelectScrollButton",n),s=r.useRef(null),u=L(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...l,ref:t,style:{flexShrink:0,...l.style},onPointerDown:(0,i.m)(l.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.m)(l.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.m)(l.onPointerLeave,()=>{c()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,C.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eS="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=H(n),l=O(eS,n),i=Z(eS,n);return l.open&&"popper"===i.position?(0,C.jsx)(m.i3,{...o,...r,ref:t}):null}).displayName=eS;var eC=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...l}=e,i=r.useRef(null),a=(0,s.s)(t,i),u=(0,S.Z)(o);return r.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[u,o]),(0,C.jsx)(g.sG.select,{...l,style:{...k,...l.style},ref:a,defaultValue:o})});function ek(e){return""===e||void 0===e}function eR(e){let t=(0,w.c)(e),n=r.useRef(""),o=r.useRef(0),l=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),i=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,l,i]}function eA(e,t,n){var r,o;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=e,o=Math.max(i,0),r.map((e,t)=>r[(o+t)%r.length]));1===l.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return s!==n?s:void 0}eC.displayName="SelectBubbleInput";var eT=F,ej=W,eE=G,eP=K,eL=q,eN=X,eM=eo,eD=ef,eH=eh,eI=ev,eO=ey,eB=ex},45503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(12115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},76981:(e,t,n)=>{n.d(t,{C1:()=>C,bL:()=>b});var r=n(12115),o=n(6101),l=n(46081),i=n(85185),a=n(5845),s=n(45503),u=n(11275),c=n(28905),d=n(63655),f=n(95155),p="Checkbox",[h,m]=(0,l.A)(p),[v,g]=h(p);function y(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:l,disabled:i,form:s,name:u,onCheckedChange:c,required:d,value:h="on",internal_do_not_use_render:m}=e,[g,y]=(0,a.i)({prop:n,defaultProp:null!=l&&l,onChange:c,caller:p}),[w,x]=r.useState(null),[b,S]=r.useState(null),C=r.useRef(!1),k=!w||!!s||!!w.closest("form"),R={checked:g,disabled:i,setChecked:y,control:w,setControl:x,name:u,form:s,value:h,hasConsumerStoppedPropagationRef:C,required:d,defaultChecked:!A(l)&&l,isFormControl:k,bubbleInput:b,setBubbleInput:S};return(0,f.jsx)(v,{scope:t,...R,children:"function"==typeof m?m(R):o})}var w="CheckboxTrigger",x=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:l,onClick:a,...s}=e,{control:u,value:c,disabled:p,checked:h,required:m,setControl:v,setChecked:y,hasConsumerStoppedPropagationRef:x,isFormControl:b,bubbleInput:S}=g(w,n),C=(0,o.s)(t,v),k=r.useRef(h);return r.useEffect(()=>{let e=null==u?void 0:u.form;if(e){let t=()=>y(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,y]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":A(h)?"mixed":h,"aria-required":m,"data-state":T(h),"data-disabled":p?"":void 0,disabled:p,value:c,...s,ref:C,onKeyDown:(0,i.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(a,e=>{y(e=>!!A(e)||!e),S&&b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});x.displayName=w;var b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:l,required:i,disabled:a,value:s,onCheckedChange:u,form:c,...d}=e;return(0,f.jsx)(y,{__scopeCheckbox:n,checked:o,defaultChecked:l,disabled:a,required:i,onCheckedChange:u,name:r,form:c,value:s,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(x,{...d,ref:t,__scopeCheckbox:n}),r&&(0,f.jsx)(R,{__scopeCheckbox:n})]})}})});b.displayName=p;var S="CheckboxIndicator",C=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,l=g(S,n);return(0,f.jsx)(c.C,{present:r||A(l.checked)||!0===l.checked,children:(0,f.jsx)(d.sG.span,{"data-state":T(l.checked),"data-disabled":l.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});C.displayName=S;var k="CheckboxBubbleInput",R=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...l}=e,{control:i,hasConsumerStoppedPropagationRef:a,checked:c,defaultChecked:p,required:h,disabled:m,name:v,value:y,form:w,bubbleInput:x,setBubbleInput:b}=g(k,n),S=(0,o.s)(t,b),C=(0,s.Z)(c),R=(0,u.X)(i);r.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(C!==c&&e){let n=new Event("click",{bubbles:t});x.indeterminate=A(c),e.call(x,!A(c)&&c),x.dispatchEvent(n)}},[x,C,c,a]);let T=r.useRef(!A(c)&&c);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:T.current,required:h,disabled:m,name:v,value:y,form:w,...l,tabIndex:-1,ref:S,style:{...l.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function T(e){return A(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=k}}]);