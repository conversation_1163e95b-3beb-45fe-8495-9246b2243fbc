"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[380],{381:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5623:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>X,bm:()=>en,hE:()=>ea,hJ:()=>et,l9:()=>$});var a=r(12115),o=r(85185),n=r(6101),l=r(46081),i=r(61285),d=r(5845),s=r(19178),c=r(25519),u=r(34378),p=r(28905),h=r(63655),f=r(92293),v=r(93795),y=r(38168),g=r(99708),m=r(95155),k="Dialog",[x,A]=(0,l.A)(k),[C,j]=x(k),w=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:n,onOpenChange:l,modal:s=!0}=e,c=a.useRef(null),u=a.useRef(null),[p,h]=(0,d.i)({prop:o,defaultProp:null!=n&&n,onChange:l,caller:k});return(0,m.jsx)(C,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),modal:s,children:r})};w.displayName=k;var b="DialogTrigger",R=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,l=j(b,r),i=(0,n.s)(t,l.triggerRef);return(0,m.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":U(l.open),...a,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=b;var D="DialogPortal",[M,P]=x(D,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:n}=e,l=j(D,t);return(0,m.jsx)(M,{scope:t,forceMount:r,children:a.Children.map(o,e=>(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};O.displayName=D;var F="DialogOverlay",_=a.forwardRef((e,t)=>{let r=P(F,e.__scopeDialog),{forceMount:a=r.forceMount,...o}=e,n=j(F,e.__scopeDialog);return n.modal?(0,m.jsx)(p.C,{present:a||n.open,children:(0,m.jsx)(I,{...o,ref:t})}):null});_.displayName=F;var E=(0,g.TL)("DialogOverlay.RemoveScroll"),I=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=j(F,r);return(0,m.jsx)(v.A,{as:E,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(h.sG.div,{"data-state":U(o.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),N="DialogContent",q=a.forwardRef((e,t)=>{let r=P(N,e.__scopeDialog),{forceMount:a=r.forceMount,...o}=e,n=j(N,e.__scopeDialog);return(0,m.jsx)(p.C,{present:a||n.open,children:n.modal?(0,m.jsx)(V,{...o,ref:t}):(0,m.jsx)(z,{...o,ref:t})})});q.displayName=N;var V=a.forwardRef((e,t)=>{let r=j(N,e.__scopeDialog),l=a.useRef(null),i=(0,n.s)(t,r.contentRef,l);return a.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,m.jsx)(T,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),z=a.forwardRef((e,t)=>{let r=j(N,e.__scopeDialog),o=a.useRef(!1),n=a.useRef(!1);return(0,m.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,n.current=!1},onInteractOutside:t=>{var a,l;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),T=a.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...d}=e,u=j(N,r),p=a.useRef(null),h=(0,n.s)(t,p);return(0,f.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(s.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":U(u.open),...d,ref:h,onDismiss:()=>u.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(Y,{titleId:u.titleId}),(0,m.jsx)(Q,{contentRef:p,descriptionId:u.descriptionId})]})]})}),L="DialogTitle",B=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=j(L,r);return(0,m.jsx)(h.sG.h2,{id:o.titleId,...a,ref:t})});B.displayName=L;var G="DialogDescription",H=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,o=j(G,r);return(0,m.jsx)(h.sG.p,{id:o.descriptionId,...a,ref:t})});H.displayName=G;var Z="DialogClose",S=a.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=j(Z,r);return(0,m.jsx)(h.sG.button,{type:"button",...a,ref:t,onClick:(0,o.m)(e.onClick,()=>n.onOpenChange(!1))})});function U(e){return e?"open":"closed"}S.displayName=Z;var W="DialogTitleWarning",[K,J]=(0,l.q)(W,{contentName:N,titleName:L,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,r=J(W),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return a.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Q=e=>{let{contentRef:t,descriptionId:r}=e,o=J("DialogDescriptionWarning"),n="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return a.useEffect(()=>{var e;let a=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&a&&(document.getElementById(r)||console.warn(n))},[n,t,r]),null},X=w,$=R,ee=O,et=_,er=q,ea=B,eo=H,en=S},18979:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},19145:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},20547:(e,t,r)=>{r.d(t,{UC:()=>S,ZL:()=>Z,bL:()=>G,l9:()=>H});var a=r(12115),o=r(85185),n=r(6101),l=r(46081),i=r(19178),d=r(92293),s=r(25519),c=r(61285),u=r(35152),p=r(34378),h=r(28905),f=r(63655),v=r(99708),y=r(5845),g=r(38168),m=r(93795),k=r(95155),x="Popover",[A,C]=(0,l.A)(x,[u.Bk]),j=(0,u.Bk)(),[w,b]=A(x),R=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:n,onOpenChange:l,modal:i=!1}=e,d=j(t),s=a.useRef(null),[p,h]=a.useState(!1),[f,v]=(0,y.i)({prop:o,defaultProp:null!=n&&n,onChange:l,caller:x});return(0,k.jsx)(u.bL,{...d,children:(0,k.jsx)(w,{scope:t,contentId:(0,c.B)(),triggerRef:s,open:f,onOpenChange:v,onOpenToggle:a.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:p,onCustomAnchorAdd:a.useCallback(()=>h(!0),[]),onCustomAnchorRemove:a.useCallback(()=>h(!1),[]),modal:i,children:r})})};R.displayName=x;var D="PopoverAnchor";a.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,n=b(D,r),l=j(r),{onCustomAnchorAdd:i,onCustomAnchorRemove:d}=n;return a.useEffect(()=>(i(),()=>d()),[i,d]),(0,k.jsx)(u.Mz,{...l,...o,ref:t})}).displayName=D;var M="PopoverTrigger",P=a.forwardRef((e,t)=>{let{__scopePopover:r,...a}=e,l=b(M,r),i=j(r),d=(0,n.s)(t,l.triggerRef),s=(0,k.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":B(l.open),...a,ref:d,onClick:(0,o.m)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?s:(0,k.jsx)(u.Mz,{asChild:!0,...i,children:s})});P.displayName=M;var O="PopoverPortal",[F,_]=A(O,{forceMount:void 0}),E=e=>{let{__scopePopover:t,forceMount:r,children:a,container:o}=e,n=b(O,t);return(0,k.jsx)(F,{scope:t,forceMount:r,children:(0,k.jsx)(h.C,{present:r||n.open,children:(0,k.jsx)(p.Z,{asChild:!0,container:o,children:a})})})};E.displayName=O;var I="PopoverContent",N=a.forwardRef((e,t)=>{let r=_(I,e.__scopePopover),{forceMount:a=r.forceMount,...o}=e,n=b(I,e.__scopePopover);return(0,k.jsx)(h.C,{present:a||n.open,children:n.modal?(0,k.jsx)(V,{...o,ref:t}):(0,k.jsx)(z,{...o,ref:t})})});N.displayName=I;var q=(0,v.TL)("PopoverContent.RemoveScroll"),V=a.forwardRef((e,t)=>{let r=b(I,e.__scopePopover),l=a.useRef(null),i=(0,n.s)(t,l),d=a.useRef(!1);return a.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,k.jsx)(m.A,{as:q,allowPinchZoom:!0,children:(0,k.jsx)(T,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),d.current||null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;d.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),z=a.forwardRef((e,t)=>{let r=b(I,e.__scopePopover),o=a.useRef(!1),n=a.useRef(!1);return(0,k.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,n.current=!1},onInteractOutside:t=>{var a,l;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),T=a.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:n,disableOutsidePointerEvents:l,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:f,...v}=e,y=b(I,r),g=j(r);return(0,d.Oh)(),(0,k.jsx)(s.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:n,children:(0,k.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:f,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:h,onDismiss:()=>y.onOpenChange(!1),children:(0,k.jsx)(u.UC,{"data-state":B(y.open),role:"dialog",id:y.contentId,...g,...v,ref:t,style:{...v.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),L="PopoverClose";function B(e){return e?"open":"closed"}a.forwardRef((e,t)=>{let{__scopePopover:r,...a}=e,n=b(L,r);return(0,k.jsx)(f.sG.button,{type:"button",...a,ref:t,onClick:(0,o.m)(e.onClick,()=>n.onOpenChange(!1))})}).displayName=L,a.forwardRef((e,t)=>{let{__scopePopover:r,...a}=e,o=j(r);return(0,k.jsx)(u.i3,{...o,...a,ref:t})}).displayName="PopoverArrow";var G=R,H=P,Z=E,S=N},21492:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},25273:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},29869:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},37108:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},39881:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},58832:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},64261:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},66932:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},69037:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},71539:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},72713:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}}]);