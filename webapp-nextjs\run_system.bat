@echo off
REM run_system.bat - Script batch per avviare webapp-nextjs su Windows
echo.
echo === Avvio del sistema CABLYS NEXT.JS WEBAPP ===
echo.

REM Verifica che Python sia installato
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Errore: Python non trovato. Installare Python prima di continuare.
    pause
    exit /b 1
)

REM Verifica che Node.js sia installato
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Errore: Node.js non trovato. Installare Node.js prima di continuare.
    pause
    exit /b 1
)

REM Verifica che npm sia installato
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Errore: npm non trovato. Installare npm prima di continuare.
    pause
    exit /b 1
)

REM Esegui lo script Python
python run_system.py

pause
