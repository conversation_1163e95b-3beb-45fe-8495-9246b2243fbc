/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        mariner: {
          50: '#eefaff',
          100: '#d8f3ff',
          200: '#bae9ff',
          300: '#8adeff',
          400: '#53caff',
          500: '#2bacff',
          600: '#1490fc',
          700: '#0d74e3',
          800: '#125fbb',
          900: '#155293',
          950: '#123259',
        },
        // Alias per compatibilità con il sistema esistente
        primary: {
          50: '#eefaff',
          100: '#d8f3ff',
          200: '#bae9ff',
          300: '#8adeff',
          400: '#53caff',
          500: '#2bacff',
          600: '#1490fc',
          700: '#0d74e3',
          800: '#125fbb',
          900: '#155293',
          950: '#123259',
        },
      },
      // Configurazioni aggiuntive per il sistema
      backgroundColor: {
        'rest': '#f5f7fa',
        'hover': 'rgba(20, 144, 252, 0.1)', // mariner-600 con opacità
      },
      borderColor: {
        'default': '#e0e0e0',
      },
      textColor: {
        'primary': '#212121',
        'secondary': '#757575',
      },
      // Animation and transitions
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
