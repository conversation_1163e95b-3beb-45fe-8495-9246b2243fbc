(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[984],{381:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>r});var i=a(95155),l=a(12115),n=a(30064),t=a(59434);let r=n.bL,c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(n.B8,{ref:s,className:(0,t.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...l})});c.displayName=n.B8.displayName;let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(n.l9,{ref:s,className:(0,t.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...l})});d.displayName=n.l9.displayName;let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,i.jsx)(n.UC,{ref:s,className:(0,t.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...l})});o.displayName=n.UC.displayName},19258:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var i=a(95155),l=a(12115),n=a(66695),t=a(17313),r=a(27883),c=a(4229),d=a(91788),o=a(53904),x=a(13717),m=a(381),h=a(71007),u=a(84616),j=a(29869),g=a(62525),p=a(54213);function b(){let[e,s]=(0,l.useState)(!1),a=e=>{console.log("Clicked: ".concat(e)),s(!0),setTimeout(()=>s(!1),2e3)};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"\uD83C\uDFA8 Demo Pulsanti Animati"}),(0,i.jsx)("p",{className:"text-lg text-slate-600",children:"Effetto shimmer su tutti i pulsanti principali + Tab con contrasto migliorato"})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Pulsanti Primari"})}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsx)(r.jn,{onClick:()=>a("Primary"),icon:(0,i.jsx)(c.A,{className:"h-4 w-4"}),children:"Salva"}),(0,i.jsx)(r.jn,{onClick:()=>a("Primary Glow"),icon:(0,i.jsx)(d.A,{className:"h-4 w-4"}),glow:!0,children:"Download con Glow"}),(0,i.jsx)(r.jn,{onClick:()=>a("Primary Loading"),loading:e,icon:(0,i.jsx)(o.A,{className:"h-4 w-4"}),children:"Con Loading"})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Pulsanti Secondari"})}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsx)(r.tA,{onClick:()=>a("Secondary"),icon:(0,i.jsx)(x.A,{className:"h-4 w-4"}),children:"Modifica"}),(0,i.jsx)(r.tA,{onClick:()=>a("Secondary Small"),icon:(0,i.jsx)(m.A,{className:"h-4 w-4"}),size:"sm",children:"Piccolo"}),(0,i.jsx)(r.tA,{onClick:()=>a("Secondary Large"),icon:(0,i.jsx)(h.A,{className:"h-4 w-4"}),size:"lg",children:"Grande"})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Pulsanti di Successo"})}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsx)(r.k7,{onClick:()=>a("Success"),icon:(0,i.jsx)(u.A,{className:"h-4 w-4"}),children:"Crea Nuovo"}),(0,i.jsx)(r.k7,{onClick:()=>a("Success Glow"),icon:(0,i.jsx)(j.A,{className:"h-4 w-4"}),glow:!0,children:"Upload con Glow"})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Pulsanti di Pericolo"})}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsx)(r.Qi,{onClick:()=>a("Danger"),icon:(0,i.jsx)(g.A,{className:"h-4 w-4"}),children:"Elimina"}),(0,i.jsx)(r.Qi,{onClick:()=>a("Danger Glow"),icon:(0,i.jsx)(p.A,{className:"h-4 w-4"}),glow:!0,children:"Reset Database"})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Pulsanti Outline"})}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsx)(r.rp,{onClick:()=>a("Outline"),icon:(0,i.jsx)(x.A,{className:"h-4 w-4"}),children:"Modifica"}),(0,i.jsx)(r.rp,{onClick:()=>a("Outline Small"),icon:(0,i.jsx)(m.A,{className:"h-4 w-4"}),size:"sm",children:"Impostazioni"})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsx)(n.ZB,{children:"Pulsanti Rapidi (Quick Buttons)"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Pulsanti sottili per azioni rapide - Solo hover bold, nessun ingrandimento"})]}),(0,i.jsxs)(n.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,i.jsx)("span",{className:"text-sm text-slate-600",children:"Azioni tabella:"}),(0,i.jsx)(r.eU,{onClick:()=>a("Quick Edit"),title:"Modifica",className:"p-2",children:(0,i.jsx)(x.A,{className:"h-4 w-4"})}),(0,i.jsx)(r.eU,{onClick:()=>a("Quick Delete"),title:"Elimina",className:"p-2",children:(0,i.jsx)(g.A,{className:"h-4 w-4 text-red-600"})}),(0,i.jsx)(r.eU,{onClick:()=>a("Quick Settings"),title:"Impostazioni",className:"p-2",children:(0,i.jsx)(m.A,{className:"h-4 w-4"})})]}),(0,i.jsx)("div",{className:"bg-slate-50 rounded-lg p-4 text-sm",children:(0,i.jsx)("p",{className:"text-slate-600",children:"I pulsanti rapidi sono progettati per essere discreti e non invasivi. Perfetti per azioni in tabelle o menu dove serve finezza."})})]})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsx)(n.ZB,{children:"Tab con Contrasto Migliorato"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Hover marcato e tab selezionato con bordo blu e font semibold"})]}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)(t.tU,{defaultValue:"tab1",className:"w-full",children:[(0,i.jsxs)(t.j7,{className:"grid w-full grid-cols-4",children:[(0,i.jsxs)(t.Xi,{value:"tab1",className:"tab-trigger",children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Impostazioni"]}),(0,i.jsxs)(t.Xi,{value:"tab2",className:"tab-trigger",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Utenti"]}),(0,i.jsxs)(t.Xi,{value:"tab3",className:"tab-trigger",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Database"]}),(0,i.jsxs)(t.Xi,{value:"tab4",className:"tab-trigger",children:[(0,i.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Aggiorna"]})]}),(0,i.jsx)(t.av,{value:"tab1",className:"mt-4 p-4 bg-slate-50 rounded-lg",children:(0,i.jsx)("p",{className:"text-slate-600",children:"Contenuto tab Impostazioni"})}),(0,i.jsx)(t.av,{value:"tab2",className:"mt-4 p-4 bg-slate-50 rounded-lg",children:(0,i.jsx)("p",{className:"text-slate-600",children:"Contenuto tab Utenti"})}),(0,i.jsx)(t.av,{value:"tab3",className:"mt-4 p-4 bg-slate-50 rounded-lg",children:(0,i.jsx)("p",{className:"text-slate-600",children:"Contenuto tab Database"})}),(0,i.jsx)(t.av,{value:"tab4",className:"mt-4 p-4 bg-slate-50 rounded-lg",children:(0,i.jsx)("p",{className:"text-slate-600",children:"Contenuto tab Aggiorna"})})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"Esempi Combinati"})}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center",children:[(0,i.jsx)(r.jn,{onClick:()=>a("Combined 1"),icon:(0,i.jsx)(c.A,{className:"h-4 w-4"}),glow:!0,size:"lg",children:"Salva Principale"}),(0,i.jsx)(r.tA,{onClick:()=>a("Combined 2"),icon:(0,i.jsx)(x.A,{className:"h-4 w-4"}),children:"Annulla"}),(0,i.jsx)(r.Qi,{onClick:()=>a("Combined 3"),icon:(0,i.jsx)(g.A,{className:"h-4 w-4"}),size:"sm",children:"Elimina"})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsx)(n.ZB,{children:"\uD83C\uDFAF Come Usare Tailwind CSS - Stile Elegante"})}),(0,i.jsxs)(n.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"bg-slate-50 rounded-lg p-4 text-sm",children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"Classi Tailwind per Effetti Sottili:"}),(0,i.jsxs)("ul",{className:"space-y-1 text-slate-600",children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("code",{className:"bg-slate-200 px-1 rounded",children:"bg-blue-500"})," - Colore di sfondo"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("code",{className:"bg-slate-200 px-1 rounded",children:"hover:bg-blue-600"})," - Colore hover"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("code",{className:"bg-slate-200 px-1 rounded",children:"transition-all duration-300"})," - Animazioni fluide"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("code",{className:"bg-slate-200 px-1 rounded",children:"hover:font-semibold"})," - Testo bold al hover"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("code",{className:"bg-slate-200 px-1 rounded",children:"shadow-lg hover:shadow-xl"})," - Ombre dinamiche"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("code",{className:"bg-slate-200 px-1 rounded",children:"hover:brightness-110"})," - Luminosit\xe0 icone"]})]})]}),(0,i.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 text-sm",children:[(0,i.jsx)("h4",{className:"font-medium mb-2 text-blue-900",children:"✨ Filosofia Design:"}),(0,i.jsxs)("ul",{className:"space-y-1 text-blue-700",children:[(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Nessun ingrandimento"})," - Mantiene layout stabile"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Hover sottili"})," - Bold, colori, ombre"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Pulsanti rapidi"})," - Discreti e non invasivi"]}),(0,i.jsxs)("li",{children:["• ",(0,i.jsx)("strong",{children:"Tab eleganti"})," - Solo cambio colore, niente effetti"]})]})]})]})]})]})})}},27883:(e,s,a)=>{"use strict";a.d(s,{Qi:()=>x,eU:()=>h,jn:()=>c,k7:()=>o,rp:()=>m,tA:()=>d});var i=a(95155),l=a(12115),n=a(59434),t=a(51154);let r=l.forwardRef((e,s)=>{let{className:a,variant:l="primary",size:r="md",loading:c=!1,glow:d=!1,icon:o,children:x,disabled:m,...h}=e,u=m||c;return(0,i.jsxs)("button",{className:(0,n.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[l],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[r],d&&"quick"!==l&&"btn-glow",u&&"opacity-50 cursor-not-allowed hover:shadow-none",a),disabled:u,ref:s,...h,children:[(0,i.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,i.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[c?(0,i.jsx)(t.A,{className:"h-4 w-4 animate-spin btn-icon"}):o?(0,i.jsx)("span",{className:"btn-icon",children:o}):null,x]})]})});r.displayName="AnimatedButton";let c=e=>(0,i.jsx)(r,{variant:"primary",...e}),d=e=>(0,i.jsx)(r,{variant:"secondary",...e}),o=e=>(0,i.jsx)(r,{variant:"success",...e}),x=e=>(0,i.jsx)(r,{variant:"danger",...e}),m=e=>(0,i.jsx)(r,{variant:"outline",...e}),h=e=>(0,i.jsx)(r,{variant:"quick",...e})},29869:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},59434:(e,s,a)=>{"use strict";a.d(s,{cn:()=>n});var i=a(52596),l=a(39688);function n(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.QP)((0,i.$)(s))}},66695:(e,s,a)=>{"use strict";a.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>r,Zp:()=>n,aR:()=>t});var i=a(95155);a(12115);var l=a(59434);function n(e){let{className:s,...a}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...a})}function t(e){let{className:s,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...a})}function r(e){let{className:s,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",s),...a})}function c(e){let{className:s,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",s),...a})}function d(e){let{className:s,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",s),...a})}},71007:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},72979:(e,s,a)=>{Promise.resolve().then(a.bind(a,19258))},91788:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[848,132,998,172,441,684,358],()=>s(72979)),_N_E=e.O()}]);