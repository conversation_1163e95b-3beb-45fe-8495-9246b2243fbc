import React, { useState } from 'react'

interface TruncatedTextProps {
  text: string
  maxLength?: number
  className?: string
}

export default function TruncatedText({
  text,
  maxLength = 20,
  className = ""
}: TruncatedTextProps) {
  const [showTooltip, setShowTooltip] = useState(false)

  if (!text) return <span className="text-gray-400">-</span>

  const shouldTruncate = text.length > maxLength
  const displayText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text

  if (!shouldTruncate) {
    return <span className={className}>{text}</span>
  }

  return (
    <div className="relative inline-block">
      <span
        className={`cursor-help ${className}`}
        style={{
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          maxWidth: '100%',
          display: 'inline-block'
        }}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        title={text} // Fallback browser tooltip
      >
        {displayText}
      </span>

      {/* Custom tooltip */}
      {showTooltip && (
        <div
          className="absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg -top-8 left-0 min-w-max max-w-xs break-words"
          style={{
            transform: 'translateX(-50%)',
            left: '50%'
          }}
        >
          {text}
          {/* Arrow */}
          <div
            className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0"
            style={{
              borderLeft: '4px solid transparent',
              borderRight: '4px solid transparent',
              borderTop: '4px solid #1f2937'
            }}
          />
        </div>
      )}
    </div>
  )
}
