(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[562],{24944:(e,t,s)=>{"use strict";s.d(t,{k:()=>n});var i=s(95155);s(12115);var r=s(55863),a=s(59434);function n(e){let{className:t,value:s,...n}=e;return(0,i.jsx)(r.bL,{"data-slot":"progress",className:(0,a.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...n,children:(0,i.jsx)(r.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})}},26126:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var i=s(95155);s(12115);var r=s(99708),a=s(74466),n=s(59434);let l=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:s,asChild:a=!1,...d}=e,o=a?r.DX:"span";return(0,i.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:s}),t),...d})}},27212:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A});var i=s(95155),r=s(12115),a=s(66695),n=s(30285),l=s(26126),d=s(62523),o=s(85127),c=s(24944),u=s(40283),x=s(25731),h=s(91788),m=s(29869),b=s(84616),v=s(37108),j=s(40646),p=s(14186),f=s(85339),g=s(47924),N=s(51154),w=s(92657),y=s(13717),_=s(62525);function A(){let[e,t]=(0,r.useState)(""),[s,A]=(0,r.useState)("all"),[k,z]=(0,r.useState)([]),[E,C]=(0,r.useState)(!0),[B,S]=(0,r.useState)(""),{user:Z,cantiere:L}=(0,u.A)();(0,r.useEffect)(()=>{$()},[]);let $=async()=>{try{C(!0),S("");let e=(null==L?void 0:L.id_cantiere)||(null==Z?void 0:Z.id_utente);if(!e)return void S("Cantiere non selezionato");let t=await x.Fw.getBobine(e);z(t)}catch(s){var e,t;console.error("Errore caricamento bobine:",s),S((null==(t=s.response)||null==(e=t.data)?void 0:e.detail)||"Errore durante il caricamento delle bobine")}finally{C(!1)}};(0,r.useEffect)(()=>{let e=setTimeout(()=>{$()},500);return()=>clearTimeout(e)},[e,s]);let I=(e,t,s)=>{let r=s>0?t/s*100:0;return 0===r?(0,i.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Esaurita"}):r<20?(0,i.jsx)(l.E,{className:"bg-orange-100 text-orange-800",children:"Quasi Esaurita"}):r<50?(0,i.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"In Uso"}):(0,i.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Disponibile"})},T=k.filter(t=>{var i,r,a,n;let l=(null==(i=t.id_bobina)?void 0:i.toLowerCase().includes(e.toLowerCase()))||(null==(r=t.numero_bobina)?void 0:r.toLowerCase().includes(e.toLowerCase()))||(null==(a=t.tipologia)?void 0:a.toLowerCase().includes(e.toLowerCase()))||(null==(n=t.utility)?void 0:n.toLowerCase().includes(e.toLowerCase())),d=!0;if("all"!==s){let e=t.metri_totali>0?t.metri_residui/t.metri_totali*100:0;switch(s){case"disponibile":d=e>=50;break;case"in_uso":d=e>0&&e<50;break;case"esaurita":d=0===e}}return l&&d}),D={totali:k.length,disponibili:k.filter(e=>e.metri_totali>0&&e.metri_residui/e.metri_totali>=.5).length,in_uso:k.filter(e=>{let t=e.metri_totali>0?e.metri_residui/e.metri_totali:0;return t>0&&t<.5}).length,esaurite:k.filter(e=>0===e.metri_residui).length};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"flex justify-end gap-2 mb-6",children:[(0,i.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,i.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,i.jsxs)(n.$,{size:"sm",children:[(0,i.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Nuova Bobina"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,i.jsx)(a.Zp,{children:(0,i.jsx)(a.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:D.totali})]}),(0,i.jsx)(v.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,i.jsx)(a.Zp,{children:(0,i.jsx)(a.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Disponibili"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-green-600",children:D.disponibili})]}),(0,i.jsx)(j.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,i.jsx)(a.Zp,{children:(0,i.jsx)(a.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"In Uso"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:D.in_uso})]}),(0,i.jsx)(p.A,{className:"h-8 w-8 text-yellow-500"})]})})}),(0,i.jsx)(a.Zp,{children:(0,i.jsx)(a.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Esaurite"}),(0,i.jsx)("p",{className:"text-2xl font-bold text-red-600",children:D.esaurite})]}),(0,i.jsx)(f.A,{className:"h-8 w-8 text-red-500"})]})})})]}),(0,i.jsxs)(a.Zp,{children:[(0,i.jsx)(a.aR,{children:(0,i.jsxs)(a.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,i.jsx)(a.Wu,{children:(0,i.jsxs)("div",{className:"flex gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)(d.p,{placeholder:"Cerca per ID bobina, numero, tipologia o utility...",value:e,onChange:e=>t(e.target.value),className:"w-full"})}),(0,i.jsx)("div",{className:"flex gap-2",children:["all","disponibile","in_uso","esaurita"].map(e=>(0,i.jsx)(n.$,{variant:s===e?"default":"outline",size:"sm",onClick:()=>A(e),children:"all"===e?"Tutte":"disponibile"===e?"Disponibili":"in_uso"===e?"In Uso":"Esaurite"},e))})]})})]}),(0,i.jsxs)(a.Zp,{children:[(0,i.jsxs)(a.aR,{children:[(0,i.jsxs)(a.ZB,{children:["Elenco Bobine (",T.length,")"]}),(0,i.jsx)(a.BT,{children:"Gestione completa delle bobine con stato utilizzo e metrature"})]}),(0,i.jsx)(a.Wu,{children:(0,i.jsx)("div",{className:"rounded-md border",children:(0,i.jsxs)(o.XI,{children:[(0,i.jsx)(o.A0,{children:(0,i.jsxs)(o.Hj,{children:[(0,i.jsx)(o.nd,{children:"ID Bobina"}),(0,i.jsx)(o.nd,{children:"Numero"}),(0,i.jsx)(o.nd,{children:"Utility"}),(0,i.jsx)(o.nd,{children:"Tipologia"}),(0,i.jsx)(o.nd,{children:"Conduttori/Sezione"}),(0,i.jsx)(o.nd,{children:"Metrature"}),(0,i.jsx)(o.nd,{children:"Utilizzo"}),(0,i.jsx)(o.nd,{children:"Stato"}),(0,i.jsx)(o.nd,{children:"Ubicazione"}),(0,i.jsx)(o.nd,{children:"Azioni"})]})}),(0,i.jsx)(o.BF,{children:E?(0,i.jsx)(o.Hj,{children:(0,i.jsx)(o.nA,{colSpan:10,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):B?(0,i.jsx)(o.Hj,{children:(0,i.jsx)(o.nA,{colSpan:10,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),B]})})}):0===T.length?(0,i.jsx)(o.Hj,{children:(0,i.jsx)(o.nA,{colSpan:10,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):T.map(e=>{let t=e.metri_totali>0?(e.metri_totali-e.metri_residui)/e.metri_totali*100:0;return(0,i.jsxs)(o.Hj,{children:[(0,i.jsx)(o.nA,{className:"font-medium",children:e.id_bobina}),(0,i.jsx)(o.nA,{children:e.numero_bobina||"-"}),(0,i.jsx)(o.nA,{children:e.utility||"-"}),(0,i.jsx)(o.nA,{children:e.tipologia||"-"}),(0,i.jsx)(o.nA,{children:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("div",{children:e.n_conduttori||"-"}),(0,i.jsx)("div",{className:"text-slate-500",children:e.sezione||"-"})]})}),(0,i.jsx)(o.nA,{children:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsxs)("div",{children:["Residui: ",(0,i.jsxs)("span",{className:"font-medium",children:[e.metri_residui,"m"]})]}),(0,i.jsxs)("div",{className:"text-slate-500",children:["Totali: ",e.metri_totali,"m"]})]})}),(0,i.jsx)(o.nA,{children:(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsxs)("div",{className:"text-sm font-medium",children:[Math.round(t),"%"]}),(0,i.jsx)(c.k,{value:t,className:"h-2"})]})}),(0,i.jsx)(o.nA,{children:I(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,i.jsx)(o.nA,{children:(0,i.jsx)(l.E,{variant:"outline",children:e.ubicazione_bobina||"Non specificata"})}),(0,i.jsx)(o.nA,{children:(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,i.jsx)(w.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,i.jsx)(y.A,{className:"h-4 w-4"})}),(0,i.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,i.jsx)(_.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})]})})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var i=s(95155);s(12115);var r=s(99708),a=s(74466),n=s(59434);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,size:a,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,i.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:a,className:t})),...o})}},30557:(e,t,s)=>{Promise.resolve().then(s.bind(s,27212))},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var i=s(52596),r=s(39688);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,i.$)(t))}},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>a});var i=s(95155);s(12115);var r=s(59434);function a(e){let{className:t,type:s,...a}=e;return(0,i.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...a})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>a,aR:()=>n});var i=s(95155);s(12115);var r=s(59434);function a(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function d(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function o(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}},85127:(e,t,s)=>{"use strict";s.d(t,{A0:()=>n,BF:()=>l,Hj:()=>d,XI:()=>a,nA:()=>c,nd:()=>o});var i=s(95155);s(12115);var r=s(59434);function a(e){let{className:t,...s}=e;return(0,i.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,i.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",t),...s})})}function n(e){let{className:t,...s}=e;return(0,i.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...s})}function l(e){let{className:t,...s}=e;return(0,i.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...s})}function d(e){let{className:t,...s}=e;return(0,i.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...s})}function o(e){let{className:t,...s}=e;return(0,i.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}function c(e){let{className:t,...s}=e;return(0,i.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...s})}}},e=>{var t=t=>e(e.s=t);e.O(0,[848,464,289,283,441,684,358],()=>t(30557)),_N_E=e.O()}]);