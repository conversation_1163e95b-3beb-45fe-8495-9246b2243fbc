'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

export default function TestPasswordResetPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testPasswordReset = async () => {
    setLoading(true)
    setResult(null)

    try {
      console.log('Testing password reset for:', email)
      
      const response = await fetch('/api/password/request-password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          user_type: 'user'
        })
      })

      const data = await response.json()
      
      setResult({
        status: response.status,
        success: response.ok,
        data: data,
        headers: Object.fromEntries(response.headers.entries())
      })

      console.log('Response:', {
        status: response.status,
        data: data
      })

    } catch (error) {
      console.error('Error:', error)
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  const testBackendDirect = async () => {
    setLoading(true)
    setResult(null)

    try {
      console.log('Testing backend direct for:', email)
      
      const response = await fetch('http://localhost:8001/api/password/request-password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          user_type: 'user'
        })
      })

      const data = await response.json()
      
      setResult({
        status: response.status,
        success: response.ok,
        data: data,
        headers: Object.fromEntries(response.headers.entries()),
        direct: true
      })

      console.log('Direct Response:', {
        status: response.status,
        data: data
      })

    } catch (error) {
      console.error('Direct Error:', error)
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        direct: true
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle>Test Password Reset System</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Email to test:</label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email to test"
            />
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={testPasswordReset} 
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Testing...' : 'Test via Next.js API'}
            </Button>
            <Button 
              onClick={testBackendDirect} 
              disabled={loading}
              variant="outline"
              className="flex-1"
            >
              {loading ? 'Testing...' : 'Test Backend Direct'}
            </Button>
          </div>

          {result && (
            <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <AlertDescription>
                <div className="space-y-2">
                  <div><strong>Status:</strong> {result.status || 'N/A'}</div>
                  <div><strong>Success:</strong> {result.success ? 'Yes' : 'No'}</div>
                  {result.direct && <div><strong>Method:</strong> Direct Backend</div>}
                  {result.error && <div><strong>Error:</strong> {result.error}</div>}
                  {result.data && (
                    <div>
                      <strong>Response Data:</strong>
                      <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
