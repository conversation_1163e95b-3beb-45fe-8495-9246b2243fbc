'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Cable, Building, FileText, Tag, Plus } from 'lucide-react'

export default function TipologieCaviManager() {
  const [activeTab, setActiveTab] = useState('categorie')

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cable className="h-5 w-5" />
          Database Tipologie Cavi
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <Cable className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Database Enciclopedico Tipologie Cavi</h4>
              <p className="text-sm text-blue-700 mt-1">
                Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, 
                standard e tipologie specifiche. Questo database serve come riferimento per 
                la classificazione e gestione dei cavi nei progetti.
              </p>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="categorie" className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Categorie
            </TabsTrigger>
            <TabsTrigger value="produttori" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Produttori
            </TabsTrigger>
            <TabsTrigger value="standard" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Standard
            </TabsTrigger>
            <TabsTrigger value="tipologie" className="flex items-center gap-2">
              <Cable className="h-4 w-4" />
              Tipologie
            </TabsTrigger>
          </TabsList>

          <TabsContent value="categorie" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Categorie Cavi</h3>
                <p className="text-sm text-slate-600">
                  Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)
                </p>
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuova Categoria
              </Button>
            </div>
            
            <div className="text-center py-12 text-slate-500">
              <Tag className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <p>Gestione categorie cavi - Da implementare</p>
              <p className="text-sm mt-2">
                Qui sarà possibile creare, modificare ed eliminare le categorie di cavi
              </p>
            </div>
          </TabsContent>

          <TabsContent value="produttori" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Produttori</h3>
                <p className="text-sm text-slate-600">
                  Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)
                </p>
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuovo Produttore
              </Button>
            </div>
            
            <div className="text-center py-12 text-slate-500">
              <Building className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <p>Gestione produttori - Da implementare</p>
              <p className="text-sm mt-2">
                Qui sarà possibile gestire l'anagrafica dei produttori di cavi
              </p>
            </div>
          </TabsContent>

          <TabsContent value="standard" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Standard e Normative</h3>
                <p className="text-sm text-slate-600">
                  Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)
                </p>
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuovo Standard
              </Button>
            </div>
            
            <div className="text-center py-12 text-slate-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <p>Gestione standard - Da implementare</p>
              <p className="text-sm mt-2">
                Qui sarà possibile gestire gli standard tecnici e le normative di riferimento
              </p>
            </div>
          </TabsContent>

          <TabsContent value="tipologie" className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Tipologie Specifiche</h3>
                <p className="text-sm text-slate-600">
                  Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche
                </p>
              </div>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nuova Tipologia
              </Button>
            </div>
            
            <div className="text-center py-12 text-slate-500">
              <Cable className="h-12 w-12 mx-auto mb-4 text-slate-400" />
              <p>Gestione tipologie - Da implementare</p>
              <p className="text-sm mt-2">
                Qui sarà possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {/* Informazioni aggiuntive */}
        <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
          <h5 className="font-medium text-slate-900 mb-2">Struttura Database Tipologie:</h5>
          <div className="text-sm text-slate-600 space-y-1">
            <p><strong>Categorie:</strong> Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)</p>
            <p><strong>Produttori:</strong> Aziende produttrici con informazioni di contatto</p>
            <p><strong>Standard:</strong> Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)</p>
            <p><strong>Tipologie:</strong> Specifiche tecniche dettagliate per ogni tipo di cavo</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
