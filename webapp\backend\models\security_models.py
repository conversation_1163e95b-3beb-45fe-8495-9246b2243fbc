"""
Modelli SQLAlchemy per le tabelle di sicurezza.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import INET, JSONB
from sqlalchemy.orm import relationship
from datetime import datetime

from backend.database import Base

class SecurityEvent(Base):
    """
    Modello per gli eventi di sicurezza (audit log).
    """
    __tablename__ = "security_events"

    id = Column(Integer, primary_key=True, index=True)
    event_type = Column(String(50), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("utenti.id_utente", ondelete="SET NULL"), nullable=True)
    cantiere_id = Column(Integer, ForeignKey("cantieri.id_cantiere", ondelete="SET NULL"), nullable=True)
    username = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)
    ip_address = Column(INET, nullable=True, index=True)
    user_agent = Column(Text, nullable=True)
    success = Column(Boolean, nullable=False, default=True)
    error_message = Column(Text, nullable=True)
    additional_data = Column(JSONB, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)

    # Relazioni
    user = relationship("User", foreign_keys=[user_id])
    cantiere = relationship("Cantiere", foreign_keys=[cantiere_id])

class RateLimiting(Base):
    """
    Modello per il tracking del rate limiting.
    """
    __tablename__ = "rate_limiting"

    id = Column(Integer, primary_key=True, index=True)
    identifier = Column(String(255), nullable=False, index=True)
    identifier_type = Column(String(50), nullable=False)  # 'ip', 'user_id', 'email'
    attempt_count = Column(Integer, nullable=False, default=1)
    window_start = Column(DateTime, nullable=False, default=datetime.utcnow, index=True)
    last_attempt = Column(DateTime, nullable=False, default=datetime.utcnow)
    blocked_until = Column(DateTime, nullable=True, index=True)

    # Constraint unico per identifier + type
    __table_args__ = (
        {'sqlite_autoincrement': True},
    )

class PasswordResetToken(Base):
    """
    Modello per i token di reset password.
    """
    __tablename__ = "password_reset_tokens"

    id = Column(Integer, primary_key=True, index=True)
    token_hash = Column(String(255), nullable=False, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("utenti.id_utente", ondelete="CASCADE"), nullable=True)
    cantiere_id = Column(Integer, ForeignKey("cantieri.id_cantiere", ondelete="CASCADE"), nullable=True)
    email = Column(String(255), nullable=False, index=True)
    token_type = Column(String(20), nullable=False, default='password_reset')
    expires_at = Column(DateTime, nullable=False, index=True)
    used_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    ip_address = Column(INET, nullable=True)
    user_agent = Column(Text, nullable=True)

    # Relazioni
    user = relationship("User", foreign_keys=[user_id])
    cantiere = relationship("Cantiere", foreign_keys=[cantiere_id])

    @property
    def is_expired(self) -> bool:
        """Controlla se il token è scaduto."""
        return datetime.utcnow() > self.expires_at

    @property
    def is_used(self) -> bool:
        """Controlla se il token è già stato utilizzato."""
        return self.used_at is not None

    @property
    def is_valid(self) -> bool:
        """Controlla se il token è valido (non scaduto e non utilizzato)."""
        return not self.is_expired and not self.is_used
