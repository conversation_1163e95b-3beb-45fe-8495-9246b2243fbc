'use client'

import { useState, useMemo, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { TableRow, TableCell } from '@/components/ui/table'
import { Cavo } from '@/types'
import FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'
import SmartCaviFilter from './SmartCaviFilter'
import {
  MoreHorizontal,
  Cable,
  Settings,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Package
} from 'lucide-react'

interface CaviTableProps {
  cavi: Cavo[]
  loading?: boolean
  selectionEnabled?: boolean
  selectedCavi?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  onStatusAction?: (cavo: Cavo, action: string) => void
  onContextMenuAction?: (cavo: Cavo, action: string) => void
}

export default function CaviTable({
  cavi = [],
  loading = false,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange,
  onStatusAction,
  onContextMenuAction
}: CaviTableProps) {
  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)
  const [filteredCavi, setFilteredCavi] = useState(cavi)
  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)

  // Aggiorna i cavi quando cambiano i cavi originali
  useEffect(() => {
    setSmartFilteredCavi(cavi)
    setFilteredCavi(cavi)
  }, [cavi])

  // Gestione filtri intelligenti
  const handleSmartFilterChange = (filtered: Cavo[]) => {
    setSmartFilteredCavi(filtered)
  }

  // Gestione filtri tabella
  const handleTableFilterChange = (filtered: Cavo[]) => {
    setFilteredCavi(filtered)
  }

  const handleSelectionToggle = () => {
    setInternalSelectionEnabled(!internalSelectionEnabled)
  }

  // Gestione selezione
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])
    }
  }

  const handleSelectCavo = (cavoId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCavi, cavoId]
        : selectedCavi.filter(id => id !== cavoId)
      onSelectionChange(newSelection)
    }
  }

  // Define columns matching original webapp structure
  const columns: ColumnDef[] = useMemo(() => {
    const baseColumns: ColumnDef[] = [
      {
        field: 'id_cavo',
        headerName: 'ID Cavo',
        dataType: 'text',
        headerStyle: { fontWeight: 'bold' },
        renderCell: (row: Cavo) => (
          <span className="font-semibold text-mariner-900">{row.id_cavo}</span>
        )
      },
      {
        field: 'sistema',
        headerName: 'Sistema',
        dataType: 'text'
      },
      {
        field: 'utility',
        headerName: 'Utility',
        dataType: 'text'
      },
      {
        field: 'tipologia',
        headerName: 'Tipologia',
        dataType: 'text'
      },
      {
        field: 'formazione',
        headerName: 'Formazione',
        dataType: 'text',
        align: 'right',
        renderCell: (row: Cavo) => row.formazione || row.sezione
      },
      {
        field: 'metri_teorici',
        headerName: 'Metri Teorici',
        dataType: 'number',
        align: 'right',
        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
      },
      {
        field: 'metri_posati',
        headerName: 'Metri Reali',
        dataType: 'number',
        align: 'right',
        renderCell: (row: Cavo) => {
          const metri = row.metri_posati || row.metratura_reale || 0
          return metri ? metri.toFixed(1) : '0'
        }
      },
      {
        field: 'ubicazione_partenza',
        headerName: 'Da',
        dataType: 'text',
        renderCell: (row: Cavo) => row.da || row.ubicazione_partenza
      },
      {
        field: 'ubicazione_arrivo',
        headerName: 'A',
        dataType: 'text',
        renderCell: (row: Cavo) => row.a || row.ubicazione_arrivo
      },
      {
        field: 'id_bobina',
        headerName: 'Bobina',
        dataType: 'text',
        renderCell: (row: Cavo) => row.id_bobina || 'N/A'
      },
      {
        field: 'stato_installazione',
        headerName: 'Stato',
        dataType: 'text',
        align: 'center',
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getStatusButton(row)
      },
      {
        field: 'collegamenti',
        headerName: 'Collegamenti',
        dataType: 'text',
        align: 'center',
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getConnectionButton(row)
      },
      {
        field: 'certificato',
        headerName: 'Certificato',
        dataType: 'text',
        align: 'center',
        disableFilter: true,
        disableSort: true,
        renderCell: (row: Cavo) => getCertificationButton(row)
      },
      {
        field: 'menu',
        headerName: '',
        width: 50,
        disableFilter: true,
        disableSort: true,
        align: 'center',
        renderCell: (row: Cavo) => (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onContextMenuAction?.(row, 'menu')}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        )
      }
    ]

    // Add selection column if enabled
    if (internalSelectionEnabled) {
      baseColumns.unshift({
        field: 'selection',
        headerName: '',
        disableFilter: true,
        disableSort: true,
        width: 50,
        align: 'center',
        renderHeader: () => (
          <Checkbox
            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}
            onCheckedChange={handleSelectAll}
          />
        ),
        renderCell: (row: Cavo) => (
          <Checkbox
            checked={selectedCavi.includes(row.id_cavo)}
            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}
            onClick={(e) => e.stopPropagation()}
          />
        )
      })
    }

    return baseColumns
  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])

  // Custom row renderer for selection and context menu
  const renderRow = (row: Cavo, index: number) => {
    const isSelected = selectedCavi.includes(row.id_cavo)

    return (
      <TableRow
        key={row.id_cavo}
        className={`${isSelected ? 'bg-mariner-50' : ''} hover:bg-mariner-50 cursor-pointer border-b border-mariner-100`}
        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}
        onContextMenu={(e) => {
          e.preventDefault()
          onContextMenuAction?.(row, 'context_menu')
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            className={`py-2 px-4 ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : ''}`}
            style={column.cellStyle}
            onClick={(e) => {
              // Prevent row click for action columns
              if (['stato_installazione', 'collegamenti', 'certificato', 'menu'].includes(column.field)) {
                e.stopPropagation()
              }
            }}
          >
            {column.renderCell ? column.renderCell(row) : row[column.field]}
          </TableCell>
        ))}
      </TableRow>
    )
  }

  // Funzioni di utilità per lo stato
  const getStatusBadge = (cavo: Cavo) => {
    // Verifica se il cavo è assegnato a una comanda
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione

    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda
    if (comandaAttiva && cavo.stato_installazione === 'In corso') {
      return (
        <Badge
          className="bg-blue-600 text-white cursor-pointer hover:bg-blue-700"
          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}
        >
          {comandaAttiva}
        </Badge>
      )
    }

    // Logica normale per gli altri stati
    const stato = cavo.stato_installazione || 'Da installare'

    switch (stato) {
      case 'Installato':
        return <Badge className="bg-green-100 text-green-800">Installato</Badge>
      case 'In corso':
        return <Badge className="bg-yellow-100 text-yellow-800">In corso</Badge>
      case 'Da installare':
        return <Badge variant="outline">Da installare</Badge>
      default:
        return <Badge variant="outline">{stato}</Badge>
    }
  }

  const getStatusButton = (cavo: Cavo) => {
    // Verifica se il cavo è installato controllando metri_posati o metratura_reale
    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0
    const isInstalled = metriInstallati > 0

    // Verifica se il cavo è assegnato a una comanda attiva
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda
    if (comandaAttiva && cavo.stato_installazione === 'In corso') {
      return (
        <Button
          size="sm"
          variant="default"
          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}
          className="text-xs bg-blue-600 hover:bg-blue-700 text-white"
        >
          {comandaAttiva}
        </Button>
      )
    }

    if (!isInstalled) {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onStatusAction?.(cavo, 'insert_meters')}
          className="text-xs hover:bg-blue-50 hover:border-blue-300"
        >
          <Package className="h-3 w-3 mr-1" />
          Inserisci Metri Posati
        </Button>
      )
    } else {
      return (
        <Button
          size="sm"
          variant="outline"
          onClick={() => onStatusAction?.(cavo, 'modify_reel')}
          className="text-xs hover:bg-green-50 hover:border-green-300"
        >
          <Settings className="h-3 w-3 mr-1" />
          Modifica Bobina
        </Button>
      )
    }
  }

  const getConnectionButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    if (!isInstalled) {
      return (
        <Button
          size="sm"
          variant="outline"
          disabled
          className="text-xs"
        >
          <AlertCircle className="h-3 w-3 mr-1" />
          Non disponibile
        </Button>
      )
    }

    let label, actionType, variant: "default" | "outline" | "secondary" | "destructive" | "ghost" | "link" = "outline"
    let icon

    switch (collegamento) {
      case 0:
        label = "⚪⚪ Collega cavo"
        actionType = "connect_cable"
        icon = <Zap className="h-3 w-3 mr-1" />
        break
      case 1:
        label = "🟢⚪ Completa collegamento"
        actionType = "connect_arrival"
        icon = <Zap className="h-3 w-3 mr-1" />
        variant = "secondary"
        break
      case 2:
        label = "⚪🟢 Completa collegamento"
        actionType = "connect_departure"
        icon = <Zap className="h-3 w-3 mr-1" />
        variant = "secondary"
        break
      case 3:
        label = "🟢🟢 Scollega cavo"
        actionType = "disconnect_cable"
        icon = <CheckCircle className="h-3 w-3 mr-1" />
        variant = "default"
        break
      default:
        label = "Gestisci collegamenti"
        actionType = "manage_connections"
        icon = <Settings className="h-3 w-3 mr-1" />
        break
    }

    return (
      <Button
        size="sm"
        variant={variant}
        onClick={() => onStatusAction?.(cavo, actionType)}
        className="text-xs"
      >
        {icon}
        {label}
      </Button>
    )
  }

  const getCertificationButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'

    if (!isInstalled) {
      return (
        <Button
          size="sm"
          variant="outline"
          disabled
          className="text-xs"
        >
          <AlertCircle className="h-3 w-3 mr-1" />
          Non disponibile
        </Button>
      )
    }

    if (isCertified) {
      return (
        <Button
          size="sm"
          variant="default"
          onClick={() => onStatusAction?.(cavo, 'generate_pdf')}
          className="text-xs bg-green-600 hover:bg-green-700"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Genera PDF
        </Button>
      )
    }

    return (
      <Button
        size="sm"
        variant="outline"
        onClick={() => onStatusAction?.(cavo, 'create_certificate')}
        className="text-xs"
      >
        <Clock className="h-3 w-3 mr-1" />
        Certifica cavo
      </Button>
    )
  }

  return (
    <div>
      {/* Smart Filter */}
      <SmartCaviFilter
        cavi={cavi}
        onFilteredDataChange={handleSmartFilterChange}
        loading={loading}
        selectionEnabled={internalSelectionEnabled}
        onSelectionToggle={handleSelectionToggle}
      />

      {/* Selection info only - no title */}
      {internalSelectionEnabled && selectedCavi.length > 0 && (
        <div className="mb-4 flex justify-end">
          <Badge variant="secondary" className="bg-mariner-100 text-mariner-800">
            {selectedCavi.length} selezionati
          </Badge>
        </div>
      )}

      {/* Filterable Table */}
      <FilterableTable
        data={smartFilteredCavi}
        columns={columns}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        onFilteredDataChange={handleTableFilterChange}
        renderRow={renderRow}
      />
    </div>
  )
}
