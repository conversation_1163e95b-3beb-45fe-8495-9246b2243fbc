'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  AnimatedButton,
  PrimaryButton,
  SecondaryButton,
  SuccessButton,
  DangerButton,
  OutlineButton,
  QuickButton
} from '@/components/ui/animated-button'
import { 
  Save, 
  Download, 
  Upload, 
  Trash2, 
  Edit, 
  Plus, 
  RefreshCw,
  Settings,
  User,
  Database
} from 'lucide-react'

export default function DemoButtonsPage() {
  const [loading, setLoading] = useState(false)

  const handleClick = (buttonName: string) => {
    console.log(`Clicked: ${buttonName}`)
    setLoading(true)
    setTimeout(() => setLoading(false), 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-slate-900 mb-4">
            🎨 Demo Pulsanti Animati
          </h1>
          <p className="text-lg text-slate-600">
            Effetto shimmer su tutti i pulsanti principali + Tab con contrasto migliorato
          </p>
        </div>

        {/* Pulsanti Primari */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti Primari</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <PrimaryButton 
                onClick={() => handleClick('Primary')}
                icon={<Save className="h-4 w-4" />}
              >
                Salva
              </PrimaryButton>
              
              <PrimaryButton 
                onClick={() => handleClick('Primary Glow')}
                icon={<Download className="h-4 w-4" />}
                glow
              >
                Download con Glow
              </PrimaryButton>
              
              <PrimaryButton 
                onClick={() => handleClick('Primary Loading')}
                loading={loading}
                icon={<RefreshCw className="h-4 w-4" />}
              >
                Con Loading
              </PrimaryButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti Secondari */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti Secondari</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <SecondaryButton 
                onClick={() => handleClick('Secondary')}
                icon={<Edit className="h-4 w-4" />}
              >
                Modifica
              </SecondaryButton>
              
              <SecondaryButton 
                onClick={() => handleClick('Secondary Small')}
                icon={<Settings className="h-4 w-4" />}
                size="sm"
              >
                Piccolo
              </SecondaryButton>
              
              <SecondaryButton 
                onClick={() => handleClick('Secondary Large')}
                icon={<User className="h-4 w-4" />}
                size="lg"
              >
                Grande
              </SecondaryButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti di Successo */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti di Successo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <SuccessButton 
                onClick={() => handleClick('Success')}
                icon={<Plus className="h-4 w-4" />}
              >
                Crea Nuovo
              </SuccessButton>
              
              <SuccessButton 
                onClick={() => handleClick('Success Glow')}
                icon={<Upload className="h-4 w-4" />}
                glow
              >
                Upload con Glow
              </SuccessButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti di Pericolo */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti di Pericolo</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DangerButton 
                onClick={() => handleClick('Danger')}
                icon={<Trash2 className="h-4 w-4" />}
              >
                Elimina
              </DangerButton>
              
              <DangerButton 
                onClick={() => handleClick('Danger Glow')}
                icon={<Database className="h-4 w-4" />}
                glow
              >
                Reset Database
              </DangerButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti Outline */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti Outline</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <OutlineButton
                onClick={() => handleClick('Outline')}
                icon={<Edit className="h-4 w-4" />}
              >
                Modifica
              </OutlineButton>

              <OutlineButton
                onClick={() => handleClick('Outline Small')}
                icon={<Settings className="h-4 w-4" />}
                size="sm"
              >
                Impostazioni
              </OutlineButton>
            </div>
          </CardContent>
        </Card>

        {/* Pulsanti Rapidi - Stile sottile e non invasivo */}
        <Card>
          <CardHeader>
            <CardTitle>Pulsanti Rapidi (Quick Buttons)</CardTitle>
            <p className="text-sm text-slate-600">
              Pulsanti sottili per azioni rapide - Solo hover bold, nessun ingrandimento
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2 items-center">
              <span className="text-sm text-slate-600">Azioni tabella:</span>
              <QuickButton
                onClick={() => handleClick('Quick Edit')}
                title="Modifica"
                className="p-2"
              >
                <Edit className="h-4 w-4" />
              </QuickButton>

              <QuickButton
                onClick={() => handleClick('Quick Delete')}
                title="Elimina"
                className="p-2"
              >
                <Trash2 className="h-4 w-4 text-red-600" />
              </QuickButton>

              <QuickButton
                onClick={() => handleClick('Quick Settings')}
                title="Impostazioni"
                className="p-2"
              >
                <Settings className="h-4 w-4" />
              </QuickButton>
            </div>

            <div className="bg-slate-50 rounded-lg p-4 text-sm">
              <p className="text-slate-600">
                I pulsanti rapidi sono progettati per essere discreti e non invasivi.
                Perfetti per azioni in tabelle o menu dove serve finezza.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Tab Demo - Contrasto migliorato */}
        <Card>
          <CardHeader>
            <CardTitle>Tab con Contrasto Migliorato</CardTitle>
            <p className="text-sm text-slate-600">
              Hover marcato e tab selezionato con bordo blu e font semibold
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <Tabs defaultValue="tab1" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="tab1" className="tab-trigger">
                  <Settings className="h-4 w-4 mr-2" />
                  Impostazioni
                </TabsTrigger>
                <TabsTrigger value="tab2" className="tab-trigger">
                  <User className="h-4 w-4 mr-2" />
                  Utenti
                </TabsTrigger>
                <TabsTrigger value="tab3" className="tab-trigger">
                  <Database className="h-4 w-4 mr-2" />
                  Database
                </TabsTrigger>
                <TabsTrigger value="tab4" className="tab-trigger">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Aggiorna
                </TabsTrigger>
              </TabsList>
              <TabsContent value="tab1" className="mt-4 p-4 bg-slate-50 rounded-lg">
                <p className="text-slate-600">Contenuto tab Impostazioni</p>
              </TabsContent>
              <TabsContent value="tab2" className="mt-4 p-4 bg-slate-50 rounded-lg">
                <p className="text-slate-600">Contenuto tab Utenti</p>
              </TabsContent>
              <TabsContent value="tab3" className="mt-4 p-4 bg-slate-50 rounded-lg">
                <p className="text-slate-600">Contenuto tab Database</p>
              </TabsContent>
              <TabsContent value="tab4" className="mt-4 p-4 bg-slate-50 rounded-lg">
                <p className="text-slate-600">Contenuto tab Aggiorna</p>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Esempi Combinati */}
        <Card>
          <CardHeader>
            <CardTitle>Esempi Combinati</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4 justify-center">
              <PrimaryButton
                onClick={() => handleClick('Combined 1')}
                icon={<Save className="h-4 w-4" />}
                glow
                size="lg"
              >
                Salva Principale
              </PrimaryButton>

              <SecondaryButton
                onClick={() => handleClick('Combined 2')}
                icon={<Edit className="h-4 w-4" />}
              >
                Annulla
              </SecondaryButton>

              <DangerButton
                onClick={() => handleClick('Combined 3')}
                icon={<Trash2 className="h-4 w-4" />}
                size="sm"
              >
                Elimina
              </DangerButton>
            </div>
          </CardContent>
        </Card>

        {/* Informazioni Tailwind */}
        <Card>
          <CardHeader>
            <CardTitle>🎯 Come Usare Tailwind CSS - Stile Elegante</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-slate-50 rounded-lg p-4 text-sm">
              <h4 className="font-medium mb-2">Classi Tailwind per Effetti Sottili:</h4>
              <ul className="space-y-1 text-slate-600">
                <li><code className="bg-slate-200 px-1 rounded">bg-blue-500</code> - Colore di sfondo</li>
                <li><code className="bg-slate-200 px-1 rounded">hover:bg-blue-600</code> - Colore hover</li>
                <li><code className="bg-slate-200 px-1 rounded">transition-all duration-300</code> - Animazioni fluide</li>
                <li><code className="bg-slate-200 px-1 rounded">hover:font-semibold</code> - Testo bold al hover</li>
                <li><code className="bg-slate-200 px-1 rounded">shadow-lg hover:shadow-xl</code> - Ombre dinamiche</li>
                <li><code className="bg-slate-200 px-1 rounded">hover:brightness-110</code> - Luminosità icone</li>
              </ul>
            </div>
            <div className="bg-blue-50 rounded-lg p-4 text-sm">
              <h4 className="font-medium mb-2 text-blue-900">✨ Filosofia Design:</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• <strong>Nessun ingrandimento</strong> - Mantiene layout stabile</li>
                <li>• <strong>Hover sottili</strong> - Bold, colori, ombre</li>
                <li>• <strong>Pulsanti rapidi</strong> - Discreti e non invasivi</li>
                <li>• <strong>Tab eleganti</strong> - Solo cambio colore, niente effetti</li>
              </ul>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}
