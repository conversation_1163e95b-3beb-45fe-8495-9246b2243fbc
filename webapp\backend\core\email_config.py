"""
Configurazione Email per il Sistema CMS
Gestisce le configurazioni SMTP e i template email.
"""

import os
from typing import Dict, Any
from dataclasses import dataclass
from pathlib import Path

@dataclass
class SMTPConfig:
    """Configurazione SMTP per diversi provider."""
    host: str
    port: int
    use_tls: bool
    use_ssl: bool
    username: str
    password: str
    from_email: str
    from_name: str

class EmailConfigManager:
    """Gestore delle configurazioni email."""
    
    def __init__(self):
        self.configs = self._load_smtp_configs()
        self.current_provider = os.getenv('EMAIL_PROVIDER', 'gmail')
    
    def _load_smtp_configs(self) -> Dict[str, SMTPConfig]:
        """Carica le configurazioni SMTP per diversi provider."""
        return {
            'gmail': SMTPConfig(
                host='smtp.gmail.com',
                port=587,
                use_tls=True,
                use_ssl=False,
                username=os.getenv('GMAIL_USERNAME', ''),
                password=os.getenv('GMAIL_APP_PASSWORD', ''),  # App Password, non password normale
                from_email=os.getenv('GMAIL_FROM_EMAIL', ''),
                from_name=os.getenv('EMAIL_FROM_NAME', 'CMS Sistema')
            ),
            'outlook': SMTPConfig(
                host='smtp-mail.outlook.com',
                port=587,
                use_tls=True,
                use_ssl=False,
                username=os.getenv('OUTLOOK_USERNAME', ''),
                password=os.getenv('OUTLOOK_PASSWORD', ''),
                from_email=os.getenv('OUTLOOK_FROM_EMAIL', ''),
                from_name=os.getenv('EMAIL_FROM_NAME', 'CMS Sistema')
            ),
            'yahoo': SMTPConfig(
                host='smtp.mail.yahoo.com',
                port=587,
                use_tls=True,
                use_ssl=False,
                username=os.getenv('YAHOO_USERNAME', ''),
                password=os.getenv('YAHOO_APP_PASSWORD', ''),
                from_email=os.getenv('YAHOO_FROM_EMAIL', ''),
                from_name=os.getenv('EMAIL_FROM_NAME', 'CMS Sistema')
            ),
            'custom': SMTPConfig(
                host=os.getenv('SMTP_HOST', 'localhost'),
                port=int(os.getenv('SMTP_PORT', '587')),
                use_tls=os.getenv('SMTP_USE_TLS', 'true').lower() == 'true',
                use_ssl=os.getenv('SMTP_USE_SSL', 'false').lower() == 'true',
                username=os.getenv('SMTP_USERNAME', ''),
                password=os.getenv('SMTP_PASSWORD', ''),
                from_email=os.getenv('SMTP_FROM_EMAIL', ''),
                from_name=os.getenv('EMAIL_FROM_NAME', 'CMS Sistema')
            )
        }
    
    def get_current_config(self) -> SMTPConfig:
        """Restituisce la configurazione SMTP corrente."""
        config = self.configs.get(self.current_provider)
        if not config:
            raise ValueError(f"Provider email non supportato: {self.current_provider}")
        
        # Validazione configurazione
        if not config.username or not config.password:
            raise ValueError(f"Credenziali email mancanti per provider: {self.current_provider}")
        
        if not config.from_email:
            raise ValueError(f"Email mittente mancante per provider: {self.current_provider}")
        
        return config
    
    def test_connection(self) -> tuple[bool, str]:
        """Testa la connessione SMTP."""
        import smtplib
        
        try:
            config = self.get_current_config()
            
            if config.use_ssl:
                server = smtplib.SMTP_SSL(config.host, config.port)
            else:
                server = smtplib.SMTP(config.host, config.port)
                if config.use_tls:
                    server.starttls()
            
            server.login(config.username, config.password)
            server.quit()
            
            return True, "Connessione SMTP riuscita"
            
        except Exception as e:
            return False, f"Errore connessione SMTP: {str(e)}"

class EmailTemplateManager:
    """Gestore dei template email."""
    
    def __init__(self):
        self.template_dir = Path(__file__).parent / "email_templates"
        self.template_dir.mkdir(exist_ok=True)
    
    def get_password_reset_template(self) -> str:
        """Template per email di reset password."""
        return """
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - CMS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background-color: #f5f7fa;
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header { 
            background: linear-gradient(135deg, #2196f3, #1976d2); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 { 
            font-size: 24px; 
            margin-bottom: 10px;
        }
        .header p { 
            opacity: 0.9; 
            font-size: 14px;
        }
        .content { 
            padding: 40px 30px; 
        }
        .content h2 { 
            color: #2196f3; 
            margin-bottom: 20px;
            font-size: 20px;
        }
        .content p { 
            margin-bottom: 15px; 
            color: #555;
        }
        .button-container { 
            text-align: center; 
            margin: 30px 0; 
        }
        .reset-button { 
            display: inline-block; 
            background: linear-gradient(135deg, #2196f3, #1976d2); 
            color: white; 
            padding: 15px 30px; 
            text-decoration: none; 
            border-radius: 25px; 
            font-weight: bold;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .reset-button:hover {
            transform: translateY(-2px);
        }
        .warning { 
            background: #fff3cd; 
            border-left: 4px solid #ffc107; 
            padding: 15px; 
            margin: 25px 0; 
            border-radius: 4px;
        }
        .warning h3 {
            color: #856404;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .warning ul {
            margin-left: 20px;
            color: #856404;
        }
        .warning li {
            margin-bottom: 5px;
        }
        .link-fallback {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }
        .footer { 
            background: #f8f9fa;
            padding: 20px; 
            text-align: center; 
            font-size: 12px; 
            color: #666; 
            border-top: 1px solid #e9ecef;
        }
        .footer p {
            margin-bottom: 5px;
        }
        .security-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .security-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .security-info p {
            color: #1976d2;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Reset Password</h1>
            <p>Sistema di Gestione Cantieri CMS</p>
        </div>
        
        <div class="content">
            <h2>Ciao {{ user_name }}!</h2>
            <p>Hai richiesto il reset della password per il tuo account <strong>{{ user_type }}</strong> nel sistema CMS.</p>
            
            <p>Per reimpostare la tua password in modo sicuro, clicca sul pulsante qui sotto:</p>
            
            <div class="button-container">
                <a href="{{ reset_link }}" class="reset-button">🔑 Reimposta Password</a>
            </div>
            
            <div class="warning">
                <h3>⚠️ Informazioni Importanti</h3>
                <ul>
                    <li>Questo link è valido per <strong>30 minuti</strong></li>
                    <li>Può essere utilizzato <strong>una sola volta</strong></li>
                    <li>Se non hai richiesto questo reset, ignora questa email</li>
                    <li>Non condividere mai questo link con nessuno</li>
                </ul>
            </div>
            
            <div class="security-info">
                <h3>🛡️ Sicurezza</h3>
                <p>Per la tua sicurezza, assicurati di essere su una connessione sicura quando reimposti la password.</p>
            </div>
            
            <p><strong>Se il pulsante non funziona</strong>, copia e incolla questo link nel tuo browser:</p>
            <div class="link-fallback">{{ reset_link }}</div>
        </div>
        
        <div class="footer">
            <p><strong>CMS - Sistema di Gestione Cantieri</strong></p>
            <p>Questa email è stata generata automaticamente dal sistema.</p>
            <p>Per assistenza tecnica, contatta l'amministratore del sistema.</p>
            <p style="margin-top: 10px; font-size: 11px; color: #999;">
                Email inviata il {{ current_date }} alle {{ current_time }}
            </p>
        </div>
    </div>
</body>
</html>
        """
    
    def get_password_changed_template(self) -> str:
        """Template per notifica cambio password."""
        return """
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Modificata - CMS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            background-color: #f5f7fa;
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header { 
            background: linear-gradient(135deg, #4caf50, #388e3c); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 { 
            font-size: 24px; 
            margin-bottom: 10px;
        }
        .content { 
            padding: 40px 30px; 
        }
        .success-message {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .success-message h3 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        .success-message p {
            color: #2e7d32;
        }
        .alert { 
            background: #fff3cd; 
            border-left: 4px solid #ffc107; 
            padding: 20px; 
            margin: 25px 0; 
            border-radius: 4px;
        }
        .alert h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        .alert p {
            color: #856404;
        }
        .footer { 
            background: #f8f9fa;
            padding: 20px; 
            text-align: center; 
            font-size: 12px; 
            color: #666; 
            border-top: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Password Modificata</h1>
            <p>Sistema di Gestione Cantieri CMS</p>
        </div>
        
        <div class="content">
            <h2>Ciao {{ user_name }}!</h2>
            
            <div class="success-message">
                <h3>🔒 Password Aggiornata con Successo</h3>
                <p>La password del tuo account <strong>{{ user_type }}</strong> è stata modificata con successo.</p>
            </div>
            
            <p><strong>Data e ora:</strong> {{ current_date }} alle {{ current_time }}</p>
            
            <div class="alert">
                <h3>🚨 Se non sei stato tu</h3>
                <p>Se non hai modificato tu la password, contatta <strong>immediatamente</strong> l'amministratore del sistema per segnalare un possibile accesso non autorizzato.</p>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>CMS - Sistema di Gestione Cantieri</strong></p>
            <p>Questa email è stata generata automaticamente dal sistema.</p>
        </div>
    </div>
</body>
</html>
        """

# Istanze globali
email_config_manager = EmailConfigManager()
email_template_manager = EmailTemplateManager()
