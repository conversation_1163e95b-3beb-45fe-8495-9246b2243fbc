'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

export default function DebugUserPage() {
  const [username, setUsername] = useState('Antonio')
  const [email, setEmail] = useState('<EMAIL>')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const checkUser = async () => {
    setLoading(true)
    setResult(null)

    try {
      console.log('Checking user:', username)
      
      const response = await fetch(`http://localhost:8001/api/users/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const users = await response.json()
      const user = users.find((u: any) => u.username.toLowerCase() === username.toLowerCase())
      
      setResult({
        success: true,
        user: user,
        allUsers: users.map((u: any) => ({ 
          id: u.id_utente, 
          username: u.username, 
          email: u.email,
          ruolo: u.ruolo 
        }))
      })

    } catch (error) {
      console.error('Error:', error)
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateUserEmail = async () => {
    if (!result?.user) {
      alert('Prima cerca l\'utente')
      return
    }

    setLoading(true)

    try {
      console.log('Updating user email:', result.user.id_utente, email)
      
      const response = await fetch(`http://localhost:8001/api/users/${result.user.id_utente}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        body: JSON.stringify({
          email: email
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || `HTTP ${response.status}`)
      }

      const updatedUser = await response.json()
      
      setResult(prev => ({
        ...prev,
        user: updatedUser,
        updated: true
      }))

      alert('Email aggiornata con successo!')

    } catch (error) {
      console.error('Error updating email:', error)
      alert(`Errore: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testPasswordReset = async () => {
    setLoading(true)

    try {
      console.log('Testing password reset for:', email)
      
      const response = await fetch('http://localhost:8001/api/password/request-password-reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          user_type: 'user'
        })
      })

      const data = await response.json()
      
      alert(`Password reset test:\nStatus: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`)

    } catch (error) {
      console.error('Error testing password reset:', error)
      alert(`Errore: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl">
        <CardHeader>
          <CardTitle>Debug User & Password Reset</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Username:</label>
              <Input
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter username"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Email:</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email"
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={checkUser} disabled={loading} className="flex-1">
              {loading ? 'Checking...' : 'Check User'}
            </Button>
            <Button onClick={updateUserEmail} disabled={loading || !result?.user} variant="outline" className="flex-1">
              {loading ? 'Updating...' : 'Update Email'}
            </Button>
            <Button onClick={testPasswordReset} disabled={loading} variant="secondary" className="flex-1">
              {loading ? 'Testing...' : 'Test Password Reset'}
            </Button>
          </div>

          {result && (
            <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
              <AlertDescription>
                <div className="space-y-4">
                  {result.error && (
                    <div><strong>Error:</strong> {result.error}</div>
                  )}
                  
                  {result.user && (
                    <div>
                      <strong>Found User:</strong>
                      <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto">
                        {JSON.stringify(result.user, null, 2)}
                      </pre>
                    </div>
                  )}

                  {result.updated && (
                    <div className="text-green-600 font-medium">✅ Email updated successfully!</div>
                  )}
                  
                  {result.allUsers && (
                    <div>
                      <strong>All Users:</strong>
                      <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(result.allUsers, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

        </CardContent>
      </Card>
    </div>
  )
}
