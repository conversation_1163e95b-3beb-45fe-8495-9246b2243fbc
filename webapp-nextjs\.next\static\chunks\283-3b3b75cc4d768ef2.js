"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{25731:(e,a,t)=>{t.d(a,{AR:()=>l,At:()=>n,CV:()=>s,FH:()=>i,Fw:()=>r,ZQ:()=>c,_I:()=>u,dG:()=>m,km:()=>p,mg:()=>d});let o=t(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{{let a=localStorage.getItem("token");a&&(e.headers.Authorization="Bearer ".concat(a))}return e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),window.location.href="/login"),Promise.reject(e)});let i={get:async(e,a)=>(await o.get(e,a)).data,post:async(e,a,t)=>(await o.post(e,a,t)).data,put:async(e,a,t)=>(await o.put(e,a,t)).data,patch:async(e,a,t)=>(await o.patch(e,a,t)).data,delete:async(e,a)=>(await o.delete(e,a)).data},c={login:async e=>{let a=new FormData;return a.append("username",e.username),a.append("password",e.password),(await o.post("/api/auth/login",a,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>i.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>i.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},n={getCavi:(e,a)=>i.get("/api/cavi/".concat(e),{params:a}),getCavo:(e,a)=>i.get("/api/cavi/".concat(e,"/").concat(a)),checkCavo:(e,a)=>i.get("/api/cavi/".concat(e,"/check/").concat(a)),createCavo:(e,a)=>i.post("/api/cavi/".concat(e),a),updateCavo:(e,a,t)=>i.put("/api/cavi/".concat(e,"/").concat(a),t),deleteCavo:(e,a,t)=>i.delete("/api/cavi/".concat(e,"/").concat(a),{data:t}),updateMetriPosati:(e,a,t,o)=>i.put("/api/cavi/".concat(e,"/").concat(a,"/metri-posati"),{metri_posati:t,id_bobina:o}),updateBobina:(e,a,t)=>i.put("/api/cavi/".concat(e,"/").concat(a,"/bobina"),{id_bobina:t}),collegaCavo:(e,a,t,o)=>i.post("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),{lato:t,responsabile:o}),scollegaCavo:(e,a,t)=>i.delete("/api/cavi/".concat(e,"/").concat(a,"/collegamento"),{data:{lato:t}}),markAsSpare:(e,a,t)=>i.put("/api/cavi/".concat(e,"/").concat(a,"/spare"),{spare:+!!t}),debugCavi:e=>i.get("/api/cavi/debug/".concat(e)),debugCaviRaw:e=>i.get("/api/cavi/debug/raw/".concat(e))},r={getBobine:(e,a)=>i.get("/api/parco-cavi/".concat(e),{params:a}),getBobina:(e,a)=>i.get("/api/parco-cavi/".concat(e,"/").concat(a)),getBobineCompatibili:(e,a)=>i.get("/api/parco-cavi/".concat(e,"/compatibili"),{params:a}),createBobina:(e,a)=>i.post("/api/parco-cavi/".concat(e),a),updateBobina:(e,a,t)=>i.put("/api/parco-cavi/".concat(e,"/").concat(a),t),deleteBobina:(e,a)=>i.delete("/api/parco-cavi/".concat(e,"/").concat(a)),checkDisponibilita:(e,a,t)=>i.get("/api/parco-cavi/".concat(e,"/").concat(a,"/disponibilita"),{params:{metri_richiesti:t}})},s={getComande:e=>i.get("/api/comande/cantiere/".concat(e)),getComanda:(e,a)=>i.get("/api/comande/cantiere/".concat(e,"/").concat(a)),createComanda:(e,a)=>i.post("/api/comande/cantiere/".concat(e),a),createComandaWithCavi:(e,a,t)=>i.post("/api/comande/cantiere/".concat(e,"/crea-con-cavi"),a,{params:{lista_id_cavi:t}}),updateComanda:(e,a,t)=>i.put("/api/comande/cantiere/".concat(e,"/").concat(a),t),deleteComanda:(e,a)=>i.delete("/api/comande/cantiere/".concat(e,"/").concat(a)),assegnaCavi:(e,a,t)=>i.post("/api/comande/cantiere/".concat(e,"/").concat(a,"/assegna-cavi"),{cavi_ids:t}),rimuoviCavi:(e,a,t)=>i.delete("/api/comande/cantiere/".concat(e,"/").concat(a,"/rimuovi-cavi"),{data:{cavi_ids:t}}),getStatistiche:e=>i.get("/api/comande/cantiere/".concat(e,"/statistiche")),cambiaStato:(e,a,t)=>i.put("/api/comande/cantiere/".concat(e,"/").concat(a,"/stato"),{nuovo_stato:t})},l={getResponsabili:e=>i.get("/api/responsabili/cantiere/".concat(e)),createResponsabile:(e,a)=>i.post("/api/responsabili/".concat(e),a),updateResponsabile:(e,a,t)=>i.put("/api/responsabili/".concat(e,"/").concat(a),t),deleteResponsabile:(e,a)=>i.delete("/api/responsabili/".concat(e,"/").concat(a))},p={getCertificazioni:e=>i.get("/api/cantieri/".concat(e,"/certificazioni")),createCertificazione:(e,a)=>i.post("/api/cantieri/".concat(e,"/certificazioni"),a),generatePDF:(e,a)=>i.get("/api/cantieri/".concat(e,"/pdf-cei-64-8/").concat(a),{responseType:"blob"})},d={importCavi:(e,a,t)=>{let o=new FormData;return o.append("file",a),o.append("revisione",t),i.post("/api/excel/".concat(e,"/import-cavi"),o,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,a)=>{let t=new FormData;return t.append("file",a),i.post("/api/excel/".concat(e,"/import-parco-bobine"),t,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>i.get("/api/excel/".concat(e,"/export-cavi"),{responseType:"blob"}),exportBobine:e=>i.get("/api/excel/".concat(e,"/export-parco-bobine"),{responseType:"blob"})},u={getCantieri:()=>i.get("/api/cantieri"),getCantiere:e=>i.get("/api/cantieri/".concat(e)),createCantiere:e=>i.post("/api/cantieri",e),updateCantiere:(e,a)=>i.put("/api/cantieri/".concat(e),a)},m={getUsers:()=>i.get("/api/users"),getUser:e=>i.get("/api/users/".concat(e)),createUser:e=>i.post("/api/users",e),updateUser:(e,a)=>i.put("/api/users/".concat(e),a),deleteUser:e=>i.delete("/api/users/".concat(e)),toggleUserStatus:e=>i.get("/api/users/toggle/".concat(e)),checkExpiredUsers:()=>i.get("/api/users/check-expired"),impersonateUser:e=>i.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>i.get("/api/users/db-raw"),resetDatabase:()=>i.post("/api/admin/reset-database")}},40283:(e,a,t)=>{t.d(a,{A:()=>r,AuthProvider:()=>s});var o=t(95155),i=t(12115),c=t(25731);let n=(0,i.createContext)(void 0);function r(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function s(e){let{children:a}=e,[t,r]=(0,i.useState)(null),[s,l]=(0,i.useState)(null),[p,d]=(0,i.useState)(!0),[u,m]=(0,i.useState)(()=>"true"===localStorage.getItem("isImpersonating")),[g,v]=(0,i.useState)(()=>{{let e=localStorage.getItem("impersonatedUser");return e?JSON.parse(e):null}});(0,i.useEffect)(()=>{_()},[]);let _=async()=>{try{console.log("Verificando autenticazione all'avvio..."),d(!0);let e=localStorage.getItem("token");if(console.log("Token trovato nel localStorage:",e?"S\xec":"No"),e)try{console.log("Tentativo di verifica token...");let e=await c.ZQ.verifyToken();console.log("Token valido, dati utente:",e);let a={id_utente:e.user_id,username:e.username,ruolo:e.role};r(a);let t=!0===e.is_impersonated;if(console.log("Stato di impersonificazione recuperato dai dati utente:",t),m(t),t&&e.impersonated_id){let a={id:e.impersonated_id,username:e.impersonated_username,role:e.impersonated_role};v(a),localStorage.setItem("impersonatedUser",JSON.stringify(a)),localStorage.setItem("isImpersonating","true")}else v(null),localStorage.removeItem("impersonatedUser"),localStorage.removeItem("isImpersonating");if("cantieri_user"===e.role&&e.cantiere_id){let a={id_cantiere:e.cantiere_id,commessa:e.cantiere_name||"Cantiere ".concat(e.cantiere_id),codice_univoco:"",id_utente:e.user_id};l(a)}}catch(e){console.error("Errore durante la verifica del token:",e),console.log("Rimozione token non valido dal localStorage"),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),r(null),l(null)}else console.log("Nessun token trovato, utente non autenticato"),r(null),l(null)}catch(e){console.error("Errore generale durante la verifica dell'autenticazione:",e),localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),r(null),l(null)}finally{console.log("Completata verifica autenticazione, loading:",!1),setTimeout(()=>{d(!1)},500)}},b=async(e,a)=>{try{console.log("Tentativo di login utente:",e),d(!0);let t=await c.ZQ.login({username:e,password:a});console.log("Risposta login ricevuta:",t);{localStorage.setItem("token",t.access_token);let e={id_utente:t.user_id,username:t.username,ruolo:t.role};return console.log("Impostazione dati utente:",e),r(e),l(null),e}}catch(e){throw console.error("Errore login:",e),e}finally{d(!1)}},S=async(e,a)=>{try{console.log("Tentativo di login cantiere:",e),d(!0);let t=await c.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:a});console.log("Risposta login cantiere ricevuta:",t);{localStorage.setItem("token",t.access_token);let a={id_cantiere:t.cantiere_id,commessa:t.cantiere_name,codice_univoco:e,id_utente:t.user_id};return console.log("Impostazione dati cantiere:",a),l(a),r(null),a}}catch(e){throw console.error("Errore login cantiere:",e),e}finally{d(!1)}},h=async e=>{try{let a=await c.dG.impersonateUser(e);{localStorage.setItem("token",a.access_token);let e={id:a.impersonated_id,username:a.impersonated_username,role:a.impersonated_role};return localStorage.setItem("impersonatedUser",JSON.stringify(e)),v(e),m(!0),localStorage.setItem("isImpersonating","true"),console.log("Impersonificazione attivata"),console.log("Utente impersonato:",e.username,"Ruolo:",e.role),{impersonatedUser:e}}}catch(e){throw console.error("Errore durante l'impersonificazione:",e),e}};return(0,o.jsx)(n.Provider,{value:{user:t,cantiere:s,isAuthenticated:!!t||!!s,isLoading:p,isImpersonating:u,impersonatedUser:g,login:b,loginCantiere:S,logout:()=>{console.log("Logout completo - uscita dal sistema"),localStorage.clear(),sessionStorage.clear(),r(null),l(null),m(!1),v(null),window.location.replace("/login")},checkAuth:_,impersonateUser:h},children:a})}}}]);