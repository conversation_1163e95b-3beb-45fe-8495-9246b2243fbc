import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { selectedIds, cantiereId } = await request.json()
    
    if (!selectedIds || !Array.isArray(selectedIds) || selectedIds.length === 0) {
      return NextResponse.json(
        { error: 'Nessun cavo selezionato per l\'esportazione' },
        { status: 400 }
      )
    }

    if (!cantiereId) {
      return NextResponse.json(
        { error: 'ID cantiere mancante' },
        { status: 400 }
      )
    }

    // TODO: Implementare la logica di esportazione
    // 1. Recuperare i dati dei cavi dal database
    // 2. Formattare i dati in CSV o Excel
    // 3. Restituire il file come download

    // Per ora, simuliamo la risposta
    const csvData = generateCSV(selectedIds)
    
    return new NextResponse(csvData, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="cavi_export_${new Date().toISOString().split('T')[0]}.csv"`
      }
    })

  } catch (error) {
    console.error('Errore durante l\'esportazione:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}

function generateCSV(selectedIds: string[]): string {
  // Header CSV
  const headers = [
    'ID Cavo',
    'Sistema',
    'Utility', 
    'Tipologia',
    'Formazione',
    'Metri Teorici',
    'Metri Reali',
    'Ubicazione Partenza',
    'Ubicazione Arrivo',
    'ID Bobina',
    'Stato Installazione',
    'Collegamenti',
    'Certificato'
  ]

  // Simulazione dati (in produzione, questi verrebbero dal database)
  const rows = selectedIds.map(id => [
    id,
    'Sistema A',
    'Utility B',
    'Tipologia C',
    '4x16',
    '100.0',
    '95.5',
    'Partenza A',
    'Arrivo B',
    '123',
    'Installato',
    'Collegato',
    'Certificato'
  ])

  // Costruisci CSV
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.join(','))
  ].join('\n')

  return csvContent
}
