(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3851:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var i=r(60687),s=r(43210),a=r(16189),n=r(44493),o=r(29523),l=r(89667),d=r(80013),c=r(96834),u=r(63213),p=r(23361),m=r(58869),g=r(17313),v=r(93613),x=r(41862),h=r(81806);let f=()=>{let[e,t]=(0,s.useState)([]),[r,i]=(0,s.useState)({totalEvents:0,loginAttempts:0,blockedRequests:0,suspiciousActivity:0,lastEventTime:null}),a=(e,r,s="medium")=>{let a={type:e,timestamp:Date.now(),details:r,severity:s};t(e=>[...e,a].slice(-100)),i(t=>({totalEvents:t.totalEvents+1,loginAttempts:t.loginAttempts+ +("login_attempt"===e),blockedRequests:t.blockedRequests+ +("high"===s||"critical"===s),suspiciousActivity:t.suspiciousActivity+ +("suspicious_activity"===e),lastEventTime:a.timestamp})),console.log(`🔒 Security Event [${s.toUpperCase()}]:`,{type:e,details:r,timestamp:new Date(a.timestamp).toISOString()}),("high"===s||"critical"===s)&&console.error("\uD83D\uDEA8 CRITICAL SECURITY EVENT:",a)};(0,s.useEffect)(()=>{let e=()=>{(window.outerHeight-window.innerHeight>160||window.outerWidth-window.innerWidth>160)&&a("suspicious_activity",{action:"devtools_detected",windowSize:{outer:[window.outerWidth,window.outerHeight],inner:[window.innerWidth,window.innerHeight]}},"low")},t=e=>{let t=e.clipboardData?.getData("text")||"";[/script/gi,/javascript:/gi,/vbscript:/gi,/onload|onerror|onclick/gi,/<iframe|<object|<embed/gi,/union.*select/gi,/drop.*table/gi].some(e=>e.test(t))&&(e.preventDefault(),a("suspicious_activity",{action:"malicious_paste_blocked",content:t.substring(0,100)},"high"))},r=Storage.prototype.setItem;Storage.prototype.setItem=function(e,t){return/<script|javascript:|vbscript:/gi.test(t)?void a("suspicious_activity",{action:"malicious_storage_attempt",key:e,value:t.substring(0,50)},"high"):r.call(this,e,t)};let i=e=>{let t=e.message.toLowerCase();["script error","permission denied","access denied","blocked by cors","network error"].some(e=>t.includes(e))&&a("suspicious_activity",{action:"suspicious_js_error",message:e.message,filename:e.filename,lineno:e.lineno},"medium")},s=e=>{5e3>performance.now()&&a("suspicious_activity",{action:"rapid_page_exit",timeOnPage:performance.now()},"low")};window.addEventListener("resize",e),window.addEventListener("paste",t),window.addEventListener("error",i),window.addEventListener("beforeunload",s);let n=setInterval(e,5e3);return()=>{window.removeEventListener("resize",e),window.removeEventListener("paste",t),window.removeEventListener("error",i),window.removeEventListener("beforeunload",s),clearInterval(n),Storage.prototype.setItem=r}},[]);let n=(t=10)=>{let r=Date.now()-60*t*1e3;return e.filter(e=>e.timestamp>r)};return{events:e,metrics:r,getRecentEvents:n,getEventsByType:t=>e.filter(e=>e.type===t),getEventsBySeverity:t=>e.filter(e=>e.severity===t),isUnderAttack:()=>n(5).filter(e=>"high"===e.severity||"critical"===e.severity).length>3,getThreatLevel:()=>{let e=n(10),t=e.filter(e=>"critical"===e.severity).length,r=e.filter(e=>"high"===e.severity).length;return t>0?"critical":r>2?"high":e.length>10?"medium":"low"},logSecurityEvent:a,logLoginAttempt:(e,t,r)=>{a("login_attempt",{username:e,success:t,userAgent:navigator.userAgent,timestamp:Date.now(),...r},t?"low":"medium")},logFormSubmission:(e,t,r)=>{a("form_submission",{formType:e,success:t,userAgent:navigator.userAgent,...r},"low")},logSuspiciousActivity:(e,t)=>{a("suspicious_activity",{activity:e,userAgent:navigator.userAgent,url:window.location.href,...t},"high")},logRateLimitHit:(e,t)=>{a("rate_limit_hit",{endpoint:e,userAgent:navigator.userAgent,...t},"medium")}}};function b(){let[e,t]=(0,s.useState)("user"),[r,b]=(0,s.useState)({username:"",password:"",codice_cantiere:"",password_cantiere:""}),[w,y]=(0,s.useState)(""),[j,_]=(0,s.useState)(!1),[A,N]=(0,s.useState)({}),{login:E,loginCantiere:C}=(0,u.A)(),V=(0,a.useRouter)(),{logLoginAttempt:z,logSuspiciousActivity:P,getThreatLevel:S}=f(),k=()=>{let t={};if("user"===e){let e=(0,h.TU)(r.username);e.isValid||(t.username=e.error),r.password?r.password.length<3&&(t.password="Password troppo corta"):t.password="Password \xe8 obbligatoria"}else r.codice_cantiere.trim()?r.codice_cantiere.length<3&&(t.codice_cantiere="Codice cantiere troppo corto"):t.codice_cantiere="Codice cantiere \xe8 obbligatorio",r.password_cantiere||(t.password_cantiere="Password cantiere \xe8 obbligatoria");return N(t),0===Object.keys(t).length},q=async t=>{t.preventDefault(),y(""),N({});let i="user"===e?r.username:r.codice_cantiere;if(!(0,h.Eb)(`login-${i}`,5,3e5)){y("Troppi tentativi di login. Riprova tra 5 minuti."),P("rate_limit_exceeded",{loginType:e,identifier:i});return}if(!k())return;let s=S();if("critical"===s){y("Sistema temporaneamente non disponibile per motivi di sicurezza."),P("login_blocked_threat_level",{threatLevel:s});return}_(!0);try{if("user"===e){let e=await E(r.username,r.password);z(r.username,!0,{ruolo:e?.ruolo}),e?.ruolo==="owner"?V.push("/admin"):e?.ruolo==="user"?V.push("/cantieri"):e?.ruolo==="cantieri_user"?V.push("/cavi"):V.push("/cantieri")}else await C(r.codice_cantiere,r.password_cantiere),z(r.codice_cantiere,!0,{type:"cantiere"}),V.push("/cavi")}catch(t){z("user"===e?r.username:r.codice_cantiere,!1,{error:t.response?.data?.detail||t.message,loginType:e}),y(t.response?.data?.detail||"Credenziali non valide")}finally{_(!1)}},T=(e,t)=>{b(r=>({...r,[e]:t})),y("")};return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4",children:(0,i.jsxs)("div",{className:"w-full max-w-md space-y-6",children:[(0,i.jsxs)("div",{className:"text-center space-y-2",children:[(0,i.jsx)("div",{className:"flex justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center",children:(0,i.jsx)(p.A,{className:"w-8 h-8 text-white"})})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"CABLYS"}),(0,i.jsx)("p",{className:"text-slate-600",children:"Cable Installation Advance System"})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsxs)(o.$,{variant:"user"===e?"default":"outline",className:"flex-1",onClick:()=>t("user"),children:[(0,i.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Utente"]}),(0,i.jsxs)(o.$,{variant:"cantiere"===e?"default":"outline",className:"flex-1",onClick:()=>t("cantiere"),children:[(0,i.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Cantiere"]})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsxs)(n.aR,{children:[(0,i.jsx)(n.ZB,{className:"flex items-center gap-2",children:"user"===e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(m.A,{className:"w-5 h-5 text-blue-600"}),"Login Utente"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(g.A,{className:"w-5 h-5 text-green-600"}),"Login Cantiere"]})}),(0,i.jsx)(n.BT,{children:"user"===e?"Accedi con le tue credenziali utente":"Accedi con il codice cantiere"})]}),(0,i.jsx)(n.Wu,{children:(0,i.jsxs)("form",{onSubmit:q,className:"space-y-4",children:["user"===e?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"username",children:"Username"}),(0,i.jsx)(l.p,{id:"username",type:"text",placeholder:"Inserisci username",value:r.username,onChange:e=>T("username",e.target.value),required:!0,disabled:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,i.jsx)(l.p,{id:"password",type:"password",placeholder:"Inserisci password",value:r.password,onChange:e=>T("password",e.target.value),required:!0,disabled:j})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"codice_cantiere",children:"Codice Cantiere"}),(0,i.jsx)(l.p,{id:"codice_cantiere",type:"text",placeholder:"Inserisci codice cantiere",value:r.codice_cantiere,onChange:e=>T("codice_cantiere",e.target.value),required:!0,disabled:j})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(d.J,{htmlFor:"password_cantiere",children:"Password Cantiere"}),(0,i.jsx)(l.p,{id:"password_cantiere",type:"password",placeholder:"Inserisci password cantiere",value:r.password_cantiere,onChange:e=>T("password_cantiere",e.target.value),required:!0,disabled:j})]})]}),w&&(0,i.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[(0,i.jsx)(v.A,{className:"w-4 h-4 text-red-500"}),(0,i.jsx)("span",{className:"text-sm text-red-700",children:w})]}),(0,i.jsx)(o.$,{type:"submit",className:"w-full",disabled:j,children:j?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(x.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Accesso in corso..."]}):"Accedi"}),"user"===e&&(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)(o.$,{type:"button",variant:"link",className:"text-sm text-blue-600 hover:text-blue-800",onClick:()=>V.push("/forgot-password"),children:"Password dimenticata?"})})]})})]}),(0,i.jsxs)("div",{className:"text-center space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,i.jsx)(c.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:"Next.js 15"}),(0,i.jsx)(c.E,{variant:"secondary",className:"bg-green-100 text-green-800",children:"PWA Ready"})]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"Sistema di gestione cavi di nuova generazione"})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>o});var i=r(43210),s=r(51215),a=r(8730),n=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),s=i.forwardRef((e,i)=>{let{asChild:s,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?r:t,{...a,ref:i})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function l(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25037:(e,t,r)=>{Promise.resolve().then(r.bind(r,94934))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36780:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var i=r(65239),s=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94934)),"C:\\CMS\\webapp-nextjs\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>a,aR:()=>n});var i=r(60687);r(43210);var s=r(4780);function a({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72301:(e,t,r)=>{Promise.resolve().then(r.bind(r,3851))},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var i=r(43210),s=r(14163),a=r(60687),n=i.forwardRef((e,t)=>(0,a.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var i=r(60687);r(43210);var s=r(78148),a=r(4780);function n({className:e,...t}){return(0,i.jsx)(s.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},81806:(e,t,r)=>{"use strict";r.d(t,{Eb:()=>g,GN:()=>v,TU:()=>o});let i=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,s=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,a=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,n=e=>"string"!=typeof e?"":e.trim().replace(i,"").replace(/\s+/g," ").substring(0,1e3),o=e=>{let t=n(e);return t.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:t.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(t)?/^[._-]|[._-]$/.test(t)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},l=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let t=0;return(/[a-z]/.test(e)&&t++,/[A-Z]/.test(e)&&t++,/[0-9]/.test(e)&&t++,/[^a-zA-Z0-9]/.test(e)&&t++,e.length>=12&&t++,t<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:t}:["password","123456","admin","qwerty","letmein"].some(t=>e.toLowerCase().includes(t))?{isValid:!1,error:"Password troppo comune",strength:t}:{isValid:!0,strength:t}},d=e=>{let t=n(e);return t?t.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(t)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=(e,t=255)=>n(e).length>t?{isValid:!1,error:`Testo troppo lungo (max ${t} caratteri)`}:a.test(e)||s.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0},u=e=>{let t=n(e);return t?t.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:t.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(t)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},p=e=>{if(!e)return{isValid:!0};let t=n(e).replace(/\s/g,"");return t.length<8||t.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(t)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},m=new Map,g=(e,t,r)=>{let i=Date.now(),s=m.get(e);return!s||i>s.resetTime?(m.set(e,{count:1,resetTime:i+r}),!0):!(s.count>=t)&&(s.count++,!0)},v=e=>{let t={},r=o(e.username);if(r.isValid||(t.username=r.error),e.password){let r=l(e.password);r.isValid||(t.password=r.error)}let i=u(e.ragione_sociale);if(i.isValid||(t.ragione_sociale=i.error),e.email){let r=d(e.email);r.isValid||(t.email=r.error)}if(e.vat){let r=p(e.vat);r.isValid||(t.vat=r.error)}if(e.indirizzo){let r=c(e.indirizzo,200);r.isValid||(t.indirizzo=r.error)}if(e.nazione){let r=c(e.nazione,50);r.isValid||(t.nazione=r.error)}if(e.referente_aziendale){let r=c(e.referente_aziendale,100);r.isValid||(t.referente_aziendale=r.error)}return{isValid:0===Object.keys(t).length,errors:t}}},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var i=r(60687);r(43210);var s=r(4780);function a({className:e,type:t,...r}){return(0,i.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")},94934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\login\\page.tsx","default")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var i=r(60687);r(43210);var s=r(8730),a=r(24224),n=r(4780);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...a}){let l=r?s.DX:"span";return(0,i.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...a})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,538,658,615],()=>r(36780));module.exports=i})();