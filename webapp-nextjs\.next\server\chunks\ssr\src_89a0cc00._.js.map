{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/common/FilterableTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo } from 'react'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { \n  Filter, \n  ChevronDown, \n  ChevronUp,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown,\n  X\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport interface ColumnDef {\n  field: string\n  headerName: string\n  dataType?: 'text' | 'number' | 'date'\n  align?: 'left' | 'center' | 'right'\n  width?: number\n  disableFilter?: boolean\n  disableSort?: boolean\n  headerStyle?: React.CSSProperties\n  cellStyle?: React.CSSProperties\n  renderHeader?: () => React.ReactNode\n  renderCell?: (row: any) => React.ReactNode\n}\n\ninterface FilterableTableProps {\n  data: any[]\n  columns: ColumnDef[]\n  loading?: boolean\n  emptyMessage?: string\n  onFilteredDataChange?: (filteredData: any[]) => void\n  renderRow?: (row: any, index: number) => React.ReactNode\n  className?: string\n}\n\ninterface SortConfig {\n  key: string | null\n  direction: 'asc' | 'desc' | null\n}\n\ninterface FilterConfig {\n  [key: string]: {\n    type: 'text' | 'select' | 'number'\n    value: string | string[]\n    operator?: 'contains' | 'equals' | 'gt' | 'lt' | 'gte' | 'lte'\n  }\n}\n\nexport default function FilterableTable({\n  data = [],\n  columns = [],\n  loading = false,\n  emptyMessage = 'Nessun dato disponibile',\n  onFilteredDataChange,\n  renderRow,\n  className\n}: FilterableTableProps) {\n  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: null })\n  const [filters, setFilters] = useState<FilterConfig>({})\n  const [openFilters, setOpenFilters] = useState<{ [key: string]: boolean }>({})\n\n  // Get unique values for select filters\n  const getUniqueValues = (field: string) => {\n    return [...new Set(data.map(item => item[field]).filter(Boolean))].sort()\n  }\n\n  // Apply filters and sorting\n  const filteredAndSortedData = useMemo(() => {\n    let filtered = [...data]\n\n    // Apply filters\n    Object.entries(filters).forEach(([field, filterConfig]) => {\n      if (!filterConfig.value || \n          (Array.isArray(filterConfig.value) && filterConfig.value.length === 0) ||\n          (typeof filterConfig.value === 'string' && filterConfig.value.trim() === '')) {\n        return\n      }\n\n      filtered = filtered.filter(item => {\n        const itemValue = item[field]\n        \n        if (filterConfig.type === 'select') {\n          const selectedValues = Array.isArray(filterConfig.value) ? filterConfig.value : [filterConfig.value]\n          return selectedValues.includes(itemValue)\n        }\n        \n        if (filterConfig.type === 'text') {\n          const searchValue = (filterConfig.value as string).toLowerCase()\n          const cellValue = String(itemValue || '').toLowerCase()\n          \n          if (filterConfig.operator === 'equals') {\n            return cellValue === searchValue\n          }\n          return cellValue.includes(searchValue)\n        }\n        \n        if (filterConfig.type === 'number') {\n          const numValue = parseFloat(itemValue)\n          const filterValue = parseFloat(filterConfig.value as string)\n          \n          if (isNaN(numValue) || isNaN(filterValue)) return false\n          \n          switch (filterConfig.operator) {\n            case 'equals': return numValue === filterValue\n            case 'gt': return numValue > filterValue\n            case 'lt': return numValue < filterValue\n            case 'gte': return numValue >= filterValue\n            case 'lte': return numValue <= filterValue\n            default: return numValue === filterValue\n          }\n        }\n        \n        return true\n      })\n    })\n\n    // Apply sorting\n    if (sortConfig.key && sortConfig.direction) {\n      filtered.sort((a, b) => {\n        const aValue = a[sortConfig.key!]\n        const bValue = b[sortConfig.key!]\n        \n        // Handle null/undefined values\n        if (aValue == null && bValue == null) return 0\n        if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1\n        if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1\n        \n        // Determine if values are numbers\n        const aNum = parseFloat(aValue)\n        const bNum = parseFloat(bValue)\n        const isNumeric = !isNaN(aNum) && !isNaN(bNum)\n        \n        let comparison = 0\n        if (isNumeric) {\n          comparison = aNum - bNum\n        } else {\n          comparison = String(aValue).localeCompare(String(bValue))\n        }\n        \n        return sortConfig.direction === 'asc' ? comparison : -comparison\n      })\n    }\n\n    return filtered\n  }, [data, filters, sortConfig])\n\n  // Notify parent of filtered data changes\n  useEffect(() => {\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filteredAndSortedData)\n    }\n  }, [filteredAndSortedData, onFilteredDataChange])\n\n  const handleSort = (field: string) => {\n    const column = columns.find(col => col.field === field)\n    if (column?.disableSort) return\n\n    setSortConfig(prev => {\n      if (prev.key === field) {\n        if (prev.direction === 'asc') return { key: field, direction: 'desc' }\n        if (prev.direction === 'desc') return { key: null, direction: null }\n      }\n      return { key: field, direction: 'asc' }\n    })\n  }\n\n  const updateFilter = (field: string, filterConfig: Partial<FilterConfig[string]>) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: { ...prev[field], ...filterConfig }\n    }))\n  }\n\n  const clearFilter = (field: string) => {\n    setFilters(prev => {\n      const newFilters = { ...prev }\n      delete newFilters[field]\n      return newFilters\n    })\n  }\n\n  const clearAllFilters = () => {\n    setFilters({})\n  }\n\n  const getSortIcon = (field: string) => {\n    if (sortConfig.key !== field) return <ArrowUpDown className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'asc') return <ArrowUp className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'desc') return <ArrowDown className=\"h-3 w-3\" />\n    return <ArrowUpDown className=\"h-3 w-3\" />\n  }\n\n  const hasActiveFilters = Object.keys(filters).length > 0\n\n  if (loading) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">Caricamento...</div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className={className}>\n      {/* Active filters display */}\n      {hasActiveFilters && (\n        <div className=\"mb-4 flex flex-wrap gap-2 items-center\">\n          <span className=\"text-sm text-muted-foreground\">Filtri attivi:</span>\n          {Object.entries(filters).map(([field, filterConfig]) => {\n            const column = columns.find(col => col.field === field)\n            if (!column) return null\n            \n            const displayValue = Array.isArray(filterConfig.value) \n              ? filterConfig.value.join(', ')\n              : String(filterConfig.value)\n            \n            return (\n              <Badge key={field} variant=\"secondary\" className=\"gap-1\">\n                {column.headerName}: {displayValue}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 hover:bg-transparent\"\n                  onClick={() => clearFilter(field)}\n                >\n                  <X className=\"h-3 w-3\" />\n                </Button>\n              </Badge>\n            )\n          })}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={clearAllFilters}\n            className=\"h-6 px-2 text-xs\"\n          >\n            Pulisci tutti\n          </Button>\n        </div>\n      )}\n\n      {/* Table */}\n      <Card>\n        <CardContent className=\"p-0\">\n          <Table>\n            <TableHeader>\n              <TableRow className=\"bg-mariner-50 hover:bg-mariner-50\">\n                {columns.map((column) => (\n                  <TableHead\n                    key={column.field}\n                    className={cn(\n                      \"font-semibold text-mariner-900 border-b border-mariner-200\",\n                      column.align === 'center' && \"text-center\",\n                      column.align === 'right' && \"text-right\"\n                    )}\n                    style={{ width: column.width, ...column.headerStyle }}\n                  >\n                    {column.renderHeader ? (\n                      column.renderHeader()\n                    ) : (\n                      <div className=\"flex items-center gap-2\">\n                        <span>{column.headerName}</span>\n                        \n                        {/* Sort button */}\n                        {!column.disableSort && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-auto p-0 hover:bg-mariner-100\"\n                            onClick={() => handleSort(column.field)}\n                          >\n                            {getSortIcon(column.field)}\n                          </Button>\n                        )}\n                        \n                        {/* Filter button */}\n                        {!column.disableFilter && (\n                          <Popover \n                            open={openFilters[column.field]} \n                            onOpenChange={(open) => setOpenFilters(prev => ({ ...prev, [column.field]: open }))}\n                          >\n                            <PopoverTrigger asChild>\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                className={cn(\n                                  \"h-auto p-0 hover:bg-mariner-100\",\n                                  filters[column.field] && \"text-mariner-600\"\n                                )}\n                              >\n                                <Filter className=\"h-3 w-3\" />\n                              </Button>\n                            </PopoverTrigger>\n                            <PopoverContent className=\"w-64\" align=\"start\">\n                              <FilterContent\n                                column={column}\n                                data={data}\n                                currentFilter={filters[column.field]}\n                                onFilterChange={(filterConfig) => updateFilter(column.field, filterConfig)}\n                                onClearFilter={() => clearFilter(column.field)}\n                                getUniqueValues={() => getUniqueValues(column.field)}\n                              />\n                            </PopoverContent>\n                          </Popover>\n                        )}\n                      </div>\n                    )}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredAndSortedData.length > 0 ? (\n                filteredAndSortedData.map((row, index) => (\n                  renderRow ? (\n                    renderRow(row, index)\n                  ) : (\n                    <TableRow \n                      key={index}\n                      className=\"hover:bg-mariner-50 border-b border-mariner-100\"\n                    >\n                      {columns.map((column) => (\n                        <TableCell\n                          key={column.field}\n                          className={cn(\n                            \"py-2 px-4\",\n                            column.align === 'center' && \"text-center\",\n                            column.align === 'right' && \"text-right\"\n                          )}\n                          style={column.cellStyle}\n                        >\n                          {column.renderCell ? column.renderCell(row) : row[column.field]}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  )\n                ))\n              ) : (\n                <TableRow>\n                  <TableCell colSpan={columns.length} className=\"text-center py-8 text-muted-foreground\">\n                    {emptyMessage}\n                  </TableCell>\n                </TableRow>\n              )}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\n// Filter content component\ninterface FilterContentProps {\n  column: ColumnDef\n  data: any[]\n  currentFilter?: FilterConfig[string]\n  onFilterChange: (filterConfig: Partial<FilterConfig[string]>) => void\n  onClearFilter: () => void\n  getUniqueValues: () => any[]\n}\n\nfunction FilterContent({\n  column,\n  currentFilter,\n  onFilterChange,\n  onClearFilter,\n  getUniqueValues\n}: FilterContentProps) {\n  const [localValue, setLocalValue] = useState(currentFilter?.value || '')\n  const [operator, setOperator] = useState(currentFilter?.operator || 'contains')\n\n  const uniqueValues = getUniqueValues()\n  const isSelectType = column.dataType !== 'number' && uniqueValues.length <= 20\n  const isNumberType = column.dataType === 'number'\n\n  const applyFilter = () => {\n    if (isSelectType) {\n      onFilterChange({\n        type: 'select',\n        value: Array.isArray(localValue) ? localValue : [localValue]\n      })\n    } else if (isNumberType) {\n      onFilterChange({\n        type: 'number',\n        value: localValue as string,\n        operator\n      })\n    } else {\n      onFilterChange({\n        type: 'text',\n        value: localValue as string,\n        operator\n      })\n    }\n  }\n\n  return (\n    <div className=\"space-y-3\">\n      <div className=\"font-medium text-sm\">Filtra {column.headerName}</div>\n      \n      {isSelectType ? (\n        <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n          {uniqueValues.map(value => (\n            <div key={value} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`filter-${value}`}\n                checked={Array.isArray(localValue) ? localValue.includes(value) : localValue === value}\n                onCheckedChange={(checked) => {\n                  if (Array.isArray(localValue)) {\n                    setLocalValue(checked \n                      ? [...localValue, value]\n                      : localValue.filter(v => v !== value)\n                    )\n                  } else {\n                    setLocalValue(checked ? [value] : [])\n                  }\n                }}\n              />\n              <label htmlFor={`filter-${value}`} className=\"text-sm\">\n                {value}\n              </label>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n                <SelectItem value=\"gt\">Maggiore di</SelectItem>\n                <SelectItem value=\"lt\">Minore di</SelectItem>\n                <SelectItem value=\"gte\">Maggiore o uguale</SelectItem>\n                <SelectItem value=\"lte\">Minore o uguale</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          {!isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          <Input\n            placeholder={`Cerca ${column.headerName.toLowerCase()}...`}\n            value={localValue as string}\n            onChange={(e) => setLocalValue(e.target.value)}\n            onKeyDown={(e) => e.key === 'Enter' && applyFilter()}\n          />\n        </div>\n      )}\n      \n      <div className=\"flex gap-2\">\n        <Button size=\"sm\" onClick={applyFilter}>\n          Applica\n        </Button>\n        <Button size=\"sm\" variant=\"outline\" onClick={onClearFilter}>\n          Pulisci\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AArCA;;;;;;;;;;;;;AA4Ee,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,eAAe,yBAAyB,EACxC,oBAAoB,EACpB,SAAS,EACT,SAAS,EACY;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,KAAK;QAAM,WAAW;IAAK;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAE5E,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,OAAO;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;SAAU,CAAC,IAAI;IACzE;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,WAAW;eAAI;SAAK;QAExB,gBAAgB;QAChB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,OAAO,aAAa;YACpD,IAAI,CAAC,aAAa,KAAK,IAClB,MAAM,OAAO,CAAC,aAAa,KAAK,KAAK,aAAa,KAAK,CAAC,MAAM,KAAK,KACnE,OAAO,aAAa,KAAK,KAAK,YAAY,aAAa,KAAK,CAAC,IAAI,OAAO,IAAK;gBAChF;YACF;YAEA,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,YAAY,IAAI,CAAC,MAAM;gBAE7B,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,KAAK,IAAI,aAAa,KAAK,GAAG;wBAAC,aAAa,KAAK;qBAAC;oBACpG,OAAO,eAAe,QAAQ,CAAC;gBACjC;gBAEA,IAAI,aAAa,IAAI,KAAK,QAAQ;oBAChC,MAAM,cAAc,AAAC,aAAa,KAAK,CAAY,WAAW;oBAC9D,MAAM,YAAY,OAAO,aAAa,IAAI,WAAW;oBAErD,IAAI,aAAa,QAAQ,KAAK,UAAU;wBACtC,OAAO,cAAc;oBACvB;oBACA,OAAO,UAAU,QAAQ,CAAC;gBAC5B;gBAEA,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,WAAW,WAAW;oBAC5B,MAAM,cAAc,WAAW,aAAa,KAAK;oBAEjD,IAAI,MAAM,aAAa,MAAM,cAAc,OAAO;oBAElD,OAAQ,aAAa,QAAQ;wBAC3B,KAAK;4BAAU,OAAO,aAAa;wBACnC,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAO,OAAO,YAAY;wBAC/B,KAAK;4BAAO,OAAO,YAAY;wBAC/B;4BAAS,OAAO,aAAa;oBAC/B;gBACF;gBAEA,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,IAAI,WAAW,GAAG,IAAI,WAAW,SAAS,EAAE;YAC1C,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBACjC,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBAEjC,+BAA+B;gBAC/B,IAAI,UAAU,QAAQ,UAAU,MAAM,OAAO;gBAC7C,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,CAAC,IAAI;gBACjE,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,IAAI,CAAC;gBAEjE,kCAAkC;gBAClC,MAAM,OAAO,WAAW;gBACxB,MAAM,OAAO,WAAW;gBACxB,MAAM,YAAY,CAAC,MAAM,SAAS,CAAC,MAAM;gBAEzC,IAAI,aAAa;gBACjB,IAAI,WAAW;oBACb,aAAa,OAAO;gBACtB,OAAO;oBACL,aAAa,OAAO,QAAQ,aAAa,CAAC,OAAO;gBACnD;gBAEA,OAAO,WAAW,SAAS,KAAK,QAAQ,aAAa,CAAC;YACxD;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAM;QAAS;KAAW;IAE9B,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,sBAAsB;YACxB,qBAAqB;QACvB;IACF,GAAG;QAAC;QAAuB;KAAqB;IAEhD,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACjD,IAAI,QAAQ,aAAa;QAEzB,cAAc,CAAA;YACZ,IAAI,KAAK,GAAG,KAAK,OAAO;gBACtB,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO;oBAAE,KAAK;oBAAO,WAAW;gBAAO;gBACrE,IAAI,KAAK,SAAS,KAAK,QAAQ,OAAO;oBAAE,KAAK;oBAAM,WAAW;gBAAK;YACrE;YACA,OAAO;gBAAE,KAAK;gBAAO,WAAW;YAAM;QACxC;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;oBAAE,GAAG,IAAI,CAAC,MAAM;oBAAE,GAAG,YAAY;gBAAC;YAC7C,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA;YACT,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,OAAO,UAAU,CAAC,MAAM;YACxB,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,GAAG,KAAK,OAAO,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC5D,IAAI,WAAW,SAAS,KAAK,OAAO,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC9D,IAAI,WAAW,SAAS,KAAK,QAAQ,qBAAO,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACjE,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;IAEvD,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BAAc;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAW;;YAEb,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;oBAC/C,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,OAAO,aAAa;wBACjD,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;wBACjD,IAAI,CAAC,QAAQ,OAAO;wBAEpB,MAAM,eAAe,MAAM,OAAO,CAAC,aAAa,KAAK,IACjD,aAAa,KAAK,CAAC,IAAI,CAAC,QACxB,OAAO,aAAa,KAAK;wBAE7B,qBACE,8OAAC,iIAAA,CAAA,QAAK;4BAAa,SAAQ;4BAAY,WAAU;;gCAC9C,OAAO,UAAU;gCAAC;gCAAG;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,YAAY;8CAE3B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;kCACA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;oCAAC,WAAU;8CACjB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4CAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;4CAE9B,OAAO;gDAAE,OAAO,OAAO,KAAK;gDAAE,GAAG,OAAO,WAAW;4CAAC;sDAEnD,OAAO,YAAY,GAClB,OAAO,YAAY,mBAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAM,OAAO,UAAU;;;;;;oDAGvB,CAAC,OAAO,WAAW,kBAClB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,WAAW,OAAO,KAAK;kEAErC,YAAY,OAAO,KAAK;;;;;;oDAK5B,CAAC,OAAO,aAAa,kBACpB,8OAAC,mIAAA,CAAA,UAAO;wDACN,MAAM,WAAW,CAAC,OAAO,KAAK,CAAC;wDAC/B,cAAc,CAAC,OAAS,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,CAAC,OAAO,KAAK,CAAC,EAAE;gEAAK,CAAC;;0EAEjF,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mCACA,OAAO,CAAC,OAAO,KAAK,CAAC,IAAI;8EAG3B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAGtB,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,WAAU;gEAAO,OAAM;0EACrC,cAAA,8OAAC;oEACC,QAAQ;oEACR,MAAM;oEACN,eAAe,OAAO,CAAC,OAAO,KAAK,CAAC;oEACpC,gBAAgB,CAAC,eAAiB,aAAa,OAAO,KAAK,EAAE;oEAC7D,eAAe,IAAM,YAAY,OAAO,KAAK;oEAC7C,iBAAiB,IAAM,gBAAgB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;2CAnD1D,OAAO,KAAK;;;;;;;;;;;;;;;0CA8DzB,8OAAC,iIAAA,CAAA,YAAS;0CACP,sBAAsB,MAAM,GAAG,IAC9B,sBAAsB,GAAG,CAAC,CAAC,KAAK,QAC9B,YACE,UAAU,KAAK,uBAEf,8OAAC,iIAAA,CAAA,WAAQ;wCAEP,WAAU;kDAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;gDAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;gDAE9B,OAAO,OAAO,SAAS;0DAEtB,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,CAAC;+CAR1D,OAAO,KAAK;;;;;uCALhB;;;;8DAoBX,8OAAC,iIAAA,CAAA,WAAQ;8CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wCAAC,SAAS,QAAQ,MAAM;wCAAE,WAAU;kDAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;AAYA,SAAS,cAAc,EACrB,MAAM,EACN,aAAa,EACb,cAAc,EACd,aAAa,EACb,eAAe,EACI;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,SAAS;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY;IAEpE,MAAM,eAAe;IACrB,MAAM,eAAe,OAAO,QAAQ,KAAK,YAAY,aAAa,MAAM,IAAI;IAC5E,MAAM,eAAe,OAAO,QAAQ,KAAK;IAEzC,MAAM,cAAc;QAClB,IAAI,cAAc;YAChB,eAAe;gBACb,MAAM;gBACN,OAAO,MAAM,OAAO,CAAC,cAAc,aAAa;oBAAC;iBAAW;YAC9D;QACF,OAAO,IAAI,cAAc;YACvB,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF,OAAO;YACL,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBAAsB;oBAAQ,OAAO,UAAU;;;;;;;YAE7D,6BACC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC,oIAAA,CAAA,WAAQ;gCACP,IAAI,CAAC,OAAO,EAAE,OAAO;gCACrB,SAAS,MAAM,OAAO,CAAC,cAAc,WAAW,QAAQ,CAAC,SAAS,eAAe;gCACjF,iBAAiB,CAAC;oCAChB,IAAI,MAAM,OAAO,CAAC,aAAa;wCAC7B,cAAc,UACV;+CAAI;4CAAY;yCAAM,GACtB,WAAW,MAAM,CAAC,CAAA,IAAK,MAAM;oCAEnC,OAAO;wCACL,cAAc,UAAU;4CAAC;yCAAM,GAAG,EAAE;oCACtC;gCACF;;;;;;0CAEF,8OAAC;gCAAM,SAAS,CAAC,OAAO,EAAE,OAAO;gCAAE,WAAU;0CAC1C;;;;;;;uBAhBK;;;;;;;;;qCAsBd,8OAAC;gBAAI,WAAU;;oBACZ,8BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;;;;;;;;;;;;;oBAK7B,CAAC,8BACA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAKjC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,aAAa,CAAC,MAAM,EAAE,OAAO,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;wBAC1D,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAS;kCAAa;;;;;;kCAGxC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;kCAAe;;;;;;;;;;;;;;;;;;AAMpE", "debugId": null}}, {"offset": {"line": 1392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/SmartCaviFilter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { \n  Search, \n  X, \n  CheckSquare,\n  Square\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\ninterface SmartCaviFilterProps {\n  cavi: Cavo[]\n  onFilteredDataChange?: (filteredCavi: Cavo[]) => void\n  loading?: boolean\n  selectionEnabled?: boolean\n  onSelectionToggle?: () => void\n}\n\nexport default function SmartCaviFilter({\n  cavi = [],\n  onFilteredDataChange,\n  loading = false,\n  selectionEnabled = false,\n  onSelectionToggle\n}: SmartCaviFilterProps) {\n  const [searchText, setSearchText] = useState('')\n  const [searchType, setSearchType] = useState<'contains' | 'equals'>('contains')\n\n  // Normalize string for search\n  const normalizeString = (str: string | null | undefined): string => {\n    if (!str) return ''\n    return str.toString().toLowerCase().trim()\n  }\n\n  // Extract cable info for advanced search\n  const getCavoInfo = (idCavo: string) => {\n    const match = idCavo.match(/^([A-Z]+)(\\d+)([A-Z]*)$/)\n    if (match) {\n      return {\n        prefix: match[1],\n        number: match[2],\n        suffix: match[3] || ''\n      }\n    }\n    return { prefix: '', number: idCavo, suffix: '' }\n  }\n\n  // Check if a cable matches a search term\n  const cavoMatchesTerm = useCallback((cavo: Cavo, term: string, exactMatch: boolean): boolean => {\n    const normalizedTerm = normalizeString(term)\n    \n    if (!normalizedTerm) return true\n\n    // Basic cable info\n    const cavoId = normalizeString(cavo.id_cavo)\n    const { prefix: cavoPrefix, number: cavoNumber, suffix: cavoSuffix } = getCavoInfo(cavo.id_cavo || '')\n    \n    // Cable properties\n    const tipologia = normalizeString(cavo.tipologia)\n    const formazione = normalizeString(cavo.formazione || cavo.sezione)\n    const utility = normalizeString(cavo.utility)\n    const sistema = normalizeString(cavo.sistema)\n    \n    // Locations\n    const ubicazionePartenza = normalizeString(cavo.da || cavo.ubicazione_partenza)\n    const ubicazioneArrivo = normalizeString(cavo.a || cavo.ubicazione_arrivo)\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza)\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo)\n    \n    // Reel info\n    const bobina = normalizeString(cavo.id_bobina)\n    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :\n                         cavo.id_bobina === null ? '' :\n                         normalizeString(cavo.id_bobina)\n\n    // All text fields to search\n    const textFields = [\n      cavoId, cavoPrefix, cavoNumber, cavoSuffix, tipologia, formazione, utility, sistema,\n      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,\n      bobina, bobinaDisplay\n    ]\n\n    // Numeric fields for range search\n    const numericFields = [\n      { value: cavo.metri_teorici, name: 'metri_teorici' },\n      { value: cavo.metratura_reale || cavo.metri_posati, name: 'metratura_reale' },\n      { value: parseFloat(formazione), name: 'formazione' }\n    ]\n\n    // Check for range queries (e.g., \">100\", \"<=50\")\n    const rangeMatch = normalizedTerm.match(/^([><=]+)(\\d+(?:\\.\\d+)?)$/)\n    if (rangeMatch) {\n      const operator = rangeMatch[1]\n      const value = parseFloat(rangeMatch[2])\n      \n      return numericFields.some(field => {\n        if (field.value == null || isNaN(field.value)) return false\n        \n        switch (operator) {\n          case '>': return field.value > value\n          case '>=': return field.value >= value\n          case '<': return field.value < value\n          case '<=': return field.value <= value\n          case '=': return field.value === value\n          default: return false\n        }\n      })\n    }\n\n    // Check for exact numeric match\n    const numericTerm = parseFloat(normalizedTerm)\n    if (!isNaN(numericTerm)) {\n      const numericMatch = numericFields.some(field => \n        field.value != null && !isNaN(field.value) && field.value === numericTerm\n      )\n      if (numericMatch) return true\n    }\n\n    // Text search\n    if (exactMatch) {\n      return textFields.some(field => field === normalizedTerm)\n    } else {\n      return textFields.some(field => field.includes(normalizedTerm))\n    }\n  }, [])\n\n  // Apply filter\n  const applyFilter = useCallback(() => {\n    if (!searchText.trim()) {\n      onFilteredDataChange?.(cavi)\n      return\n    }\n\n    // Split search terms by comma\n    const searchTerms = searchText.split(',')\n      .map(term => term.trim())\n      .filter(term => term.length > 0)\n\n    let filtered: Cavo[] = []\n\n    if (searchType === 'equals') {\n      if (searchTerms.length === 1) {\n        // Single term: exact search\n        filtered = cavi.filter(cavo => cavoMatchesTerm(cavo, searchTerms[0], true))\n      } else {\n        // Multiple terms: all must match (AND)\n        filtered = cavi.filter(cavo =>\n          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))\n        )\n      }\n    } else {\n      // Contains search: at least one term must match (OR)\n      filtered = cavi.filter(cavo =>\n        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))\n      )\n    }\n\n    onFilteredDataChange?.(filtered)\n  }, [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm])\n\n  // Apply filter when dependencies change\n  useEffect(() => {\n    applyFilter()\n  }, [applyFilter])\n\n  const handleSearchTextChange = (value: string) => {\n    setSearchText(value)\n  }\n\n  const clearFilter = () => {\n    setSearchText('')\n    setSearchType('contains')\n  }\n\n  return (\n    <Card className=\"mb-3\">\n      <CardContent className=\"p-3\">\n        <div className=\"flex items-center gap-3\">\n          {/* Search input - takes most space */}\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Ricerca intelligente cavi...\"\n              value={searchText}\n              onChange={(e) => handleSearchTextChange(e.target.value)}\n              disabled={loading}\n              className=\"pl-10 pr-10\"\n            />\n            {searchText && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\"\n                onClick={clearFilter}\n              >\n                <X className=\"h-3 w-3\" />\n              </Button>\n            )}\n          </div>\n\n          {/* Search type selector */}\n          <div className=\"w-40\">\n            <Select value={searchType} onValueChange={(value: 'contains' | 'equals') => setSearchType(value)}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Clear button */}\n          {searchText && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={clearFilter}\n              disabled={loading}\n            >\n              Pulisci\n            </Button>\n          )}\n\n          {/* Selection toggle button */}\n          {onSelectionToggle && (\n            <Button\n              variant={selectionEnabled ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={onSelectionToggle}\n              className=\"flex items-center gap-2\"\n            >\n              {selectionEnabled ? <CheckSquare className=\"h-4 w-4\" /> : <Square className=\"h-4 w-4\" />}\n              {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}\n            </Button>\n          )}\n        </div>\n\n        {/* Search help text - more compact */}\n        {searchText && (\n          <div className=\"mt-1 text-xs text-muted-foreground\">\n            <div className=\"flex flex-wrap gap-3\">\n              <span>💡 Suggerimenti:</span>\n              <span>• Virgole per termini multipli</span>\n              <span>• Cerca ID, tipologia, formazione</span>\n              <span>• Usa &gt;100, &lt;=50 per numeri</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAOA;AACA;AAAA;AAAA;AAAA;AAdA;;;;;;;;AA8Be,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,oBAAoB,EACpB,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,iBAAiB,EACI;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAEpE,8BAA8B;IAC9B,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,OAAO;QACjB,OAAO,IAAI,QAAQ,GAAG,WAAW,GAAG,IAAI;IAC1C;IAEA,yCAAyC;IACzC,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,OAAO,KAAK,CAAC;QAC3B,IAAI,OAAO;YACT,OAAO;gBACL,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE,IAAI;YACtB;QACF;QACA,OAAO;YAAE,QAAQ;YAAI,QAAQ;YAAQ,QAAQ;QAAG;IAClD;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAY,MAAc;QAC7D,MAAM,iBAAiB,gBAAgB;QAEvC,IAAI,CAAC,gBAAgB,OAAO;QAE5B,mBAAmB;QACnB,MAAM,SAAS,gBAAgB,KAAK,OAAO;QAC3C,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,KAAK,OAAO,IAAI;QAEnG,mBAAmB;QACnB,MAAM,YAAY,gBAAgB,KAAK,SAAS;QAChD,MAAM,aAAa,gBAAgB,KAAK,UAAU,IAAI,KAAK,OAAO;QAClE,MAAM,UAAU,gBAAgB,KAAK,OAAO;QAC5C,MAAM,UAAU,gBAAgB,KAAK,OAAO;QAE5C,YAAY;QACZ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI,KAAK,mBAAmB;QAC9E,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,IAAI,KAAK,iBAAiB;QACzE,MAAM,iBAAiB,gBAAgB,KAAK,eAAe;QAC3D,MAAM,eAAe,gBAAgB,KAAK,aAAa;QAEvD,YAAY;QACZ,MAAM,SAAS,gBAAgB,KAAK,SAAS;QAC7C,MAAM,gBAAgB,KAAK,SAAS,KAAK,iBAAiB,iBACrC,KAAK,SAAS,KAAK,OAAO,KAC1B,gBAAgB,KAAK,SAAS;QAEnD,4BAA4B;QAC5B,MAAM,aAAa;YACjB;YAAQ;YAAY;YAAY;YAAY;YAAW;YAAY;YAAS;YAC5E;YAAoB;YAAkB;YAAgB;YACtD;YAAQ;SACT;QAED,kCAAkC;QAClC,MAAM,gBAAgB;YACpB;gBAAE,OAAO,KAAK,aAAa;gBAAE,MAAM;YAAgB;YACnD;gBAAE,OAAO,KAAK,eAAe,IAAI,KAAK,YAAY;gBAAE,MAAM;YAAkB;YAC5E;gBAAE,OAAO,WAAW;gBAAa,MAAM;YAAa;SACrD;QAED,iDAAiD;QACjD,MAAM,aAAa,eAAe,KAAK,CAAC;QACxC,IAAI,YAAY;YACd,MAAM,WAAW,UAAU,CAAC,EAAE;YAC9B,MAAM,QAAQ,WAAW,UAAU,CAAC,EAAE;YAEtC,OAAO,cAAc,IAAI,CAAC,CAAA;gBACxB,IAAI,MAAM,KAAK,IAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO;gBAEtD,OAAQ;oBACN,KAAK;wBAAK,OAAO,MAAM,KAAK,GAAG;oBAC/B,KAAK;wBAAM,OAAO,MAAM,KAAK,IAAI;oBACjC,KAAK;wBAAK,OAAO,MAAM,KAAK,GAAG;oBAC/B,KAAK;wBAAM,OAAO,MAAM,KAAK,IAAI;oBACjC,KAAK;wBAAK,OAAO,MAAM,KAAK,KAAK;oBACjC;wBAAS,OAAO;gBAClB;YACF;QACF;QAEA,gCAAgC;QAChC,MAAM,cAAc,WAAW;QAC/B,IAAI,CAAC,MAAM,cAAc;YACvB,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,QACtC,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK;YAEhE,IAAI,cAAc,OAAO;QAC3B;QAEA,cAAc;QACd,IAAI,YAAY;YACd,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,UAAU;QAC5C,OAAO;YACL,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC;QACjD;IACF,GAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,uBAAuB;YACvB;QACF;QAEA,8BAA8B;QAC9B,MAAM,cAAc,WAAW,KAAK,CAAC,KAClC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAEhC,IAAI,WAAmB,EAAE;QAEzB,IAAI,eAAe,UAAU;YAC3B,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,4BAA4B;gBAC5B,WAAW,KAAK,MAAM,CAAC,CAAA,OAAQ,gBAAgB,MAAM,WAAW,CAAC,EAAE,EAAE;YACvE,OAAO;gBACL,uCAAuC;gBACvC,WAAW,KAAK,MAAM,CAAC,CAAA,OACrB,YAAY,KAAK,CAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;YAE1D;QACF,OAAO;YACL,qDAAqD;YACrD,WAAW,KAAK,MAAM,CAAC,CAAA,OACrB,YAAY,IAAI,CAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;QAEzD;QAEA,uBAAuB;IACzB,GAAG;QAAC;QAAY;QAAY;QAAM;QAAsB;KAAgB;IAExE,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,cAAc;QACd,cAAc;IAChB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;oCACtD,UAAU;oCACV,WAAU;;;;;;gCAEX,4BACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAMnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAY,eAAe,CAAC,QAAiC,cAAc;;kDACxF,8OAAC,kIAAA,CAAA,gBAAa;kDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;;;;;;;;;;;;;;;;;;wBAMhC,4BACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU;sCACX;;;;;;wBAMF,mCACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,mBAAmB,YAAY;4BACxC,MAAK;4BACL,SAAS;4BACT,WAAU;;gCAET,iCAAmB,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAAe,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAC3E,mBAAmB,yBAAyB;;;;;;;;;;;;;gBAMlD,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { TableRow, TableCell } from '@/components/ui/table'\nimport { Cavo } from '@/types'\nimport FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'\nimport SmartCaviFilter from './SmartCaviFilter'\nimport {\n  MoreHorizontal,\n  Cable,\n  Settings,\n  Zap,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Package\n} from 'lucide-react'\n\ninterface CaviTableProps {\n  cavi: Cavo[]\n  loading?: boolean\n  selectionEnabled?: boolean\n  selectedCavi?: string[]\n  onSelectionChange?: (selectedIds: string[]) => void\n  onStatusAction?: (cavo: Cavo, action: string) => void\n  onContextMenuAction?: (cavo: Cavo, action: string) => void\n}\n\nexport default function CaviTable({\n  cavi = [],\n  loading = false,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange,\n  onStatusAction,\n  onContextMenuAction\n}: CaviTableProps) {\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)\n  const [filteredCavi, setFilteredCavi] = useState(cavi)\n  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)\n\n  // Aggiorna i cavi quando cambiano i cavi originali\n  useEffect(() => {\n    setSmartFilteredCavi(cavi)\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Gestione filtri intelligenti\n  const handleSmartFilterChange = (filtered: Cavo[]) => {\n    setSmartFilteredCavi(filtered)\n  }\n\n  // Gestione filtri tabella\n  const handleTableFilterChange = (filtered: Cavo[]) => {\n    setFilteredCavi(filtered)\n  }\n\n  const handleSelectionToggle = () => {\n    setInternalSelectionEnabled(!internalSelectionEnabled)\n  }\n\n  // Gestione selezione\n  const handleSelectAll = (checked: boolean) => {\n    if (onSelectionChange) {\n      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])\n    }\n  }\n\n  const handleSelectCavo = (cavoId: string, checked: boolean) => {\n    if (onSelectionChange) {\n      const newSelection = checked\n        ? [...selectedCavi, cavoId]\n        : selectedCavi.filter(id => id !== cavoId)\n      onSelectionChange(newSelection)\n    }\n  }\n\n  // Define columns matching original webapp structure\n  const columns: ColumnDef[] = useMemo(() => {\n    const baseColumns: ColumnDef[] = [\n      {\n        field: 'id_cavo',\n        headerName: 'ID Cavo',\n        dataType: 'text',\n        headerStyle: { fontWeight: 'bold' },\n        renderCell: (row: Cavo) => (\n          <span className=\"font-semibold text-mariner-900\">{row.id_cavo}</span>\n        )\n      },\n      {\n        field: 'sistema',\n        headerName: 'Sistema',\n        dataType: 'text'\n      },\n      {\n        field: 'utility',\n        headerName: 'Utility',\n        dataType: 'text'\n      },\n      {\n        field: 'tipologia',\n        headerName: 'Tipologia',\n        dataType: 'text'\n      },\n      {\n        field: 'formazione',\n        headerName: 'Formazione',\n        dataType: 'text',\n        align: 'right',\n        renderCell: (row: Cavo) => row.formazione || row.sezione\n      },\n      {\n        field: 'metri_teorici',\n        headerName: 'Metri Teorici',\n        dataType: 'number',\n        align: 'right',\n        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n      },\n      {\n        field: 'metri_posati',\n        headerName: 'Metri Reali',\n        dataType: 'number',\n        align: 'right',\n        renderCell: (row: Cavo) => {\n          const metri = row.metri_posati || row.metratura_reale || 0\n          return metri ? metri.toFixed(1) : '0'\n        }\n      },\n      {\n        field: 'ubicazione_partenza',\n        headerName: 'Da',\n        dataType: 'text',\n        renderCell: (row: Cavo) => row.da || row.ubicazione_partenza\n      },\n      {\n        field: 'ubicazione_arrivo',\n        headerName: 'A',\n        dataType: 'text',\n        renderCell: (row: Cavo) => row.a || row.ubicazione_arrivo\n      },\n      {\n        field: 'id_bobina',\n        headerName: 'Bobina',\n        dataType: 'text',\n        renderCell: (row: Cavo) => row.id_bobina || 'N/A'\n      },\n      {\n        field: 'stato_installazione',\n        headerName: 'Stato',\n        dataType: 'text',\n        align: 'center',\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getStatusBadge(row)\n      },\n      {\n        field: 'collegamenti',\n        headerName: 'Collegamenti',\n        dataType: 'text',\n        align: 'center',\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getConnectionButton(row)\n      },\n      {\n        field: 'certificato',\n        headerName: 'Certificato',\n        dataType: 'text',\n        align: 'center',\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getCertificationButton(row)\n      },\n      {\n        field: 'azioni',\n        headerName: 'Azioni',\n        dataType: 'text',\n        align: 'center',\n        disableFilter: true,\n        disableSort: true,\n        renderCell: (row: Cavo) => getStatusButton(row)\n      },\n      {\n        field: 'menu',\n        headerName: '',\n        width: 50,\n        disableFilter: true,\n        disableSort: true,\n        align: 'center',\n        renderCell: (row: Cavo) => (\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => onContextMenuAction?.(row, 'menu')}\n          >\n            <MoreHorizontal className=\"h-4 w-4\" />\n          </Button>\n        )\n      }\n    ]\n\n    // Add selection column if enabled\n    if (internalSelectionEnabled) {\n      baseColumns.unshift({\n        field: 'selection',\n        headerName: '',\n        disableFilter: true,\n        disableSort: true,\n        width: 50,\n        align: 'center',\n        renderHeader: () => (\n          <Checkbox\n            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}\n            onCheckedChange={handleSelectAll}\n          />\n        ),\n        renderCell: (row: Cavo) => (\n          <Checkbox\n            checked={selectedCavi.includes(row.id_cavo)}\n            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}\n            onClick={(e) => e.stopPropagation()}\n          />\n        )\n      })\n    }\n\n    return baseColumns\n  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])\n\n  // Custom row renderer for selection and context menu\n  const renderRow = (row: Cavo, index: number) => {\n    const isSelected = selectedCavi.includes(row.id_cavo)\n\n    return (\n      <TableRow\n        key={row.id_cavo}\n        className={`${isSelected ? 'bg-mariner-50' : ''} hover:bg-mariner-50 cursor-pointer border-b border-mariner-100`}\n        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}\n        onContextMenu={(e) => {\n          e.preventDefault()\n          onContextMenuAction?.(row, 'context_menu')\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            className={`py-2 px-4 ${column.align === 'center' ? 'text-center' : column.align === 'right' ? 'text-right' : ''}`}\n            style={column.cellStyle}\n            onClick={(e) => {\n              // Prevent row click for action columns\n              if (['stato_installazione', 'collegamenti', 'certificato', 'azioni', 'menu'].includes(column.field)) {\n                e.stopPropagation()\n              }\n            }}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    )\n  }\n\n  // Funzioni di utilità per lo stato\n  const getStatusBadge = (cavo: Cavo) => {\n    // Verifica se il cavo è assegnato a una comanda\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n\n    // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda\n    if (comandaAttiva && cavo.stato_installazione === 'In corso') {\n      return (\n        <Badge\n          className=\"bg-blue-600 text-white cursor-pointer hover:bg-blue-700\"\n          onClick={() => onStatusAction?.(cavo, 'view_command', comandaAttiva)}\n        >\n          {comandaAttiva}\n        </Badge>\n      )\n    }\n\n    // Logica normale per gli altri stati\n    const stato = cavo.stato_installazione || 'Da installare'\n\n    switch (stato) {\n      case 'Installato':\n        return <Badge className=\"bg-green-100 text-green-800\">Installato</Badge>\n      case 'In corso':\n        return <Badge className=\"bg-yellow-100 text-yellow-800\">In corso</Badge>\n      case 'Da installare':\n        return <Badge variant=\"outline\">Da installare</Badge>\n      default:\n        return <Badge variant=\"outline\">{stato}</Badge>\n    }\n  }\n\n  const getStatusButton = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0\n\n    if (!isInstalled) {\n      return (\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          onClick={() => onStatusAction?.(cavo, 'insert_meters')}\n          className=\"text-xs\"\n        >\n          <Package className=\"h-3 w-3 mr-1\" />\n          Inserisci Metri\n        </Button>\n      )\n    } else {\n      return (\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          onClick={() => onStatusAction?.(cavo, 'modify_reel')}\n          className=\"text-xs\"\n        >\n          <Settings className=\"h-3 w-3 mr-1\" />\n          Modifica Bobina\n        </Button>\n      )\n    }\n  }\n\n  const getConnectionButton = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n\n    if (!isInstalled) {\n      return (\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          disabled\n          className=\"text-xs\"\n        >\n          <AlertCircle className=\"h-3 w-3 mr-1\" />\n          Non disponibile\n        </Button>\n      )\n    }\n\n    let label, actionType, variant: \"default\" | \"outline\" | \"secondary\" | \"destructive\" | \"ghost\" | \"link\" = \"outline\"\n    let icon\n\n    switch (collegamento) {\n      case 0:\n        label = \"⚪⚪ Collega cavo\"\n        actionType = \"connect_cable\"\n        icon = <Zap className=\"h-3 w-3 mr-1\" />\n        break\n      case 1:\n        label = \"🟢⚪ Completa collegamento\"\n        actionType = \"connect_arrival\"\n        icon = <Zap className=\"h-3 w-3 mr-1\" />\n        variant = \"secondary\"\n        break\n      case 2:\n        label = \"⚪🟢 Completa collegamento\"\n        actionType = \"connect_departure\"\n        icon = <Zap className=\"h-3 w-3 mr-1\" />\n        variant = \"secondary\"\n        break\n      case 3:\n        label = \"🟢🟢 Scollega cavo\"\n        actionType = \"disconnect_cable\"\n        icon = <CheckCircle className=\"h-3 w-3 mr-1\" />\n        variant = \"default\"\n        break\n      default:\n        label = \"Gestisci collegamenti\"\n        actionType = \"manage_connections\"\n        icon = <Settings className=\"h-3 w-3 mr-1\" />\n        break\n    }\n\n    return (\n      <Button\n        size=\"sm\"\n        variant={variant}\n        onClick={() => onStatusAction?.(cavo, actionType)}\n        className=\"text-xs\"\n      >\n        {icon}\n        {label}\n      </Button>\n    )\n  }\n\n  const getCertificationButton = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n\n    if (!isInstalled) {\n      return (\n        <Button\n          size=\"sm\"\n          variant=\"outline\"\n          disabled\n          className=\"text-xs\"\n        >\n          <AlertCircle className=\"h-3 w-3 mr-1\" />\n          Non disponibile\n        </Button>\n      )\n    }\n\n    if (isCertified) {\n      return (\n        <Button\n          size=\"sm\"\n          variant=\"default\"\n          onClick={() => onStatusAction?.(cavo, 'generate_pdf')}\n          className=\"text-xs bg-green-600 hover:bg-green-700\"\n        >\n          <CheckCircle className=\"h-3 w-3 mr-1\" />\n          Genera PDF\n        </Button>\n      )\n    }\n\n    return (\n      <Button\n        size=\"sm\"\n        variant=\"outline\"\n        onClick={() => onStatusAction?.(cavo, 'create_certificate')}\n        className=\"text-xs\"\n      >\n        <Clock className=\"h-3 w-3 mr-1\" />\n        Certifica cavo\n      </Button>\n    )\n  }\n\n  return (\n    <div>\n      {/* Smart Filter */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n        selectionEnabled={internalSelectionEnabled}\n        onSelectionToggle={handleSelectionToggle}\n      />\n\n      {/* Selection info only - no title */}\n      {internalSelectionEnabled && selectedCavi.length > 0 && (\n        <div className=\"mb-4 flex justify-end\">\n          <Badge variant=\"secondary\" className=\"bg-mariner-100 text-mariner-800\">\n            {selectedCavi.length} selezionati\n          </Badge>\n        </div>\n      )}\n\n      {/* Filterable Table */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        onFilteredDataChange={handleTableFilterChange}\n        renderRow={renderRow}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;AAgCe,SAAS,UAAU,EAChC,OAAO,EAAE,EACT,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,eAAe,EAAE,EACjB,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACJ;IACf,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;QACrB,gBAAgB;IAClB,GAAG;QAAC;KAAK;IAET,+BAA+B;IAC/B,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;IACvB;IAEA,0BAA0B;IAC1B,MAAM,0BAA0B,CAAC;QAC/B,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,4BAA4B,CAAC;IAC/B;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,IAAI,mBAAmB;YACrB,kBAAkB,UAAU,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE;QACnE;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,mBAAmB;YACrB,MAAM,eAAe,UACjB;mBAAI;gBAAc;aAAO,GACzB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YACrC,kBAAkB;QACpB;IACF;IAEA,oDAAoD;IACpD,MAAM,UAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,MAAM,cAA2B;YAC/B;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,aAAa;oBAAE,YAAY;gBAAO;gBAClC,YAAY,CAAC,oBACX,8OAAC;wBAAK,WAAU;kCAAkC,IAAI,OAAO;;;;;;YAEjE;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,MAAc,IAAI,UAAU,IAAI,IAAI,OAAO;YAC1D;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,MAAc,IAAI,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK;YAChF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC;oBACX,MAAM,QAAQ,IAAI,YAAY,IAAI,IAAI,eAAe,IAAI;oBACzD,OAAO,QAAQ,MAAM,OAAO,CAAC,KAAK;gBACpC;YACF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,YAAY,CAAC,MAAc,IAAI,EAAE,IAAI,IAAI,mBAAmB;YAC9D;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,YAAY,CAAC,MAAc,IAAI,CAAC,IAAI,IAAI,iBAAiB;YAC3D;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,YAAY,CAAC,MAAc,IAAI,SAAS,IAAI;YAC9C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY,CAAC,MAAc,eAAe;YAC5C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY,CAAC,MAAc,oBAAoB;YACjD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY,CAAC,MAAc,uBAAuB;YACpD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY,CAAC,MAAc,gBAAgB;YAC7C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,OAAO;gBACP,eAAe;gBACf,aAAa;gBACb,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,sBAAsB,KAAK;kCAE1C,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;YAGhC;SACD;QAED,kCAAkC;QAClC,IAAI,0BAA0B;YAC5B,YAAY,OAAO,CAAC;gBAClB,OAAO;gBACP,YAAY;gBACZ,eAAe;gBACf,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,MAAM,KAAK,aAAa,MAAM,IAAI,aAAa,MAAM,GAAG;wBAC9E,iBAAiB;;;;;;gBAGrB,YAAY,CAAC,oBACX,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,QAAQ,CAAC,IAAI,OAAO;wBAC1C,iBAAiB,CAAC,UAAY,iBAAiB,IAAI,OAAO,EAAE;wBAC5D,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;YAGvC;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAA0B;QAAc;QAAc;QAAiB;KAAiB;IAE5F,qDAAqD;IACrD,MAAM,YAAY,CAAC,KAAW;QAC5B,MAAM,aAAa,aAAa,QAAQ,CAAC,IAAI,OAAO;QAEpD,qBACE,8OAAC,iIAAA,CAAA,WAAQ;YAEP,WAAW,GAAG,aAAa,kBAAkB,GAAG,+DAA+D,CAAC;YAChH,SAAS,IAAM,4BAA4B,iBAAiB,IAAI,OAAO,EAAE,CAAC;YAC1E,eAAe,CAAC;gBACd,EAAE,cAAc;gBAChB,sBAAsB,KAAK;YAC7B;sBAEC,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;oBAER,WAAW,CAAC,UAAU,EAAE,OAAO,KAAK,KAAK,WAAW,gBAAgB,OAAO,KAAK,KAAK,UAAU,eAAe,IAAI;oBAClH,OAAO,OAAO,SAAS;oBACvB,SAAS,CAAC;wBACR,uCAAuC;wBACvC,IAAI;4BAAC;4BAAuB;4BAAgB;4BAAe;4BAAU;yBAAO,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG;4BACnG,EAAE,eAAe;wBACnB;oBACF;8BAEC,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,CAAC;mBAV1D,OAAO,KAAK;;;;;WAVhB,IAAI,OAAO;;;;;IAyBtB;IAEA,mCAAmC;IACnC,MAAM,iBAAiB,CAAC;QACtB,gDAAgD;QAChD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QAEzD,gFAAgF;QAChF,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,8EAA8E;QAC9E,IAAI,iBAAiB,KAAK,mBAAmB,KAAK,YAAY;YAC5D,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBACJ,WAAU;gBACV,SAAS,IAAM,iBAAiB,MAAM,gBAAgB;0BAErD;;;;;;QAGP;QAEA,qCAAqC;QACrC,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAE1C,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAgC;;;;;;YAC1D,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc,KAAK,YAAY,GAAG;QAExC,IAAI,CAAC,aAAa;YAChB,qBACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS,IAAM,iBAAiB,MAAM;gBACtC,WAAU;;kCAEV,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI1C,OAAO;YACL,qBACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS,IAAM,iBAAiB,MAAM;gBACtC,WAAU;;kCAEV,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI3C;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,IAAI,CAAC,aAAa;YAChB,qBACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,QAAQ;gBACR,WAAU;;kCAEV,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI9C;QAEA,IAAI,OAAO,YAAY,UAAkF;QACzG,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;gBACtB;YACF,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;gBACtB,UAAU;gBACV;YACF,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;gBACtB,UAAU;gBACV;YACF,KAAK;gBACH,QAAQ;gBACR,aAAa;gBACb,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;gBAC9B,UAAU;gBACV;YACF;gBACE,QAAQ;gBACR,aAAa;gBACb,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBAC3B;QACJ;QAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,MAAK;YACL,SAAS;YACT,SAAS,IAAM,iBAAiB,MAAM;YACtC,WAAU;;gBAET;gBACA;;;;;;;IAGP;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QAEnG,IAAI,CAAC,aAAa;YAChB,qBACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,QAAQ;gBACR,WAAU;;kCAEV,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI9C;QAEA,IAAI,aAAa;YACf,qBACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS,IAAM,iBAAiB,MAAM;gBACtC,WAAU;;kCAEV,8OAAC,2NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;QAI9C;QAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,MAAK;YACL,SAAQ;YACR,SAAS,IAAM,iBAAiB,MAAM;YACtC,WAAU;;8BAEV,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;gBAAiB;;;;;;;IAIxC;IAEA,qBACE,8OAAC;;0BAEC,8OAAC,6IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,sBAAsB;gBACtB,SAAS;gBACT,kBAAkB;gBAClB,mBAAmB;;;;;;YAIpB,4BAA4B,aAAa,MAAM,GAAG,mBACjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,WAAU;;wBAClC,aAAa,MAAM;wBAAC;;;;;;;;;;;;0BAM3B,8OAAC,+IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,cAAa;gBACb,sBAAsB;gBACtB,WAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Cable, \n  CheckCircle, \n  Clock, \n  AlertTriangle,\n  Zap,\n  Package,\n  BarChart3\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\ninterface CaviStatisticsProps {\n  cavi: Cavo[]\n  filteredCavi: Cavo[]\n  className?: string\n  revisioneCorrente?: string\n}\n\nexport default function CaviStatistics({\n  cavi,\n  filteredCavi,\n  className,\n  revisioneCorrente\n}: CaviStatisticsProps) {\n  const stats = useMemo(() => {\n    const totalCavi = cavi.length\n    const filteredCount = filteredCavi.length\n    \n    // Installation status\n    const installati = filteredCavi.filter(c => \n      c.stato_installazione === 'Installato' || \n      (c.metri_posati && c.metri_posati > 0) ||\n      (c.metratura_reale && c.metratura_reale > 0)\n    ).length\n    \n    const inCorso = filteredCavi.filter(c => \n      c.stato_installazione === 'In corso'\n    ).length\n    \n    const daInstallare = filteredCount - installati - inCorso\n    \n    // Connection status\n    const collegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 3 // Both sides connected\n    }).length\n    \n    const parzialmenteCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 1 || collegamento === 2 // One side connected\n    }).length\n    \n    const nonCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)\n    }).length\n    \n    // Certification status\n    const certificati = filteredCavi.filter(c => \n      c.certificato === true || \n      c.certificato === 'SI' || \n      c.certificato === 'CERTIFICATO'\n    ).length\n    \n    // Meters calculation\n    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = filteredCavi.reduce((sum, c) => {\n      const metri = c.metri_posati || c.metratura_reale || 0\n      return sum + metri\n    }, 0)\n    \n    const percentualeInstallazione = metriTotali > 0 ? (metriInstallati / metriTotali * 100) : 0\n    \n    return {\n      totalCavi,\n      filteredCount,\n      installati,\n      inCorso,\n      daInstallare,\n      collegati,\n      parzialmenteCollegati,\n      nonCollegati,\n      certificati,\n      metriTotali,\n      metriInstallati,\n      percentualeInstallazione\n    }\n  }, [cavi, filteredCavi])\n\n  return (\n    <Card className={className}>\n      <CardContent className=\"p-4\">\n        {/* Header with revision */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-2\">\n            <BarChart3 className=\"h-4 w-4 text-mariner-600\" />\n            <span className=\"text-sm font-semibold text-mariner-900\">Statistiche Cavi</span>\n          </div>\n          {revisioneCorrente && (\n            <Badge variant=\"outline\" className=\"text-xs font-medium\">\n              Rev. {revisioneCorrente}\n            </Badge>\n          )}\n        </div>\n\n        {/* Single row statistics */}\n        <div className=\"flex flex-wrap items-center gap-3 text-sm\">\n\n          {/* Total cables */}\n          <div className=\"flex items-center space-x-1.5 bg-mariner-50 px-3 py-1.5 rounded-lg\">\n            <Cable className=\"h-4 w-4 text-mariner-600\" />\n            <span className=\"font-bold text-mariner-900\">{stats.filteredCount}</span>\n            <span className=\"text-mariner-600\">di {stats.totalCavi} cavi</span>\n          </div>\n\n          {/* Installation status */}\n          <div className=\"flex items-center space-x-1.5 bg-green-50 px-3 py-1.5 rounded-lg\">\n            <CheckCircle className=\"h-4 w-4 text-green-600\" />\n            <span className=\"font-bold text-green-700\">{stats.installati}</span>\n            <span className=\"text-green-600\">installati</span>\n          </div>\n\n          <div className=\"flex items-center space-x-1.5 bg-yellow-50 px-3 py-1.5 rounded-lg\">\n            <Clock className=\"h-4 w-4 text-yellow-600\" />\n            <span className=\"font-bold text-yellow-700\">{stats.inCorso}</span>\n            <span className=\"text-yellow-600\">in corso</span>\n          </div>\n\n          <div className=\"flex items-center space-x-1.5 bg-gray-50 px-3 py-1.5 rounded-lg\">\n            <AlertTriangle className=\"h-4 w-4 text-gray-600\" />\n            <span className=\"font-bold text-gray-700\">{stats.daInstallare}</span>\n            <span className=\"text-gray-600\">da installare</span>\n          </div>\n\n          {/* Connection status */}\n          <div className=\"flex items-center space-x-1.5 bg-blue-50 px-3 py-1.5 rounded-lg\">\n            <Zap className=\"h-4 w-4 text-blue-600\" />\n            <span className=\"font-bold text-blue-700\">{stats.collegati}</span>\n            <span className=\"text-blue-600\">collegati</span>\n          </div>\n\n          {/* Certification status */}\n          <div className=\"flex items-center space-x-1.5 bg-purple-50 px-3 py-1.5 rounded-lg\">\n            <Package className=\"h-4 w-4 text-purple-600\" />\n            <span className=\"font-bold text-purple-700\">{stats.certificati}</span>\n            <span className=\"text-purple-600\">certificati</span>\n          </div>\n\n          {/* Meters progress */}\n          <div className=\"flex items-center space-x-1.5 bg-indigo-50 px-3 py-1.5 rounded-lg\">\n            <div className=\"h-4 w-4 flex items-center justify-center\">\n              <div className=\"h-2.5 w-2.5 bg-indigo-600 rounded-full\"></div>\n            </div>\n            <span className=\"font-bold text-indigo-700\">{stats.metriInstallati.toLocaleString()}m</span>\n            <span className=\"text-indigo-600\">di {stats.metriTotali.toLocaleString()}m</span>\n            <span className=\"text-indigo-700 font-semibold\">({stats.percentualeInstallazione.toFixed(1)}%)</span>\n          </div>\n\n          {/* Connection details - only show if there are partial/non-connected cables */}\n          {stats.parzialmenteCollegati > 0 && (\n            <div className=\"flex items-center space-x-1.5 bg-yellow-50 px-3 py-1.5 rounded-lg\">\n              <div className=\"h-4 w-4 flex items-center justify-center\">\n                <div className=\"h-2.5 w-2.5 bg-yellow-500 rounded-full\"></div>\n              </div>\n              <span className=\"font-bold text-yellow-700\">{stats.parzialmenteCollegati}</span>\n              <span className=\"text-yellow-600\">parziali</span>\n            </div>\n          )}\n\n          {stats.nonCollegati > 0 && (\n            <div className=\"flex items-center space-x-1.5 bg-red-50 px-3 py-1.5 rounded-lg\">\n              <div className=\"h-4 w-4 flex items-center justify-center\">\n                <div className=\"h-2.5 w-2.5 bg-red-500 rounded-full\"></div>\n              </div>\n              <span className=\"font-bold text-red-700\">{stats.nonCollegati}</span>\n              <span className=\"text-red-600\">non collegati</span>\n            </div>\n          )}\n\n        </div>\n\n        {/* Progress bar */}\n        {stats.metriTotali > 0 && (\n          <div className=\"mt-4 bg-gray-50 p-3 rounded-lg\">\n            <div className=\"flex justify-between text-sm font-medium text-gray-700 mb-2\">\n              <span>Progresso installazione</span>\n              <span className=\"text-mariner-700\">{stats.percentualeInstallazione.toFixed(1)}%</span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className=\"bg-mariner-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}\n              />\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAuBe,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,iBAAiB,EACG;IACpB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,YAAY,KAAK,MAAM;QAC7B,MAAM,gBAAgB,aAAa,MAAM;QAEzC,sBAAsB;QACtB,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,IACrC,EAAE,mBAAmB,KAAK,gBACzB,EAAE,YAAY,IAAI,EAAE,YAAY,GAAG,KACnC,EAAE,eAAe,IAAI,EAAE,eAAe,GAAG,GAC1C,MAAM;QAER,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,IAClC,EAAE,mBAAmB,KAAK,YAC1B,MAAM;QAER,MAAM,eAAe,gBAAgB,aAAa;QAElD,oBAAoB;QACpB,MAAM,YAAY,aAAa,MAAM,CAAC,CAAA;YACpC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,EAAE,uBAAuB;;QACnD,GAAG,MAAM;QAET,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA;YAChD,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,iBAAiB,EAAE,qBAAqB;;QACvE,GAAG,MAAM;QAET,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA;YACvC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,CAAC,EAAE,YAAY,GAAG,KAAK,EAAE,eAAe,GAAG,CAAC;QAC3E,GAAG,MAAM;QAET,uBAAuB;QACvB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,IACtC,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,eAClB,MAAM;QAER,qBAAqB;QACrB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAClF,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAC,KAAK;YAChD,MAAM,QAAQ,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI;YACrD,OAAO,MAAM;QACf,GAAG;QAEH,MAAM,2BAA2B,cAAc,IAAK,kBAAkB,cAAc,MAAO;QAE3F,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAM;KAAa;IAEvB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;kBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAyC;;;;;;;;;;;;wBAE1D,mCACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;;gCAAsB;gCACjD;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAA8B,MAAM,aAAa;;;;;;8CACjE,8OAAC;oCAAK,WAAU;;wCAAmB;wCAAI,MAAM,SAAS;wCAAC;;;;;;;;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CAA4B,MAAM,UAAU;;;;;;8CAC5D,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;sCAGnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAK,WAAU;8CAA6B,MAAM,OAAO;;;;;;8CAC1D,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAGpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAA2B,MAAM,YAAY;;;;;;8CAC7D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA2B,MAAM,SAAS;;;;;;8CAC1D,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAK,WAAU;8CAA6B,MAAM,WAAW;;;;;;8CAC9D,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;sCAIpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;;wCAA6B,MAAM,eAAe,CAAC,cAAc;wCAAG;;;;;;;8CACpF,8OAAC;oCAAK,WAAU;;wCAAkB;wCAAI,MAAM,WAAW,CAAC,cAAc;wCAAG;;;;;;;8CACzE,8OAAC;oCAAK,WAAU;;wCAAgC;wCAAE,MAAM,wBAAwB,CAAC,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAI7F,MAAM,qBAAqB,GAAG,mBAC7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;8CAA6B,MAAM,qBAAqB;;;;;;8CACxE,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;wBAIrC,MAAM,YAAY,GAAG,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAK,WAAU;8CAA0B,MAAM,YAAY;;;;;;8CAC5D,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;;;;;;;gBAOpC,MAAM,WAAW,GAAG,mBACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAU;;wCAAoB,MAAM,wBAAwB,CAAC,OAAO,CAAC;wCAAG;;;;;;;;;;;;;sCAEhF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,wBAAwB,EAAE,KAAK,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpF", "debugId": null}}, {"offset": {"line": 2918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/InserisciMetriDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface Bobina {\n  id_bobina: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n}\n\ninterface InserisciMetriDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function InserisciMetriDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: InserisciMetriDialogProps) {\n  const { cantiere } = useAuth()\n  const [metriPosati, setMetriPosati] = useState('')\n  const [selectedBobina, setSelectedBobina] = useState('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [error, setError] = useState('')\n\n  // Carica bobine compatibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      loadBobineCompatibili()\n      setMetriPosati(cavo.metri_teorici?.toString() || '')\n      setSelectedBobina('')\n      setError('')\n    }\n  }, [open, cavo])\n\n  const loadBobineCompatibili = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoadingBobine(true)\n\n      // Carica bobine compatibili dall'API\n      const response = await parcoCaviApi.getBobineCompatibili(cantiere.id_cantiere, {\n        tipologia: cavo.tipologia,\n        n_conduttori: cavo.n_conduttori,\n        sezione: cavo.formazione || cavo.sezione\n      })\n\n      const bobineCompatibili: Bobina[] = response.data.map((b: any) => ({\n        id_bobina: b.id_bobina,\n        tipologia: b.tipologia,\n        formazione: b.sezione,\n        metri_residui: b.metri_residui,\n        fornitore: b.fornitore\n      }))\n\n      // Aggiungi sempre BOBINA_VUOTA come opzione\n      const bobineConVuota: Bobina[] = [\n        {\n          id_bobina: 'BOBINA_VUOTA',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || cavo.sezione || '',\n          metri_residui: 0\n        },\n        ...bobineCompatibili\n      ]\n\n      setBobine(bobineConVuota)\n    } catch (error) {\n      console.error('Errore nel caricamento bobine:', error)\n      // Fallback con solo BOBINA_VUOTA\n      setBobine([{\n        id_bobina: 'BOBINA_VUOTA',\n        tipologia: cavo.tipologia || '',\n        formazione: cavo.formazione || cavo.sezione || '',\n        metri_residui: 0\n      }])\n      onError('Errore nel caricamento delle bobine disponibili')\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  const handleSave = async () => {\n    if (!cavo || !metriPosati || !selectedBobina) {\n      setError('Compilare tutti i campi obbligatori')\n      return\n    }\n\n    const metri = parseFloat(metriPosati)\n    if (isNaN(metri) || metri <= 0) {\n      setError('Inserire un valore valido per i metri posati')\n      return\n    }\n\n    if (metri > (cavo.metri_teorici || 0)) {\n      setError('I metri posati non possono superare i metri teorici')\n      return\n    }\n\n    // Verifica metri disponibili nella bobina (se non è BOBINA_VUOTA)\n    const bobina = bobine.find(b => b.id_bobina === selectedBobina)\n    if (bobina && bobina.id_bobina !== 'BOBINA_VUOTA' && metri > bobina.metri_residui) {\n      setError(`La bobina selezionata ha solo ${bobina.metri_residui}m disponibili`)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Aggiorna metri posati tramite API\n      await caviApi.updateMetriPosati(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        metri,\n        selectedBobina !== 'BOBINA_VUOTA' ? selectedBobina : undefined\n      )\n\n      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nel salvataggio:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il salvataggio dei metri posati'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setMetriPosati('')\n      setSelectedBobina('')\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogDescription>\n            Inserisci i metri posati per il cavo {cavo.id_cavo}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Informazioni cavo */}\n          <div className=\"p-3 bg-gray-50 rounded-lg\">\n            <div className=\"grid grid-cols-2 gap-2 text-sm\">\n              <div><strong>Sistema:</strong> {cavo.sistema}</div>\n              <div><strong>Utility:</strong> {cavo.utility}</div>\n              <div><strong>Tipologia:</strong> {cavo.tipologia}</div>\n              <div><strong>Formazione:</strong> {cavo.formazione}</div>\n              <div><strong>Da:</strong> {cavo.da}</div>\n              <div><strong>A:</strong> {cavo.a}</div>\n              <div className=\"col-span-2\">\n                <strong>Metri teorici:</strong> {cavo.metri_teorici}m\n              </div>\n            </div>\n          </div>\n\n          {/* Metri posati */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"metri\">Metri Posati *</Label>\n            <Input\n              id=\"metri\"\n              type=\"number\"\n              step=\"0.1\"\n              min=\"0\"\n              max={cavo.metri_teorici || 0}\n              value={metriPosati}\n              onChange={(e) => setMetriPosati(e.target.value)}\n              placeholder=\"Inserisci metri posati\"\n            />\n          </div>\n\n          {/* Selezione bobina */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"bobina\">Bobina *</Label>\n            {loadingBobine ? (\n              <div className=\"flex items-center space-x-2 p-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento bobine...</span>\n              </div>\n            ) : (\n              <Select value={selectedBobina} onValueChange={setSelectedBobina}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Seleziona bobina\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {bobine.map((bobina) => (\n                    <SelectItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                      <div className=\"flex flex-col\">\n                        <span>{bobina.id_bobina}</span>\n                        {bobina.id_bobina !== 'BOBINA_VUOTA' && (\n                          <span className=\"text-xs text-muted-foreground\">\n                            {bobina.metri_residui}m disponibili\n                            {bobina.fornitore && ` - ${bobina.fornitore}`}\n                          </span>\n                        )}\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            )}\n          </div>\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleSave} \n            disabled={loading || !metriPosati || !selectedBobina}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAEA;AACA;AAzBA;;;;;;;;;;;;AA2Ce,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB;YACA,eAAe,KAAK,aAAa,EAAE,cAAc;YACjD,kBAAkB;YAClB,SAAS;QACX;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,wBAAwB;QAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,iBAAiB;YAEjB,qCAAqC;YACrC,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,SAAS,WAAW,EAAE;gBAC7E,WAAW,KAAK,SAAS;gBACzB,cAAc,KAAK,YAAY;gBAC/B,SAAS,KAAK,UAAU,IAAI,KAAK,OAAO;YAC1C;YAEA,MAAM,oBAA8B,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,IAAW,CAAC;oBACjE,WAAW,EAAE,SAAS;oBACtB,WAAW,EAAE,SAAS;oBACtB,YAAY,EAAE,OAAO;oBACrB,eAAe,EAAE,aAAa;oBAC9B,WAAW,EAAE,SAAS;gBACxB,CAAC;YAED,4CAA4C;YAC5C,MAAM,iBAA2B;gBAC/B;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI,KAAK,OAAO,IAAI;oBAC/C,eAAe;gBACjB;mBACG;aACJ;YAED,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,iCAAiC;YACjC,UAAU;gBAAC;oBACT,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI,KAAK,OAAO,IAAI;oBAC/C,eAAe;gBACjB;aAAE;YACF,QAAQ;QACV,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB;YAC5C,SAAS;YACT;QACF;QAEA,MAAM,QAAQ,WAAW;QACzB,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,SAAS;YACT;QACF;QAEA,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,GAAG;YACrC,SAAS;YACT;QACF;QAEA,kEAAkE;QAClE,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,IAAI,UAAU,OAAO,SAAS,KAAK,kBAAkB,QAAQ,OAAO,aAAa,EAAE;YACjF,SAAS,CAAC,8BAA8B,EAAE,OAAO,aAAa,CAAC,aAAa,CAAC;YAC7E;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,OACA,mBAAmB,iBAAiB,iBAAiB;YAGvD,UAAU,CAAC,iDAAiD,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,eAAe;YACf,kBAAkB;YAClB,SAAS;YACT;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;sCACb,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACqB,KAAK,OAAO;;;;;;;;;;;;;8BAItD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAmB;4CAAE,KAAK,SAAS;;;;;;;kDAChD,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAoB;4CAAE,KAAK,UAAU;;;;;;;kDAClD,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAY;4CAAE,KAAK,EAAE;;;;;;;kDAClC,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAW;4CAAE,KAAK,CAAC;;;;;;;kDAChC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAO;;;;;;4CAAuB;4CAAE,KAAK,aAAa;4CAAC;;;;;;;;;;;;;;;;;;sCAM1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAQ;;;;;;8CACvB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,KAAI;oCACJ,KAAK,KAAK,aAAa,IAAI;oCAC3B,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAS;;;;;;gCACvB,8BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;yDAG5B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAgB,eAAe;;sDAC5C,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,OAAO,GAAG,CAAC,CAAC,uBACX,8OAAC,kIAAA,CAAA,aAAU;oDAAwB,OAAO,OAAO,SAAS;8DACxD,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,OAAO,SAAS;;;;;;4DACtB,OAAO,SAAS,KAAK,gCACpB,8OAAC;gEAAK,WAAU;;oEACb,OAAO,aAAa;oEAAC;oEACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,OAAO,SAAS,EAAE;;;;;;;;;;;;;mDANpC,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;wBAkB1C,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,eAAe,CAAC;;gCAErC,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 3647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Package } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface Bobina {\n  id_bobina: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n}\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ModificaBobinaDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const { cantiere } = useAuth()\n  const [selectedBobina, setSelectedBobina] = useState('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [error, setError] = useState('')\n\n  // Carica bobine compatibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      loadBobineCompatibili()\n      setSelectedBobina(cavo.id_bobina || '')\n      setError('')\n    }\n  }, [open, cavo])\n\n  const loadBobineCompatibili = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoadingBobine(true)\n\n      // Carica bobine compatibili dall'API\n      const response = await parcoCaviApi.getBobineCompatibili(cantiere.id_cantiere, {\n        tipologia: cavo.tipologia,\n        n_conduttori: cavo.n_conduttori,\n        sezione: cavo.formazione || cavo.sezione\n      })\n\n      const bobineCompatibili: Bobina[] = response.data.map((b: any) => ({\n        id_bobina: b.id_bobina,\n        tipologia: b.tipologia,\n        formazione: b.sezione,\n        metri_residui: b.metri_residui,\n        fornitore: b.fornitore\n      }))\n\n      // Aggiungi sempre BOBINA_VUOTA come opzione\n      const bobineConVuota: Bobina[] = [\n        {\n          id_bobina: 'BOBINA_VUOTA',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || cavo.sezione || '',\n          metri_residui: 0\n        },\n        ...bobineCompatibili\n      ]\n\n      // Se il cavo ha già una bobina, assicurati che sia nella lista\n      if (cavo.id_bobina && !bobineConVuota.find(b => b.id_bobina === cavo.id_bobina)) {\n        bobineConVuota.push({\n          id_bobina: cavo.id_bobina,\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || cavo.sezione || '',\n          metri_residui: 0 // Bobina attualmente in uso\n        })\n      }\n\n      setBobine(bobineConVuota)\n    } catch (error) {\n      console.error('Errore nel caricamento bobine:', error)\n      // Fallback con solo BOBINA_VUOTA e bobina corrente\n      const fallbackBobine: Bobina[] = [\n        {\n          id_bobina: 'BOBINA_VUOTA',\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || cavo.sezione || '',\n          metri_residui: 0\n        }\n      ]\n\n      if (cavo.id_bobina) {\n        fallbackBobine.push({\n          id_bobina: cavo.id_bobina,\n          tipologia: cavo.tipologia || '',\n          formazione: cavo.formazione || cavo.sezione || '',\n          metri_residui: 0\n        })\n      }\n\n      setBobine(fallbackBobine)\n      onError('Errore nel caricamento delle bobine disponibili')\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  const handleSave = async () => {\n    if (!cavo || !selectedBobina) {\n      setError('Selezionare una bobina')\n      return\n    }\n\n    if (selectedBobina === cavo.id_bobina) {\n      setError('La bobina selezionata è già associata al cavo')\n      return\n    }\n\n    // Verifica che la bobina abbia metri sufficienti (se non è BOBINA_VUOTA)\n    const bobina = bobine.find(b => b.id_bobina === selectedBobina)\n    if (bobina && bobina.id_bobina !== 'BOBINA_VUOTA' && cavo.metri_posati > bobina.metri_residui) {\n      setError(`La bobina selezionata ha solo ${bobina.metri_residui}m disponibili, ma il cavo ha ${cavo.metri_posati}m posati`)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Aggiorna bobina tramite API\n      await caviApi.updateBobina(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        selectedBobina\n      )\n\n      const message = selectedBobina === 'BOBINA_VUOTA'\n        ? `Bobina vuota assegnata al cavo ${cavo.id_cavo}`\n        : `Bobina ${selectedBobina} assegnata al cavo ${cavo.id_cavo}`\n\n      onSuccess(message)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nel salvataggio:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setSelectedBobina('')\n      setError('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center space-x-2\">\n            <Package className=\"h-5 w-5\" />\n            <span>Modifica Bobina</span>\n          </DialogTitle>\n          <DialogDescription>\n            Modifica la bobina associata al cavo {cavo.id_cavo}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Informazioni cavo */}\n          <div className=\"p-3 bg-gray-50 rounded-lg\">\n            <div className=\"grid grid-cols-2 gap-2 text-sm\">\n              <div><strong>Sistema:</strong> {cavo.sistema}</div>\n              <div><strong>Utility:</strong> {cavo.utility}</div>\n              <div><strong>Tipologia:</strong> {cavo.tipologia}</div>\n              <div><strong>Formazione:</strong> {cavo.formazione}</div>\n              <div><strong>Metri posati:</strong> {cavo.metri_posati || 0}m</div>\n              <div><strong>Bobina attuale:</strong> {cavo.id_bobina || 'Nessuna'}</div>\n            </div>\n          </div>\n\n          {/* Selezione nuova bobina */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"bobina\">Nuova Bobina *</Label>\n            {loadingBobine ? (\n              <div className=\"flex items-center space-x-2 p-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento bobine...</span>\n              </div>\n            ) : (\n              <Select value={selectedBobina} onValueChange={setSelectedBobina}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Seleziona nuova bobina\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {bobine.map((bobina) => (\n                    <SelectItem \n                      key={bobina.id_bobina} \n                      value={bobina.id_bobina}\n                      disabled={bobina.id_bobina === cavo.id_bobina}\n                    >\n                      <div className=\"flex flex-col\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span>{bobina.id_bobina}</span>\n                          {bobina.id_bobina === cavo.id_bobina && (\n                            <span className=\"text-xs bg-blue-100 text-blue-800 px-1 rounded\">\n                              Attuale\n                            </span>\n                          )}\n                        </div>\n                        {bobina.id_bobina !== 'BOBINA_VUOTA' && (\n                          <span className=\"text-xs text-muted-foreground\">\n                            {bobina.metri_residui}m disponibili\n                            {bobina.fornitore && ` - ${bobina.fornitore}`}\n                          </span>\n                        )}\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            )}\n          </div>\n\n          {/* Avviso per bobina vuota */}\n          {selectedBobina === 'BOBINA_VUOTA' && (\n            <Alert>\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Stai assegnando una bobina vuota. Questo permetterà di posare il cavo \n                e associare la bobina reale in un secondo momento.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleSave} \n            disabled={loading || !selectedBobina || selectedBobina === cavo.id_bobina}\n          >\n            {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {loading ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAEA;AACA;AAxBA;;;;;;;;;;;AA0Ce,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB;YACA,kBAAkB,KAAK,SAAS,IAAI;YACpC,SAAS;QACX;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,wBAAwB;QAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,iBAAiB;YAEjB,qCAAqC;YACrC,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,SAAS,WAAW,EAAE;gBAC7E,WAAW,KAAK,SAAS;gBACzB,cAAc,KAAK,YAAY;gBAC/B,SAAS,KAAK,UAAU,IAAI,KAAK,OAAO;YAC1C;YAEA,MAAM,oBAA8B,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,IAAW,CAAC;oBACjE,WAAW,EAAE,SAAS;oBACtB,WAAW,EAAE,SAAS;oBACtB,YAAY,EAAE,OAAO;oBACrB,eAAe,EAAE,aAAa;oBAC9B,WAAW,EAAE,SAAS;gBACxB,CAAC;YAED,4CAA4C;YAC5C,MAAM,iBAA2B;gBAC/B;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI,KAAK,OAAO,IAAI;oBAC/C,eAAe;gBACjB;mBACG;aACJ;YAED,+DAA+D;YAC/D,IAAI,KAAK,SAAS,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,KAAK,SAAS,GAAG;gBAC/E,eAAe,IAAI,CAAC;oBAClB,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI,KAAK,OAAO,IAAI;oBAC/C,eAAe,EAAE,4BAA4B;gBAC/C;YACF;YAEA,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,mDAAmD;YACnD,MAAM,iBAA2B;gBAC/B;oBACE,WAAW;oBACX,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI,KAAK,OAAO,IAAI;oBAC/C,eAAe;gBACjB;aACD;YAED,IAAI,KAAK,SAAS,EAAE;gBAClB,eAAe,IAAI,CAAC;oBAClB,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,SAAS,IAAI;oBAC7B,YAAY,KAAK,UAAU,IAAI,KAAK,OAAO,IAAI;oBAC/C,eAAe;gBACjB;YACF;YAEA,UAAU;YACV,QAAQ;QACV,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;YAC5B,SAAS;YACT;QACF;QAEA,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACrC,SAAS;YACT;QACF;QAEA,yEAAyE;QACzE,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,IAAI,UAAU,OAAO,SAAS,KAAK,kBAAkB,KAAK,YAAY,GAAG,OAAO,aAAa,EAAE;YAC7F,SAAS,CAAC,8BAA8B,EAAE,OAAO,aAAa,CAAC,6BAA6B,EAAE,KAAK,YAAY,CAAC,QAAQ,CAAC;YACzH;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,8BAA8B;YAC9B,MAAM,iHAAA,CAAA,UAAO,CAAC,YAAY,CACxB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ;YAGF,MAAM,UAAU,mBAAmB,iBAC/B,CAAC,+BAA+B,EAAE,KAAK,OAAO,EAAE,GAChD,CAAC,OAAO,EAAE,eAAe,mBAAmB,EAAE,KAAK,OAAO,EAAE;YAEhE,UAAU;YACV;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,kBAAkB;YAClB,SAAS;YACT;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACqB,KAAK,OAAO;;;;;;;;;;;;;8BAItD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAiB;4CAAE,KAAK,OAAO;;;;;;;kDAC5C,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAmB;4CAAE,KAAK,SAAS;;;;;;;kDAChD,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAoB;4CAAE,KAAK,UAAU;;;;;;;kDAClD,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAsB;4CAAE,KAAK,YAAY,IAAI;4CAAE;;;;;;;kDAC5D,8OAAC;;0DAAI,8OAAC;0DAAO;;;;;;4CAAwB;4CAAE,KAAK,SAAS,IAAI;;;;;;;;;;;;;;;;;;sCAK7D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAS;;;;;;gCACvB,8BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;yDAG5B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAgB,eAAe;;sDAC5C,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,OAAO,GAAG,CAAC,CAAC,uBACX,8OAAC,kIAAA,CAAA,aAAU;oDAET,OAAO,OAAO,SAAS;oDACvB,UAAU,OAAO,SAAS,KAAK,KAAK,SAAS;8DAE7C,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAM,OAAO,SAAS;;;;;;oEACtB,OAAO,SAAS,KAAK,KAAK,SAAS,kBAClC,8OAAC;wEAAK,WAAU;kFAAiD;;;;;;;;;;;;4DAKpE,OAAO,SAAS,KAAK,gCACpB,8OAAC;gEAAK,WAAU;;oEACb,OAAO,aAAa;oEAAC;oEACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,OAAO,SAAS,EAAE;;;;;;;;;;;;;mDAhB9C,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;wBA4BhC,mBAAmB,gCAClB,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;wBAQrB,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;;;;;;;8BAKzB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,kBAAkB,mBAAmB,KAAK,SAAS;;gCAExE,yBAAW,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAC9B,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 4191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CollegamentiDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Zap, CheckCircle } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { caviApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CollegamentiDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CollegamentiDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: CollegamentiDialogProps) {\n  const { cantiere } = useAuth()\n  const [selectedResponsabile, setSelectedResponsabile] = useState('')\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      setSelectedResponsabile('')\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, cavo])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      console.error('Errore nel caricamento responsabili:', error)\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const getStatoCollegamento = () => {\n    if (!cavo) return { stato: 'non_collegato', descrizione: 'Non collegato' }\n    \n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n    \n    switch (collegamento) {\n      case 1:\n        return { stato: 'partenza', descrizione: '🟢⚪ Collegato lato partenza' }\n      case 2:\n        return { stato: 'arrivo', descrizione: '⚪🟢 Collegato lato arrivo' }\n      case 3:\n        return { stato: 'completo', descrizione: '🟢🟢 Completamente collegato' }\n      default:\n        return { stato: 'non_collegato', descrizione: '⚪⚪ Non collegato' }\n    }\n  }\n\n  const handleCollegaPartenza = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'partenza',\n        selectedResponsabile\n      )\n\n      onSuccess(`Collegamento lato partenza completato per il cavo ${cavo.id_cavo}`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nel collegamento:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCollegaArrivo = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      await caviApi.collegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        'arrivo',\n        selectedResponsabile\n      )\n\n      onSuccess(`Collegamento lato arrivo completato per il cavo ${cavo.id_cavo}`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nel collegamento:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleScollega = async (lato?: 'partenza' | 'arrivo') => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      await caviApi.scollegaCavo(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        lato\n      )\n\n      const latoText = lato ? ` lato ${lato}` : ''\n      onSuccess(`Scollegamento${latoText} completato per il cavo ${cavo.id_cavo}`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nello scollegamento:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante lo scollegamento'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  const statoCollegamento = getStatoCollegamento()\n  const isInstalled = (cavo.metri_posati || cavo.metratura_reale || 0) > 0\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Zap className=\"h-5 w-5\" />\n            Gestione Collegamenti - {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription>\n            Gestisci i collegamenti del cavo {cavo.id_cavo}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Stato attuale */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Stato Attuale</Label>\n            <div className=\"mt-1 text-lg font-semibold\">\n              {statoCollegamento.descrizione}\n            </div>\n          </div>\n\n          {!isInstalled && (\n            <Alert>\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Il cavo deve essere installato prima di poter essere collegato.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {isInstalled && (\n            <>\n              {/* Selezione responsabile */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"responsabile\">Responsabile Collegamento</Label>\n                <Select\n                  value={selectedResponsabile}\n                  onValueChange={setSelectedResponsabile}\n                  disabled={loadingResponsabili}\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Seleziona responsabile...\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {responsabili.map((resp) => (\n                      <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                        {resp.nome_responsabile}\n                        {resp.numero_telefono && ` - ${resp.numero_telefono}`}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Azioni di collegamento */}\n              <div className=\"space-y-3\">\n                <Label className=\"text-sm font-medium\">Azioni Disponibili</Label>\n                \n                <div className=\"grid grid-cols-2 gap-2\">\n                  {statoCollegamento.stato !== 'partenza' && statoCollegamento.stato !== 'completo' && (\n                    <Button\n                      onClick={handleCollegaPartenza}\n                      disabled={loading || !selectedResponsabile}\n                      className=\"w-full\"\n                    >\n                      {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Zap className=\"h-4 w-4 mr-2\" />}\n                      Collega Partenza\n                    </Button>\n                  )}\n\n                  {statoCollegamento.stato !== 'arrivo' && statoCollegamento.stato !== 'completo' && (\n                    <Button\n                      onClick={handleCollegaArrivo}\n                      disabled={loading || !selectedResponsabile}\n                      className=\"w-full\"\n                    >\n                      {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Zap className=\"h-4 w-4 mr-2\" />}\n                      Collega Arrivo\n                    </Button>\n                  )}\n                </div>\n\n                {statoCollegamento.stato !== 'non_collegato' && (\n                  <div className=\"space-y-2\">\n                    <Button\n                      onClick={() => handleScollega()}\n                      disabled={loading}\n                      variant=\"destructive\"\n                      className=\"w-full\"\n                    >\n                      {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <AlertCircle className=\"h-4 w-4 mr-2\" />}\n                      Scollega Completamente\n                    </Button>\n                  </div>\n                )}\n              </div>\n            </>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Chiudi\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAEA;AACA;AAxBA;;;;;;;;;;;AAyCe,SAAS,mBAAmB,EACzC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACiB;IACxB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,wBAAwB;YACxB,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO;YAAiB,aAAa;QAAgB;QAEzE,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAY,aAAa;gBAA8B;YACzE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAU,aAAa;gBAA4B;YACrE,KAAK;gBACH,OAAO;oBAAE,OAAO;oBAAY,aAAa;gBAA+B;YAC1E;gBACE,OAAO;oBAAE,OAAO;oBAAiB,aAAa;gBAAmB;QACrE;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,YACA;YAGF,UAAU,CAAC,kDAAkD,EAAE,KAAK,OAAO,EAAE;YAC7E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,iHAAA,CAAA,UAAO,CAAC,WAAW,CACvB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,UACA;YAGF,UAAU,CAAC,gDAAgD,EAAE,KAAK,OAAO,EAAE;YAC3E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,iHAAA,CAAA,UAAO,CAAC,YAAY,CACxB,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ;YAGF,MAAM,WAAW,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG;YAC1C,UAAU,CAAC,aAAa,EAAE,SAAS,wBAAwB,EAAE,KAAK,OAAO,EAAE;YAC3E;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,oBAAoB;IAC1B,MAAM,cAAc,CAAC,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI,CAAC,IAAI;IAEvE,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;gCACF,KAAK,OAAO;;;;;;;sCAEvC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACiB,KAAK,OAAO;;;;;;;;;;;;;8BAIlD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,WAAW;;;;;;;;;;;;wBAIjC,CAAC,6BACA,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;wBAMrB,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAItB,6BACC;;8CAEE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe;4CACf,UAAU;;8DAEV,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;8DACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;4DAAe,OAAO,KAAK,iBAAiB;;gEACpD,KAAK,iBAAiB;gEACtB,KAAK,eAAe,IAAI,CAAC,GAAG,EAAE,KAAK,eAAe,EAAE;;2DAFtC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;8CAUhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;;gDACZ,kBAAkB,KAAK,KAAK,cAAc,kBAAkB,KAAK,KAAK,4BACrE,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,WAAW,CAAC;oDACtB,WAAU;;wDAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAAiC,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAkB;;;;;;;gDAKnG,kBAAkB,KAAK,KAAK,YAAY,kBAAkB,KAAK,KAAK,4BACnE,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,WAAW,CAAC;oDACtB,WAAU;;wDAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAAiC,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAkB;;;;;;;;;;;;;wCAMrG,kBAAkB,KAAK,KAAK,iCAC3B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM;gDACf,UAAU;gDACV,SAAQ;gDACR,WAAU;;oDAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAAiC,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUtH,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAS,UAAU;kCAAS;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}, {"offset": {"line": 4655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CertificazioneDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Award, FileText, Download } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { certificazioniApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CertificazioneDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CertificazioneDialog({\n  open,\n  onClose,\n  cavo,\n  onSuccess,\n  onError\n}: CertificazioneDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState({\n    responsabile_certificazione: '',\n    data_certificazione: new Date().toISOString().split('T')[0],\n    esito_certificazione: 'CONFORME',\n    note_certificazione: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        responsabile_certificazione: '',\n        data_certificazione: new Date().toISOString().split('T')[0],\n        esito_certificazione: 'CONFORME',\n        note_certificazione: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, cavo])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      console.error('Errore nel caricamento responsabili:', error)\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const isCavoCollegato = () => {\n    if (!cavo) return false\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n    return collegamento === 3 // Completamente collegato\n  }\n\n  const isCavoCertificato = () => {\n    if (!cavo) return false\n    return cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n  }\n\n  const handleCertifica = async () => {\n    if (!cavo || !cantiere) return\n\n    if (!formData.responsabile_certificazione) {\n      setError('Seleziona un responsabile per la certificazione')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      const certificazioneData = {\n        id_cavo: cavo.id_cavo,\n        responsabile_certificazione: formData.responsabile_certificazione,\n        data_certificazione: formData.data_certificazione,\n        esito_certificazione: formData.esito_certificazione,\n        note_certificazione: formData.note_certificazione || null\n      }\n\n      await certificazioniApi.createCertificazione(cantiere.id_cantiere, certificazioneData)\n\n      onSuccess(`Certificazione completata per il cavo ${cavo.id_cavo}`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nella certificazione:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la certificazione'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleGeneraPDF = async () => {\n    if (!cavo || !cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      const response = await certificazioniApi.generatePDF(cantiere.id_cantiere, cavo.id_cavo)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `certificato_${cavo.id_cavo}.pdf`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess(`PDF certificato generato per il cavo ${cavo.id_cavo}`)\n    } catch (error: any) {\n      console.error('Errore nella generazione PDF:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la generazione del PDF'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  const isInstalled = (cavo.metri_posati || cavo.metratura_reale || 0) > 0\n  const isCollegato = isCavoCollegato()\n  const isCertificato = isCavoCertificato()\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Award className=\"h-5 w-5\" />\n            Gestione Certificazione - {cavo.id_cavo}\n          </DialogTitle>\n          <DialogDescription>\n            Certifica il cavo {cavo.id_cavo} o genera il PDF del certificato\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Stato attuale */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Stato Cavo</Label>\n            <div className=\"mt-2 space-y-1\">\n              <div className=\"flex items-center gap-2\">\n                <span className={`w-3 h-3 rounded-full ${isInstalled ? 'bg-green-500' : 'bg-red-500'}`}></span>\n                <span className=\"text-sm\">{isInstalled ? 'Installato' : 'Non installato'}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className={`w-3 h-3 rounded-full ${isCollegato ? 'bg-green-500' : 'bg-red-500'}`}></span>\n                <span className=\"text-sm\">{isCollegato ? 'Collegato' : 'Non collegato'}</span>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <span className={`w-3 h-3 rounded-full ${isCertificato ? 'bg-green-500' : 'bg-red-500'}`}></span>\n                <span className=\"text-sm\">{isCertificato ? 'Certificato' : 'Non certificato'}</span>\n              </div>\n            </div>\n          </div>\n\n          {!isInstalled && (\n            <Alert>\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Il cavo deve essere installato prima di poter essere certificato.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {!isCollegato && isInstalled && (\n            <Alert>\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Il cavo deve essere completamente collegato prima di poter essere certificato.\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {isCertificato ? (\n            // Cavo già certificato - mostra opzione per generare PDF\n            <div className=\"space-y-4\">\n              <Alert>\n                <Award className=\"h-4 w-4\" />\n                <AlertDescription>\n                  Questo cavo è già stato certificato. Puoi generare il PDF del certificato.\n                </AlertDescription>\n              </Alert>\n\n              <Button\n                onClick={handleGeneraPDF}\n                disabled={loading}\n                className=\"w-full\"\n              >\n                {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n                Genera PDF Certificato\n              </Button>\n            </div>\n          ) : (\n            // Cavo non certificato - mostra form per certificazione\n            isInstalled && isCollegato && (\n              <div className=\"space-y-4\">\n                {/* Responsabile */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"responsabile\">Responsabile Certificazione *</Label>\n                  <Select\n                    value={formData.responsabile_certificazione}\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile_certificazione: value }))}\n                    disabled={loadingResponsabili}\n                  >\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"Seleziona responsabile...\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {responsabili.map((resp) => (\n                        <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                          {resp.nome_responsabile}\n                          {resp.numero_telefono && ` - ${resp.numero_telefono}`}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                {/* Data certificazione */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"data\">Data Certificazione</Label>\n                  <Input\n                    id=\"data\"\n                    type=\"date\"\n                    value={formData.data_certificazione}\n                    onChange={(e) => setFormData(prev => ({ ...prev, data_certificazione: e.target.value }))}\n                  />\n                </div>\n\n                {/* Esito */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"esito\">Esito Certificazione</Label>\n                  <Select\n                    value={formData.esito_certificazione}\n                    onValueChange={(value) => setFormData(prev => ({ ...prev, esito_certificazione: value }))}\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"CONFORME\">CONFORME</SelectItem>\n                      <SelectItem value=\"NON_CONFORME\">NON CONFORME</SelectItem>\n                      <SelectItem value=\"CONFORME_CON_RISERVA\">CONFORME CON RISERVA</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n\n                {/* Note */}\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"note\">Note (opzionale)</Label>\n                  <Textarea\n                    id=\"note\"\n                    placeholder=\"Inserisci eventuali note sulla certificazione...\"\n                    value={formData.note_certificazione}\n                    onChange={(e) => setFormData(prev => ({ ...prev, note_certificazione: e.target.value }))}\n                    rows={3}\n                  />\n                </div>\n\n                <Button\n                  onClick={handleCertifica}\n                  disabled={loading || !formData.responsabile_certificazione}\n                  className=\"w-full\"\n                >\n                  {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Award className=\"h-4 w-4 mr-2\" />}\n                  Certifica Cavo\n                </Button>\n              </div>\n            )\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Chiudi\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AA1BA;;;;;;;;;;;;;AA2Ce,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,6BAA6B;QAC7B,qBAAqB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3D,sBAAsB;QACtB,qBAAqB;IACvB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,YAAY;gBACV,6BAA6B;gBAC7B,qBAAqB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3D,sBAAsB;gBACtB,qBAAqB;YACvB;YACA,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAC/D,OAAO,iBAAiB,EAAE,0BAA0B;;IACtD;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;IACxF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,CAAC,SAAS,2BAA2B,EAAE;YACzC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,qBAAqB;gBACzB,SAAS,KAAK,OAAO;gBACrB,6BAA6B,SAAS,2BAA2B;gBACjE,qBAAqB,SAAS,mBAAmB;gBACjD,sBAAsB,SAAS,oBAAoB;gBACnD,qBAAqB,SAAS,mBAAmB,IAAI;YACvD;YAEA,MAAM,iHAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC,SAAS,WAAW,EAAE;YAEnE,UAAU,CAAC,sCAAsC,EAAE,KAAK,OAAO,EAAE;YACjE;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,iHAAA,CAAA,oBAAiB,CAAC,WAAW,CAAC,SAAS,WAAW,EAAE,KAAK,OAAO;YAEvF,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC;YAC/D,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU,CAAC,qCAAqC,EAAE,KAAK,OAAO,EAAE;QAClE,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,cAAc,CAAC,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI,CAAC,IAAI;IACvE,MAAM,cAAc;IACpB,MAAM,gBAAgB;IAEtB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;gCACF,KAAK,OAAO;;;;;;;sCAEzC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACE,KAAK,OAAO;gCAAC;;;;;;;;;;;;;8BAIpC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,qBAAqB,EAAE,cAAc,iBAAiB,cAAc;;;;;;8DACtF,8OAAC;oDAAK,WAAU;8DAAW,cAAc,eAAe;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,qBAAqB,EAAE,cAAc,iBAAiB,cAAc;;;;;;8DACtF,8OAAC;oDAAK,WAAU;8DAAW,cAAc,cAAc;;;;;;;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAW,CAAC,qBAAqB,EAAE,gBAAgB,iBAAiB,cAAc;;;;;;8DACxF,8OAAC;oDAAK,WAAU;8DAAW,gBAAgB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;wBAKhE,CAAC,6BACA,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;wBAMrB,CAAC,eAAe,6BACf,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;wBAMrB,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAItB,gBACC,yDAAyD;sCACzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;;sDACJ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC,iIAAA,CAAA,mBAAgB;sDAAC;;;;;;;;;;;;8CAKpB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;wCAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAiC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAkB;;;;;;;;;;;;mCAKzG,wDAAwD;wBACxD,eAAe,6BACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,SAAS,2BAA2B;4CAC3C,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,6BAA6B;oDAAM,CAAC;4CAC9F,UAAU;;8DAEV,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;8DACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;4DAAe,OAAO,KAAK,iBAAiB;;gEACpD,KAAK,iBAAiB;gEACtB,KAAK,eAAe,IAAI,CAAC,GAAG,EAAE,KAAK,eAAe,EAAE;;2DAFtC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;8CAUhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,mBAAmB;4CACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;;;;;;;;;;;;8CAK1F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,SAAS,oBAAoB;4CACpC,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,sBAAsB;oDAAM,CAAC;;8DAEvF,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAe;;;;;;sEACjC,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAuB;;;;;;;;;;;;;;;;;;;;;;;;8CAM/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO,SAAS,mBAAmB;4CACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACtF,MAAM;;;;;;;;;;;;8CAIV,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,WAAW,CAAC,SAAS,2BAA2B;oCAC1D,WAAU;;wCAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAiC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAkB;;;;;;;;;;;;;;;;;;;8BAQ5G,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAS,UAAU;kCAAS;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}, {"offset": {"line": 5359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CreaComandaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, ClipboardList, Users } from 'lucide-react'\nimport { comandeApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CreaComandaDialogProps {\n  open: boolean\n  onClose: () => void\n  caviSelezionati: string[]\n  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CreaComandaDialog({\n  open,\n  onClose,\n  caviSelezionati,\n  tipoComanda,\n  onSuccess,\n  onError\n}: CreaComandaDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState({\n    tipo_comanda: tipoComanda || 'POSA',\n    responsabile: '',\n    note: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open) {\n      setFormData({\n        tipo_comanda: tipoComanda || 'POSA',\n        responsabile: '',\n        note: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, tipoComanda])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      console.error('Errore nel caricamento responsabili:', error)\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const getTipoComandaLabel = (tipo: string) => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa Cavi'\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza'\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo'\n      case 'CERTIFICAZIONE':\n        return 'Certificazione'\n      default:\n        return tipo\n    }\n  }\n\n  const handleCreaComanda = async () => {\n    if (!cantiere) return\n\n    if (!formData.responsabile) {\n      setError('Seleziona un responsabile per la comanda')\n      return\n    }\n\n    if (caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo per la comanda')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Crea la comanda con i cavi assegnati\n      const comandaData = {\n        tipo_comanda: formData.tipo_comanda,\n        responsabile: formData.responsabile,\n        note: formData.note || null\n      }\n\n      const response = await comandeApi.createComandaWithCavi(\n        cantiere.id_cantiere,\n        comandaData,\n        caviSelezionati\n      )\n\n      onSuccess(`Comanda ${response.data.codice_comanda} creata con successo per ${caviSelezionati.length} cavi`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nella creazione comanda:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <ClipboardList className=\"h-5 w-5\" />\n            Crea Nuova Comanda\n          </DialogTitle>\n          <DialogDescription>\n            Crea una nuova comanda per {caviSelezionati.length} cavi selezionati\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Cavi selezionati */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Cavi Selezionati ({caviSelezionati.length})</Label>\n            <div className=\"mt-2 max-h-32 overflow-y-auto\">\n              <div className=\"flex flex-wrap gap-1\">\n                {caviSelezionati.slice(0, 10).map((cavoId) => (\n                  <span\n                    key={cavoId}\n                    className=\"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\"\n                  >\n                    {cavoId}\n                  </span>\n                ))}\n                {caviSelezionati.length > 10 && (\n                  <span className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\">\n                    +{caviSelezionati.length - 10} altri...\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Tipo comanda */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"tipo\">Tipo Comanda *</Label>\n            <Select\n              value={formData.tipo_comanda}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"POSA\">🔧 Posa Cavi</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_PARTENZA\">🔌 Collegamento Partenza</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_ARRIVO\">⚡ Collegamento Arrivo</SelectItem>\n                <SelectItem value=\"CERTIFICAZIONE\">📋 Certificazione</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Responsabile */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\">Responsabile *</Label>\n            <Select\n              value={formData.responsabile}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}\n              disabled={loadingResponsabili}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona responsabile...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {responsabili.map((resp) => (\n                  <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4\" />\n                      <span>{resp.nome_responsabile}</span>\n                      {resp.numero_telefono && (\n                        <span className=\"text-xs text-gray-500\">- {resp.numero_telefono}</span>\n                      )}\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Note */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\">Note (opzionale)</Label>\n            <Textarea\n              id=\"note\"\n              placeholder=\"Inserisci eventuali note per la comanda...\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              rows={3}\n            />\n          </div>\n\n          {/* Riepilogo */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Riepilogo Comanda</Label>\n            <div className=\"mt-2 space-y-1 text-sm\">\n              <div><strong>Tipo:</strong> {getTipoComandaLabel(formData.tipo_comanda)}</div>\n              <div><strong>Responsabile:</strong> {formData.responsabile || 'Non selezionato'}</div>\n              <div><strong>Cavi:</strong> {caviSelezionati.length} selezionati</div>\n              {formData.note && <div><strong>Note:</strong> {formData.note}</div>}\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleCreaComanda}\n            disabled={loading || !formData.responsabile || caviSelezionati.length === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <ClipboardList className=\"h-4 w-4 mr-2\" />}\n            Crea Comanda\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAEA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAzBA;;;;;;;;;;;;AA2Ce,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,eAAe,EACf,WAAW,EACX,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc,eAAe;QAC7B,cAAc;QACd,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,cAAc,eAAe;gBAC7B,cAAc;gBACd,MAAM;YACR;YACA,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,SAAS;YACT;QACF;QAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,uCAAuC;YACvC,MAAM,cAAc;gBAClB,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY;gBACnC,MAAM,SAAS,IAAI,IAAI;YACzB;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,qBAAqB,CACrD,SAAS,WAAW,EACpB,aACA;YAGF,UAAU,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC;YAC1G;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACW,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;;8BAIvD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;wCAAsB;wCAAmB,gBAAgB,MAAM;wCAAC;;;;;;;8CACjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBACjC,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;4CAMR,gBAAgB,MAAM,GAAG,oBACxB,8OAAC;gDAAK,WAAU;;oDAAmE;oDAC/E,gBAAgB,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;wBAOvC,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;;sDAE/E,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAwB;;;;;;8DAC1C,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAsB;;;;;;8DACxC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;oCAC/E,UAAU;;sDAEV,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;oDAAe,OAAO,KAAK,iBAAiB;8DACrD,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,KAAK,iBAAiB;;;;;;4DAC5B,KAAK,eAAe,kBACnB,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAG,KAAK,eAAe;;;;;;;;;;;;;mDALpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAehC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,MAAM;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,oBAAoB,SAAS,YAAY;;;;;;;sDACtE,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,SAAS,YAAY,IAAI;;;;;;;sDAC9D,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,gBAAgB,MAAM;gDAAC;;;;;;;wCACnD,SAAS,IAAI,kBAAI,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;8BAKlE,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,SAAS,YAAY,IAAI,gBAAgB,MAAM,KAAK;;gCAEzE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAOtH", "debugId": null}}, {"offset": {"line": 5960, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ImportExcelDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  DialogDescription,\n  DialogFooter,\n  <PERSON>alogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Upload, FileSpreadsheet, CheckCircle } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ImportExcelDialogProps {\n  open: boolean\n  onClose: () => void\n  tipo: 'cavi' | 'bobine'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ImportExcelDialog({\n  open,\n  onClose,\n  tipo,\n  onSuccess,\n  onError\n}: ImportExcelDialogProps) {\n  const { cantiere } = useAuth()\n  const [file, setFile] = useState<File | null>(null)\n  const [revisione, setRevisione] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = event.target.files?.[0]\n    if (selectedFile) {\n      // Verifica che sia un file Excel\n      const validTypes = [\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.ms-excel'\n      ]\n      \n      if (!validTypes.includes(selectedFile.type) && \n          !selectedFile.name.toLowerCase().endsWith('.xlsx') && \n          !selectedFile.name.toLowerCase().endsWith('.xls')) {\n        setError('Seleziona un file Excel valido (.xlsx o .xls)')\n        return\n      }\n\n      setFile(selectedFile)\n      setError('')\n    }\n  }\n\n  const handleImport = async () => {\n    if (!file || !cantiere) return\n\n    if (tipo === 'cavi' && !revisione.trim()) {\n      setError('Inserisci il codice revisione per l\\'importazione cavi')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n      setUploadProgress(0)\n\n      let response\n      if (tipo === 'cavi') {\n        response = await excelApi.importCavi(cantiere.id_cantiere, file, revisione.trim())\n      } else {\n        response = await excelApi.importBobine(cantiere.id_cantiere, file)\n      }\n\n      setUploadProgress(100)\n\n      if (response.data.success) {\n        const details = response.data.details\n        let message = response.data.message\n        \n        if (tipo === 'cavi' && details?.cavi_importati) {\n          message += ` (${details.cavi_importati} cavi importati)`\n        } else if (tipo === 'bobine' && details?.bobine_importate) {\n          message += ` (${details.bobine_importate} bobine importate)`\n        }\n\n        onSuccess(message)\n        onClose()\n      } else {\n        onError(response.data.message || 'Errore durante l\\'importazione')\n      }\n    } catch (error: any) {\n      console.error('Errore nell\\'importazione:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'importazione del file'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n      setUploadProgress(0)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFile(null)\n      setRevisione('')\n      setError('')\n      setUploadProgress(0)\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n      onClose()\n    }\n  }\n\n  const getTipoLabel = () => {\n    return tipo === 'cavi' ? 'Cavi' : 'Bobine'\n  }\n\n  const getFileRequirements = () => {\n    if (tipo === 'cavi') {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'Codice revisione obbligatorio per tracciabilità'\n      ]\n    } else {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'I metri residui saranno impostati uguali ai metri totali'\n      ]\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Importa {getTipoLabel()} da Excel\n          </DialogTitle>\n          <DialogDescription>\n            Carica un file Excel per importare {getTipoLabel().toLowerCase()} nel cantiere\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Requisiti file */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Requisiti File</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              {getFileRequirements().map((req, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-500 mt-0.5\">•</span>\n                  <span>{req}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Selezione file */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"file\">File Excel *</Label>\n            <div className=\"flex items-center gap-2\">\n              <Input\n                ref={fileInputRef}\n                id=\"file\"\n                type=\"file\"\n                accept=\".xlsx,.xls\"\n                onChange={handleFileSelect}\n                disabled={loading}\n                className=\"flex-1\"\n              />\n              {file && (\n                <div className=\"flex items-center gap-1 text-green-600\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">File selezionato</span>\n                </div>\n              )}\n            </div>\n            {file && (\n              <div className=\"text-sm text-gray-600\">\n                <FileSpreadsheet className=\"h-4 w-4 inline mr-1\" />\n                {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)\n              </div>\n            )}\n          </div>\n\n          {/* Revisione (solo per cavi) */}\n          {tipo === 'cavi' && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"revisione\">Codice Revisione *</Label>\n              <Input\n                id=\"revisione\"\n                value={revisione}\n                onChange={(e) => setRevisione(e.target.value)}\n                placeholder=\"es. REV001, V1.0, 2024-01\"\n                disabled={loading}\n              />\n              <p className=\"text-sm text-gray-500\">\n                Codice identificativo della revisione per tracciabilità delle modifiche\n              </p>\n            </div>\n          )}\n\n          {/* Progress bar */}\n          {loading && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento in corso...</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${uploadProgress}%` }}\n                ></div>\n              </div>\n            </div>\n          )}\n\n          {/* Riepilogo */}\n          {file && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Importazione</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Tipo:</strong> {getTipoLabel()}</div>\n                <div><strong>File:</strong> {file.name}</div>\n                <div><strong>Dimensione:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</div>\n                {tipo === 'cavi' && revisione && (\n                  <div><strong>Revisione:</strong> {revisione}</div>\n                )}\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleImport}\n            disabled={loading || !file || (tipo === 'cavi' && !revisione.trim())}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Upload className=\"h-4 w-4 mr-2\" />}\n            Importa {getTipoLabel()}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA2Be,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAC5C,IAAI,cAAc;YAChB,iCAAiC;YACjC,MAAM,aAAa;gBACjB;gBACA;aACD;YAED,IAAI,CAAC,WAAW,QAAQ,CAAC,aAAa,IAAI,KACtC,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC1C,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACrD,SAAS;gBACT;YACF;YAEA,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,SAAS,UAAU,CAAC,UAAU,IAAI,IAAI;YACxC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,kBAAkB;YAElB,IAAI;YACJ,IAAI,SAAS,QAAQ;gBACnB,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW,EAAE,MAAM,UAAU,IAAI;YACjF,OAAO;gBACL,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE;YAC/D;YAEA,kBAAkB;YAElB,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO;gBACrC,IAAI,UAAU,SAAS,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,UAAU,SAAS,gBAAgB;oBAC9C,WAAW,CAAC,EAAE,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,IAAI,SAAS,YAAY,SAAS,kBAAkB;oBACzD,WAAW,CAAC,EAAE,EAAE,QAAQ,gBAAgB,CAAC,kBAAkB,CAAC;gBAC9D;gBAEA,UAAU;gBACV;YACF,OAAO;gBACL,QAAQ,SAAS,IAAI,CAAC,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;YACX,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;YACA;QACF;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,SAAS,SAAS,SAAS;IACpC;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS,QAAQ;YACnB,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH,OAAO;YACL,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCACrB;gCAAe;;;;;;;sCAE1B,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACmB,eAAe,WAAW;gCAAG;;;;;;;;;;;;;8BAIrE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;8CACX,sBAAsB,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;4CAAe,WAAU;;8DACxB,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;;;;;;;wBAQd,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,IAAG;4CACH,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;;;;;;wCAEX,sBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI/B,sBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAC1B,KAAK,IAAI;wCAAC;wCAAG,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAMxD,SAAS,wBACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,aAAY;oCACZ,UAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAOxC,yBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,eAAe,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;wBAO5C,sBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE;;;;;;;sDAC7B,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,KAAK,IAAI;;;;;;;sDACtC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;wCACvE,SAAS,UAAU,2BAClB,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE;;;;;;;sDAEpC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,QAAS,SAAS,UAAU,CAAC,UAAU,IAAI;;gCAEhE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAkB;gCAC1F;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 6549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ExportDataDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Download, FileSpreadsheet, Database } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ExportDataDialogProps {\n  open: boolean\n  onClose: () => void\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ExportDataDialog({\n  open,\n  onClose,\n  onSuccess,\n  onError\n}: ExportDataDialogProps) {\n  const { cantiere } = useAuth()\n  const [selectedExports, setSelectedExports] = useState({\n    cavi: true,\n    bobine: true,\n    comande: false,\n    certificazioni: false,\n    responsabili: false\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleExportChange = (exportType: string, checked: boolean) => {\n    setSelectedExports(prev => ({\n      ...prev,\n      [exportType]: checked\n    }))\n  }\n\n  const handleExportCavi = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportCavi(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `cavi_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export cavi completato con successo')\n    } catch (error: any) {\n      console.error('Errore nell\\'export cavi:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei cavi'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportBobine = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportBobine(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `bobine_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export bobine completato con successo')\n    } catch (error: any) {\n      console.error('Errore nell\\'export bobine:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export delle bobine'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportAll = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      const exports = []\n\n      if (selectedExports.cavi) {\n        exports.push(handleExportCavi())\n      }\n\n      if (selectedExports.bobine) {\n        exports.push(handleExportBobine())\n      }\n\n      // TODO: Implementare export per comande, certificazioni, responsabili\n      if (selectedExports.comande) {\n        console.log('Export comande - da implementare')\n      }\n\n      if (selectedExports.certificazioni) {\n        console.log('Export certificazioni - da implementare')\n      }\n\n      if (selectedExports.responsabili) {\n        console.log('Export responsabili - da implementare')\n      }\n\n      await Promise.all(exports)\n\n      const exportCount = Object.values(selectedExports).filter(Boolean).length\n      onSuccess(`Export completato: ${exportCount} file scaricati`)\n      onClose()\n    } catch (error: any) {\n      console.error('Errore nell\\'export:', error)\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei dati'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getSelectedCount = () => {\n    return Object.values(selectedExports).filter(Boolean).length\n  }\n\n  const exportOptions = [\n    {\n      key: 'cavi',\n      label: 'Cavi',\n      description: 'Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni',\n      icon: <Database className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'bobine',\n      label: 'Bobine',\n      description: 'Esporta tutte le bobine del parco cavi con metri residui e assegnazioni',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'comande',\n      label: 'Comande',\n      description: 'Esporta tutte le comande con cavi assegnati e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'certificazioni',\n      label: 'Certificazioni',\n      description: 'Esporta tutte le certificazioni con esiti e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'responsabili',\n      label: 'Responsabili',\n      description: 'Esporta tutti i responsabili con contatti e ruoli',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    }\n  ]\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Download className=\"h-5 w-5\" />\n            Esporta Dati Cantiere\n          </DialogTitle>\n          <DialogDescription>\n            Seleziona i dati da esportare dal cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Opzioni di export */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Seleziona Dati da Esportare</Label>\n            \n            {exportOptions.map((option) => (\n              <div\n                key={option.key}\n                className={`flex items-start space-x-3 p-3 rounded-lg border ${\n                  option.available ? 'bg-white' : 'bg-gray-50'\n                }`}\n              >\n                <Checkbox\n                  id={option.key}\n                  checked={selectedExports[option.key as keyof typeof selectedExports]}\n                  onCheckedChange={(checked) => handleExportChange(option.key, checked as boolean)}\n                  disabled={!option.available || loading}\n                />\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2\">\n                    {option.icon}\n                    <Label\n                      htmlFor={option.key}\n                      className={`font-medium ${!option.available ? 'text-gray-500' : ''}`}\n                    >\n                      {option.label}\n                      {!option.available && (\n                        <span className=\"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\">\n                          In sviluppo\n                        </span>\n                      )}\n                    </Label>\n                  </div>\n                  <p className={`text-sm mt-1 ${!option.available ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {option.description}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Informazioni export */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Informazioni Export</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              <li>• I file saranno scaricati in formato Excel (.xlsx)</li>\n              <li>• I nomi file includeranno data e nome cantiere</li>\n              <li>• I dati esportati riflettono lo stato attuale del database</li>\n              <li>• L'export non modifica i dati originali</li>\n            </ul>\n          </div>\n\n          {/* Riepilogo */}\n          {getSelectedCount() > 0 && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Export</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n                <div><strong>File da scaricare:</strong> {getSelectedCount()}</div>\n                <div><strong>Data export:</strong> {new Date().toLocaleDateString('it-IT')}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleExportAll}\n            disabled={loading || getSelectedCount() === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n            Esporta {getSelectedCount() > 0 ? `(${getSelectedCount()})` : ''}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA0Be,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACe;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,MAAM;QACN,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW;YAE/D,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW;YAEjE,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC/G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,UAAU,EAAE;YAElB,IAAI,gBAAgB,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,IAAI,gBAAgB,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC;YACf;YAEA,sEAAsE;YACtE,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,gBAAgB,cAAc,EAAE;gBAClC,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,gBAAgB,YAAY,EAAE;gBAChC,QAAQ,GAAG,CAAC;YACd;YAEA,MAAM,QAAQ,GAAG,CAAC;YAElB,MAAM,cAAc,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;YACzE,UAAU,CAAC,mBAAmB,EAAE,YAAY,eAAe,CAAC;YAC5D;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;IAC9D;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;KACD;IAED,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCAC2B,UAAU;;;;;;;;;;;;;8BAI1D,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;gCAEtC,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAEC,WAAW,CAAC,iDAAiD,EAC3D,OAAO,SAAS,GAAG,aAAa,cAChC;;0DAEF,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,OAAO,GAAG;gDACd,SAAS,eAAe,CAAC,OAAO,GAAG,CAAiC;gDACpE,iBAAiB,CAAC,UAAY,mBAAmB,OAAO,GAAG,EAAE;gDAC7D,UAAU,CAAC,OAAO,SAAS,IAAI;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,IAAI;0EACZ,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,OAAO,GAAG;gEACnB,WAAW,CAAC,YAAY,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,IAAI;;oEAEnE,OAAO,KAAK;oEACZ,CAAC,OAAO,SAAS,kBAChB,8OAAC;wEAAK,WAAU;kFAA2D;;;;;;;;;;;;;;;;;;kEAMjF,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,iBAAiB;kEAClF,OAAO,WAAW;;;;;;;;;;;;;uCA3BlB,OAAO,GAAG;;;;;;;;;;;sCAmCrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAKP,qBAAqB,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;sDAC3C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAA2B;gDAAE;;;;;;;sDAC1C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAqB;gDAAE,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8BAM1E,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,uBAAuB;;gCAE3C,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAkB;gCAC5F,qBAAqB,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;AAM1E", "debugId": null}}, {"offset": {"line": 7095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRout<PERSON> } from 'next/navigation'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { caviApi } from '@/lib/api'\nimport { Cavo } from '@/types'\nimport CaviTable from '@/components/cavi/CaviTable'\nimport CaviStatistics from '@/components/cavi/CaviStatistics'\nimport InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'\nimport ModificaBobinaDialog from '@/components/cavi/ModificaBobinaDialog'\nimport CollegamentiDialog from '@/components/cavi/CollegamentiDialog'\nimport CertificazioneDialog from '@/components/cavi/CertificazioneDialog'\nimport CreaComandaDialog from '@/components/cavi/CreaComandaDialog'\nimport ImportExcelDialog from '@/components/cavi/ImportExcelDialog'\nimport ExportDataDialog from '@/components/cavi/ExportDataDialog'\n// import { useToast } from '@/hooks/use-toast'\nimport {\n  Package,\n  AlertCircle,\n  Loader2\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totali: number\n  installati: number\n  collegati: number\n  certificati: number\n  percentualeInstallazione: number\n  percentualeCollegamento: number\n  percentualeCertificazione: number\n  metriTotali: number\n  metriInstallati: number\n  metriCollegati: number\n  metriCertificati: number\n}\n\nexport default function CaviPage() {\n  const { user, cantiere, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  // Sistema toast semplice\n  const toast = ({ title, description, variant }: { title: string, description: string, variant?: string }) => {\n    console.log(`${variant === 'destructive' ? '❌' : '✅'} ${title}: ${description}`)\n    // TODO: Implementare sistema toast visuale\n  }\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedCavi, setSelectedCavi] = useState<string[]>([])\n  const [selectionEnabled, setSelectionEnabled] = useState(false)\n  const [filteredCavi, setFilteredCavi] = useState<Cavo[]>([])\n  const [revisioneCorrente, setRevisioneCorrente] = useState<string>('')\n\n  // Update filtered cavi when main cavi change\n  useEffect(() => {\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Stati per i dialoghi\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [collegamentiDialog, setCollegamentiDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [certificazioneDialog, setCertificazioneDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [creaComandaDialog, setCreaComandaDialog] = useState<{\n    open: boolean\n    tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  }>({ open: false })\n\n  const [importExcelDialog, setImportExcelDialog] = useState<{\n    open: boolean\n    tipo?: 'cavi' | 'bobine'\n  }>({ open: false })\n\n  const [exportDataDialog, setExportDataDialog] = useState(false)\n  const [stats, setStats] = useState<DashboardStats>({\n    totali: 0,\n    installati: 0,\n    collegati: 0,\n    certificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriCollegati: 0,\n    metriCertificati: 0\n  })\n\n  // Get cantiere ID\n  const [cantiereId, setCantiereId] = useState<number>(0)\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')\n      setCantiereId(storedId)\n    }\n  }, [cantiere])\n\n  // Carica i cavi dal backend\n  useEffect(() => {\n    if (cantiereId && cantiereId > 0) {\n      loadCavi()\n      loadRevisioneCorrente()\n    }\n  }, [cantiereId])\n\n  const loadRevisioneCorrente = async () => {\n    try {\n      const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setRevisioneCorrente(data.revisione_corrente || '00')\n      } else {\n        console.warn('Impossibile recuperare la revisione corrente')\n        setRevisioneCorrente('00')\n      }\n    } catch (error) {\n      console.error('Errore nel recupero della revisione corrente:', error)\n      setRevisioneCorrente('00')\n    }\n  }\n\n  const loadCavi = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      console.log('🔍 Tentativo caricamento cavi per cantiere:', cantiereId)\n      console.log('🔍 Token presente:', !!localStorage.getItem('token'))\n\n      // Prima prova con l'API normale\n      try {\n        const data = await caviApi.getCavi(cantiereId)\n        console.log('✅ API normale riuscita, cavi ricevuti:', data?.length || 0)\n\n        // Separa cavi attivi e spare\n        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)\n        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)\n\n        setCavi(caviAttivi)\n        setCaviSpare(caviSpareFiltered)\n\n        // Calcola statistiche\n        calculateStats(caviAttivi)\n\n      } catch (apiError: any) {\n        console.log('❌ API normale fallita, provo endpoint debug...')\n        console.error('Errore API normale:', apiError)\n\n        // Fallback: prova con endpoint debug (senza autenticazione)\n        try {\n          const response = await fetch(`http://localhost:8001/api/cavi/debug/${cantiereId}`)\n          const debugData = await response.json()\n          console.log('✅ Endpoint debug riuscito:', debugData)\n\n          if (debugData.cavi && Array.isArray(debugData.cavi)) {\n            const caviAttivi = debugData.cavi.filter((cavo: any) => !cavo.spare)\n            const caviSpareFiltered = debugData.cavi.filter((cavo: any) => cavo.spare)\n\n            setCavi(caviAttivi)\n            setCaviSpare(caviSpareFiltered)\n            calculateStats(caviAttivi)\n\n            setError('⚠️ Dati caricati tramite endpoint debug (problema autenticazione)')\n          } else {\n            throw new Error('Formato dati debug non valido')\n          }\n        } catch (debugError) {\n          console.error('❌ Anche endpoint debug fallito:', debugError)\n          throw apiError // Rilancia l'errore originale\n        }\n      }\n\n    } catch (error: any) {\n      console.error('❌ Errore generale nel caricamento cavi:', error)\n      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateStats = (caviData: Cavo[]) => {\n    const totali = caviData.length\n    const installati = caviData.filter(c => (c.metri_posati || c.metratura_reale || 0) > 0).length\n    const collegati = caviData.filter(c => (c.collegamento || c.collegamenti) === 3).length // 3 = collegato\n    const certificati = caviData.filter(c => c.certificato).length\n\n    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n\n    setStats({\n      totali,\n      installati,\n      collegati,\n      certificati,\n      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,\n      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,\n      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,\n      metriTotali,\n      metriInstallati,\n      metriCollegati,\n      metriCertificati\n    })\n  }\n\n  // Gestione azioni sui cavi\n  const handleStatusAction = (cavo: Cavo, action: string, label?: string) => {\n    console.log('Status action:', action, 'for cavo:', cavo.id_cavo, 'label:', label)\n\n    switch (action) {\n      case 'insert_meters':\n        setInserisciMetriDialog({ open: true, cavo })\n        break\n      case 'modify_reel':\n        setModificaBobinaDialog({ open: true, cavo })\n        break\n      case 'view_command':\n        toast({\n          title: \"Visualizza Comanda\",\n          description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'connect_cable':\n      case 'connect_arrival':\n      case 'connect_departure':\n      case 'disconnect_cable':\n      case 'manage_connections':\n        setCollegamentiDialog({ open: true, cavo })\n        break\n      case 'create_certificate':\n      case 'generate_pdf':\n        setCertificazioneDialog({ open: true, cavo })\n        break\n    }\n  }\n\n  const handleContextMenuAction = (cavo: Cavo, action: string) => {\n    console.log('Context menu action:', action, 'for cavo:', cavo.id_cavo)\n\n    switch (action) {\n      case 'view_details':\n        toast({\n          title: \"Visualizza Dettagli\",\n          description: `Apertura dettagli per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'edit':\n        toast({\n          title: \"Modifica Cavo\",\n          description: \"Funzione modifica cavo in sviluppo\",\n        })\n        break\n      case 'delete':\n        toast({\n          title: \"Elimina Cavo\",\n          description: \"Funzione eliminazione cavo in sviluppo\",\n          variant: \"destructive\"\n        })\n        break\n      case 'add_new':\n        toast({\n          title: \"Aggiungi Nuovo Cavo\",\n          description: \"Funzione aggiunta nuovo cavo in sviluppo\",\n        })\n        break\n      case 'select':\n        const isSelected = selectedCavi.includes(cavo.id_cavo)\n        if (isSelected) {\n          setSelectedCavi(selectedCavi.filter(id => id !== cavo.id_cavo))\n          toast({\n            title: \"Cavo Deselezionato\",\n            description: `Cavo ${cavo.id_cavo} deselezionato`,\n          })\n        } else {\n          setSelectedCavi([...selectedCavi, cavo.id_cavo])\n          toast({\n            title: \"Cavo Selezionato\",\n            description: `Cavo ${cavo.id_cavo} selezionato`,\n          })\n        }\n        break\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo)\n        toast({\n          title: \"ID Copiato\",\n          description: `ID cavo ${cavo.id_cavo} copiato negli appunti`,\n        })\n        break\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`\n        navigator.clipboard.writeText(details)\n        toast({\n          title: \"Dettagli Copiati\",\n          description: \"Dettagli cavo copiati negli appunti\",\n        })\n        break\n      case 'add_to_command':\n        toast({\n          title: \"Aggiungi a Comanda\",\n          description: \"Funzione aggiunta a comanda in sviluppo\",\n        })\n        break\n      case 'remove_from_command':\n        toast({\n          title: \"Rimuovi da Comanda\",\n          description: \"Funzione rimozione da comanda in sviluppo\",\n        })\n        break\n      case 'create_command_posa':\n        setCreaComandaDialog({ open: true, tipoComanda: 'POSA' })\n        break\n      case 'create_command_collegamento_partenza':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_PARTENZA' })\n        break\n      case 'create_command_collegamento_arrivo':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_ARRIVO' })\n        break\n      case 'create_command_certificazione':\n        setCreaComandaDialog({ open: true, tipoComanda: 'CERTIFICAZIONE' })\n        break\n      case 'add_multiple_to_command':\n        toast({\n          title: \"Aggiungi Tutti a Comanda\",\n          description: \"Funzione aggiunta multipla a comanda in sviluppo\",\n        })\n        break\n      case 'remove_multiple_from_commands':\n        toast({\n          title: \"Rimuovi Tutti dalle Comande\",\n          description: \"Funzione rimozione multipla dalle comande in sviluppo\",\n        })\n        break\n      default:\n        toast({\n          title: \"Azione non implementata\",\n          description: `Azione ${action} non ancora implementata`,\n        })\n        break\n    }\n  }\n\n  // Gestione successo/errore dialoghi\n  const handleDialogSuccess = (message: string) => {\n    toast({\n      title: \"Operazione completata\",\n      description: message,\n    })\n    // Ricarica i dati\n    loadCavi()\n  }\n\n  const handleDialogError = (message: string) => {\n    toast({\n      title: \"Errore\",\n      description: message,\n      variant: \"destructive\"\n    })\n  }\n\n  if (isLoading || loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  if (!cantiereId) {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <Alert>\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>\n            Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi.\n          </AlertDescription>\n        </Alert>\n        <div className=\"mt-4 p-4 bg-gray-100 rounded\">\n          <h3 className=\"font-bold\">Debug Info:</h3>\n          <p>User: {user ? user.username : 'Non autenticato'}</p>\n          <p>Cantiere context: {cantiere ? cantiere.commessa : 'Nessuno'}</p>\n          <p>Token presente: {typeof window !== 'undefined' ? (localStorage.getItem('token') ? 'Sì' : 'No') : 'N/A'}</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"container mx-auto p-6\">\n        <Alert variant=\"destructive\">\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n        <Button onClick={loadCavi} className=\"mt-4\">\n          Riprova\n        </Button>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"container mx-auto p-6\">\n\n      {/* Statistics */}\n      <CaviStatistics\n        cavi={cavi}\n        filteredCavi={filteredCavi}\n        revisioneCorrente={revisioneCorrente}\n        className=\"mb-4\"\n      />\n\n      {/* Tabella Cavi Attivi */}\n      <div className=\"mb-8\">\n        <CaviTable\n          cavi={cavi}\n          loading={loading}\n          selectionEnabled={selectionEnabled}\n          selectedCavi={selectedCavi}\n          onSelectionChange={setSelectedCavi}\n          onStatusAction={handleStatusAction}\n          onContextMenuAction={handleContextMenuAction}\n        />\n      </div>\n\n      {/* Tabella Cavi Spare */}\n      {caviSpare.length > 0 && (\n        <div className=\"mb-8\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5\" />\n                <span>Cavi Spare ({caviSpare.length})</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CaviTable\n                cavi={caviSpare}\n                loading={loading}\n                selectionEnabled={false}\n                onStatusAction={handleStatusAction}\n                onContextMenuAction={handleContextMenuAction}\n              />\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Debug info - solo in development */}\n      {process.env.NODE_ENV === 'development' && (\n        <Card className=\"mt-6\">\n          <CardHeader>\n            <CardTitle>Debug Info</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <p><strong>User:</strong> {user ? user.username : 'Non autenticato'}</p>\n                <p><strong>User Role:</strong> {user ? user.ruolo : 'N/A'}</p>\n                <p><strong>Cantiere ID:</strong> {cantiereId}</p>\n                <p><strong>Cantiere context:</strong> {cantiere ? cantiere.commessa : 'Nessuno'}</p>\n              </div>\n              <div>\n                <p><strong>Token presente:</strong> {typeof window !== 'undefined' ? (localStorage.getItem('token') ? 'Sì' : 'No') : 'N/A'}</p>\n                <p><strong>Loading:</strong> {loading ? 'Sì' : 'No'}</p>\n                <p><strong>Error:</strong> {error || 'Nessuno'}</p>\n                <p><strong>Cavi ricevuti:</strong> {cavi.length}</p>\n                <p><strong>Cavi spare:</strong> {caviSpare.length}</p>\n              </div>\n            </div>\n            <div className=\"mt-4\">\n              <Button\n                onClick={async () => {\n                  try {\n                    const response = await fetch('http://localhost:8001/api/cavi/debug/1')\n                    const data = await response.json()\n                    console.log('🔍 Test diretto backend:', data)\n                    alert(`Backend ha ${data.total_cavi} cavi per cantiere 1`)\n                  } catch (err) {\n                    console.error('❌ Errore test backend:', err)\n                    alert('Errore nel test backend')\n                  }\n                }}\n                variant=\"outline\"\n                size=\"sm\"\n              >\n                Test Backend Diretto\n              </Button>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Dialoghi */}\n      <InserisciMetriDialog\n        open={inserisciMetriDialog.open}\n        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}\n        cavo={inserisciMetriDialog.cavo}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ModificaBobinaDialog\n        open={modificaBobinaDialog.open}\n        onClose={() => setModificaBobinaDialog({ open: false, cavo: null })}\n        cavo={modificaBobinaDialog.cavo}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <CollegamentiDialog\n        open={collegamentiDialog.open}\n        onClose={() => setCollegamentiDialog({ open: false, cavo: null })}\n        cavo={collegamentiDialog.cavo}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <CertificazioneDialog\n        open={certificazioneDialog.open}\n        onClose={() => setCertificazioneDialog({ open: false, cavo: null })}\n        cavo={certificazioneDialog.cavo}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <CreaComandaDialog\n        open={creaComandaDialog.open}\n        onClose={() => setCreaComandaDialog({ open: false })}\n        caviSelezionati={selectedCavi}\n        tipoComanda={creaComandaDialog.tipoComanda}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ImportExcelDialog\n        open={importExcelDialog.open}\n        onClose={() => setImportExcelDialog({ open: false })}\n        tipo={importExcelDialog.tipo || 'cavi'}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ExportDataDialog\n        open={exportDataDialog}\n        onClose={() => setExportDataDialog(false)}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n    </div>\n  )\n}\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AAAA;AAAA;AApBA;;;;;;;;;;;;;;;;;;;AAwCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,yBAAyB;IACzB,MAAM,QAAQ,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAA4D;QACtG,QAAQ,GAAG,CAAC,GAAG,YAAY,gBAAgB,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,aAAa;IAC/E,2CAA2C;IAC7C;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;KAAK;IAET,uBAAuB;IACvB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,aAAa;QACb,0BAA0B;QAC1B,yBAAyB;QACzB,2BAA2B;QAC3B,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,kBAAkB;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAGnC;IACF,GAAG;QAAC;KAAS;IAEb,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,aAAa,GAAG;YAChC;YACA;QACF;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,WAAW,mBAAmB,CAAC,EAAE;gBAC9F,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;oBAC1D,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,qBAAqB,KAAK,kBAAkB,IAAI;YAClD,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,qBAAqB;QACvB;IACF;IAEA,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,SAAS;YAET,QAAQ,GAAG,CAAC,+CAA+C;YAC3D,QAAQ,GAAG,CAAC,sBAAsB,CAAC,CAAC,aAAa,OAAO,CAAC;YAEzD,gCAAgC;YAChC,IAAI;gBACF,MAAM,OAAO,MAAM,iHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBACnC,QAAQ,GAAG,CAAC,0CAA0C,MAAM,UAAU;gBAEtE,6BAA6B;gBAC7B,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,OAAe,CAAC,KAAK,KAAK;gBAC1D,MAAM,oBAAoB,KAAK,MAAM,CAAC,CAAC,OAAe,KAAK,KAAK;gBAEhE,QAAQ;gBACR,aAAa;gBAEb,sBAAsB;gBACtB,eAAe;YAEjB,EAAE,OAAO,UAAe;gBACtB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,KAAK,CAAC,uBAAuB;gBAErC,4DAA4D;gBAC5D,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,CAAC,qCAAqC,EAAE,YAAY;oBACjF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,GAAG,CAAC,8BAA8B;oBAE1C,IAAI,UAAU,IAAI,IAAI,MAAM,OAAO,CAAC,UAAU,IAAI,GAAG;wBACnD,MAAM,aAAa,UAAU,IAAI,CAAC,MAAM,CAAC,CAAC,OAAc,CAAC,KAAK,KAAK;wBACnE,MAAM,oBAAoB,UAAU,IAAI,CAAC,MAAM,CAAC,CAAC,OAAc,KAAK,KAAK;wBAEzE,QAAQ;wBACR,aAAa;wBACb,eAAe;wBAEf,SAAS;oBACX,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,MAAM,SAAS,8BAA8B;;gBAC/C;YACF;QAEF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,SAAS,CAAC,iCAAiC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;QAC9F,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,SAAS,MAAM;QAC9B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI,CAAC,IAAI,GAAG,MAAM;QAC9F,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,YAAY,MAAM,GAAG,MAAM,CAAC,gBAAgB;;QACxG,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;QAE9D,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAC9E,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QACjF,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,GAAG,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAClH,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAE7G,SAAS;YACP;YACA;YACA;YACA;YACA,0BAA0B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,aAAa,SAAU,OAAO;YACjF,yBAAyB,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,SAAU,OAAO;YAC/E,2BAA2B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,cAAc,SAAU,OAAO;YACnF;YACA;YACA;YACA;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB,CAAC,MAAY,QAAgB;QACtD,QAAQ,GAAG,CAAC,kBAAkB,QAAQ,aAAa,KAAK,OAAO,EAAE,UAAU;QAE3E,OAAQ;YACN,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;YACF,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,iBAAiB,EAAE,MAAM,UAAU,EAAE,KAAK,OAAO,EAAE;gBACnE;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;YACL,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;QACJ;IACF;IAEA,MAAM,0BAA0B,CAAC,MAAY;QAC3C,QAAQ,GAAG,CAAC,wBAAwB,QAAQ,aAAa,KAAK,OAAO;QAErE,OAAQ;YACN,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,2BAA2B,EAAE,KAAK,OAAO,EAAE;gBAC3D;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM,aAAa,aAAa,QAAQ,CAAC,KAAK,OAAO;gBACrD,IAAI,YAAY;oBACd,gBAAgB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,OAAO;oBAC7D,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,cAAc,CAAC;oBACnD;gBACF,OAAO;oBACL,gBAAgB;2BAAI;wBAAc,KAAK,OAAO;qBAAC;oBAC/C,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC;oBACjD;gBACF;gBACA;YACF,KAAK;gBACH,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;gBAC1C,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,sBAAsB,CAAC;gBAC9D;gBACA;YACF,KAAK;gBACH,MAAM,UAAU,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,cAAc,EAAE,KAAK,UAAU,IAAI,KAAK,OAAO,CAAC,SAAS,EAAE,KAAK,aAAa,EAAE;gBACjJ,UAAU,SAAS,CAAC,SAAS,CAAC;gBAC9B,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAO;gBACvD;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAwB;gBACxE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAsB;gBACtE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAiB;gBACjE;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF;gBACE,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,OAAO,EAAE,OAAO,wBAAwB,CAAC;gBACzD;gBACA;QACJ;IACF;IAEA,oCAAoC;IACpC,MAAM,sBAAsB,CAAC;QAC3B,MAAM;YACJ,OAAO;YACP,aAAa;QACf;QACA,kBAAkB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;IACF;IAEA,IAAI,aAAa,SAAS;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,mBAAgB;sCAAC;;;;;;;;;;;;8BAIpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAY;;;;;;sCAC1B,8OAAC;;gCAAE;gCAAO,OAAO,KAAK,QAAQ,GAAG;;;;;;;sCACjC,8OAAC;;gCAAE;gCAAmB,WAAW,SAAS,QAAQ,GAAG;;;;;;;sCACrD,8OAAC;;gCAAE;gCAAiB,6EAAgF;;;;;;;;;;;;;;;;;;;IAI5G;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;8BAErB,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAU,WAAU;8BAAO;;;;;;;;;;;;IAKlD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAGb,8OAAC,4IAAA,CAAA,UAAc;gBACb,MAAM;gBACN,cAAc;gBACd,mBAAmB;gBACnB,WAAU;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uIAAA,CAAA,UAAS;oBACR,MAAM;oBACN,SAAS;oBACT,kBAAkB;oBAClB,cAAc;oBACd,mBAAmB;oBACnB,gBAAgB;oBAChB,qBAAqB;;;;;;;;;;;YAKxB,UAAU,MAAM,GAAG,mBAClB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;;4CAAK;4CAAa,UAAU,MAAM;4CAAC;;;;;;;;;;;;;;;;;;sCAGxC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,uIAAA,CAAA,UAAS;gCACR,MAAM;gCACN,SAAS;gCACT,kBAAkB;gCAClB,gBAAgB;gCAChB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;YAQ9B,oDAAyB,+BACxB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAc;oDAAE,OAAO,KAAK,QAAQ,GAAG;;;;;;;0DAClD,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAmB;oDAAE,OAAO,KAAK,KAAK,GAAG;;;;;;;0DACpD,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAqB;oDAAE;;;;;;;0DAClC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAA0B;oDAAE,WAAW,SAAS,QAAQ,GAAG;;;;;;;;;;;;;kDAExE,8OAAC;;0DACC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAwB;oDAAE,6EAAgF;;;;;;;0DACrH,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAiB;oDAAE,UAAU,OAAO;;;;;;;0DAC/C,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAe;oDAAE,SAAS;;;;;;;0DACrC,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAuB;oDAAE,KAAK,MAAM;;;;;;;0DAC/C,8OAAC;;kEAAE,8OAAC;kEAAO;;;;;;oDAAoB;oDAAE,UAAU,MAAM;;;;;;;;;;;;;;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,IAAI;4CACF,MAAM,WAAW,MAAM,MAAM;4CAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;4CAChC,QAAQ,GAAG,CAAC,4BAA4B;4CACxC,MAAM,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,oBAAoB,CAAC;wCAC3D,EAAE,OAAO,KAAK;4CACZ,QAAQ,KAAK,CAAC,0BAA0B;4CACxC,MAAM;wCACR;oCACF;oCACA,SAAQ;oCACR,MAAK;8CACN;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,MAAM,qBAAqB,IAAI;gBAC/B,SAAS,IAAM,wBAAwB;wBAAE,MAAM;wBAAO,MAAM;oBAAK;gBACjE,MAAM,qBAAqB,IAAI;gBAC/B,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,MAAM,qBAAqB,IAAI;gBAC/B,SAAS,IAAM,wBAAwB;wBAAE,MAAM;wBAAO,MAAM;oBAAK;gBACjE,MAAM,qBAAqB,IAAI;gBAC/B,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,gJAAA,CAAA,UAAkB;gBACjB,MAAM,mBAAmB,IAAI;gBAC7B,SAAS,IAAM,sBAAsB;wBAAE,MAAM;wBAAO,MAAM;oBAAK;gBAC/D,MAAM,mBAAmB,IAAI;gBAC7B,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,MAAM,qBAAqB,IAAI;gBAC/B,SAAS,IAAM,wBAAwB;wBAAE,MAAM;wBAAO,MAAM;oBAAK;gBACjE,MAAM,qBAAqB,IAAI;gBAC/B,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,+IAAA,CAAA,UAAiB;gBAChB,MAAM,kBAAkB,IAAI;gBAC5B,SAAS,IAAM,qBAAqB;wBAAE,MAAM;oBAAM;gBAClD,iBAAiB;gBACjB,aAAa,kBAAkB,WAAW;gBAC1C,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,+IAAA,CAAA,UAAiB;gBAChB,MAAM,kBAAkB,IAAI;gBAC5B,SAAS,IAAM,qBAAqB;wBAAE,MAAM;oBAAM;gBAClD,MAAM,kBAAkB,IAAI,IAAI;gBAChC,WAAW;gBACX,SAAS;;;;;;0BAGX,8OAAC,8IAAA,CAAA,UAAgB;gBACf,MAAM;gBACN,SAAS,IAAM,oBAAoB;gBACnC,WAAW;gBACX,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}