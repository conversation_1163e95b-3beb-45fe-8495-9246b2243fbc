(()=>{var e={};e.id=954,e.ids=[954],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44067:(e,r,t)=>{Promise.resolve().then(t.bind(t,96772))},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(60687);t(43210);var a=t(4780);function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58894:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["debug-user",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,92026)),"C:\\CMS\\webapp-nextjs\\src\\app\\debug-user\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\debug-user\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/debug-user/page",pathname:"/debug-user",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84395:(e,r,t)=>{Promise.resolve().then(t.bind(t,92026))},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var a=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>l,TN:()=>d});var s=t(60687),a=t(43210),i=t(24224),n=t(4780);let o=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=a.forwardRef(({className:e,variant:r,...t},a)=>(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(o({variant:r}),e),...t}));l.displayName="Alert",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...r})).displayName="AlertTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...r}));d.displayName="AlertDescription"},92026:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\debug-user\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\debug-user\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96772:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210),i=t(29523),n=t(89667),o=t(44493),l=t(91821);function d(){let[e,r]=(0,a.useState)("Antonio"),[t,d]=(0,a.useState)("<EMAIL>"),[c,u]=(0,a.useState)(null),[p,x]=(0,a.useState)(!1),m=async()=>{x(!0),u(null);try{console.log("Checking user:",e);let r=await fetch("http://localhost:8001/api/users/",{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!r.ok)throw Error(`HTTP ${r.status}: ${r.statusText}`);let t=await r.json(),s=t.find(r=>r.username.toLowerCase()===e.toLowerCase());u({success:!0,user:s,allUsers:t.map(e=>({id:e.id_utente,username:e.username,email:e.email,ruolo:e.ruolo}))})}catch(e){console.error("Error:",e),u({success:!1,error:e instanceof Error?e.message:"Unknown error"})}finally{x(!1)}},g=async()=>{if(!c?.user)return void alert("Prima cerca l'utente");x(!0);try{console.log("Updating user email:",c.user.id_utente,t);let e=await fetch(`http://localhost:8001/api/users/${c.user.id_utente}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({email:t})});if(!e.ok){let r=await e.json();throw Error(r.detail||`HTTP ${e.status}`)}let r=await e.json();u(e=>({...e,user:r,updated:!0})),alert("Email aggiornata con successo!")}catch(e){console.error("Error updating email:",e),alert(`Errore: ${e instanceof Error?e.message:"Unknown error"}`)}finally{x(!1)}},f=async()=>{x(!0);try{console.log("Testing password reset for:",t);let e=await fetch("http://localhost:8001/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,user_type:"user"})}),r=await e.json();alert(`Password reset test:
Status: ${e.status}
Response: ${JSON.stringify(r,null,2)}`)}catch(e){console.error("Error testing password reset:",e),alert(`Errore: ${e instanceof Error?e.message:"Unknown error"}`)}finally{x(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsxs)(o.Zp,{className:"w-full max-w-4xl",children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"Debug User & Password Reset"})}),(0,s.jsxs)(o.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Username:"}),(0,s.jsx)(n.p,{value:e,onChange:e=>r(e.target.value),placeholder:"Enter username"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Email:"}),(0,s.jsx)(n.p,{type:"email",value:t,onChange:e=>d(e.target.value),placeholder:"Enter email"})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(i.$,{onClick:m,disabled:p,className:"flex-1",children:p?"Checking...":"Check User"}),(0,s.jsx)(i.$,{onClick:g,disabled:p||!c?.user,variant:"outline",className:"flex-1",children:p?"Updating...":"Update Email"}),(0,s.jsx)(i.$,{onClick:f,disabled:p,variant:"secondary",className:"flex-1",children:p?"Testing...":"Test Password Reset"})]}),c&&(0,s.jsx)(l.Fc,{className:c.success?"border-green-200 bg-green-50":"border-red-200 bg-red-50",children:(0,s.jsx)(l.TN,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[c.error&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Error:"})," ",c.error]}),c.user&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Found User:"}),(0,s.jsx)("pre",{className:"mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(c.user,null,2)})]}),c.updated&&(0,s.jsx)("div",{className:"text-green-600 font-medium",children:"✅ Email updated successfully!"}),c.allUsers&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"All Users:"}),(0,s.jsx)("pre",{className:"mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40",children:JSON.stringify(c.allUsers,null,2)})]})]})})})]})]})})}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,538,658,615],()=>t(58894));module.exports=s})();