(()=>{var e={};e.id=986,e.ids=[986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10698:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>s});let s=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11379:(e,a,i)=>{Promise.resolve().then(i.bind(i,46025))},12412:e=>{"use strict";e.exports=require("assert")},15079:(e,a,i)=>{"use strict";i.d(a,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>c,yv:()=>d});var s=i(60687);i(43210);var t=i(97822),r=i(78272),n=i(13964),l=i(3589),o=i(4780);function c({...e}){return(0,s.jsx)(t.bL,{"data-slot":"select",...e})}function d({...e}){return(0,s.jsx)(t.WT,{"data-slot":"select-value",...e})}function m({className:e,size:a="default",children:i,...n}){return(0,s.jsxs)(t.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[i,(0,s.jsx)(t.In,{asChild:!0,children:(0,s.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:a,position:i="popper",...r}){return(0,s.jsx)(t.ZL,{children:(0,s.jsxs)(t.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:i,...r,children:[(0,s.jsx)(p,{}),(0,s.jsx)(t.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(h,{})]})})}function u({className:e,children:a,...i}){return(0,s.jsxs)(t.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(t.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(t.p4,{children:a})]})}function p({className:e,...a}){return(0,s.jsx)(t.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function h({className:e,...a}){return(0,s.jsx)(t.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(r.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45583:(e,a,i)=>{"use strict";i.d(a,{A:()=>s});let s=(0,i(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},46025:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>eV});var s=i(60687),t=i(43210),r=i(16189),n=i(44493),l=i(29523),o=i(91821),c=i(63213),d=i(62185),m=i(96834),x=i(56896),u=i(6211),p=i(89667),h=i(15079),v=i(70569),g=i(98599),j=i(11273),f=i(31355),b=i(1359),N=i(32547),C=i(96963),y=i(55509),_=i(25028),w=i(46059),A=i(14163),z=i(8730),k=i(65551),S=i(63376),E=i(42247),F="Popover",[O,I]=(0,j.A)(F,[y.Bk]),T=(0,y.Bk)(),[$,R]=O(F),P=e=>{let{__scopePopover:a,children:i,open:r,defaultOpen:n,onOpenChange:l,modal:o=!1}=e,c=T(a),d=t.useRef(null),[m,x]=t.useState(!1),[u,p]=(0,k.i)({prop:r,defaultProp:n??!1,onChange:l,caller:F});return(0,s.jsx)(y.bL,{...c,children:(0,s.jsx)($,{scope:a,contentId:(0,C.B)(),triggerRef:d,open:u,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:m,onCustomAnchorAdd:t.useCallback(()=>x(!0),[]),onCustomAnchorRemove:t.useCallback(()=>x(!1),[]),modal:o,children:i})})};P.displayName=F;var M="PopoverAnchor";t.forwardRef((e,a)=>{let{__scopePopover:i,...r}=e,n=R(M,i),l=T(i),{onCustomAnchorAdd:o,onCustomAnchorRemove:c}=n;return t.useEffect(()=>(o(),()=>c()),[o,c]),(0,s.jsx)(y.Mz,{...l,...r,ref:a})}).displayName=M;var D="PopoverTrigger",L=t.forwardRef((e,a)=>{let{__scopePopover:i,...t}=e,r=R(D,i),n=T(i),l=(0,g.s)(a,r.triggerRef),o=(0,s.jsx)(A.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":Q(r.open),...t,ref:l,onClick:(0,v.m)(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?o:(0,s.jsx)(y.Mz,{asChild:!0,...n,children:o})});L.displayName=D;var q="PopoverPortal",[B,V]=O(q,{forceMount:void 0}),U=e=>{let{__scopePopover:a,forceMount:i,children:t,container:r}=e,n=R(q,a);return(0,s.jsx)(B,{scope:a,forceMount:i,children:(0,s.jsx)(w.C,{present:i||n.open,children:(0,s.jsx)(_.Z,{asChild:!0,container:r,children:t})})})};U.displayName=q;var G="PopoverContent",J=t.forwardRef((e,a)=>{let i=V(G,e.__scopePopover),{forceMount:t=i.forceMount,...r}=e,n=R(G,e.__scopePopover);return(0,s.jsx)(w.C,{present:t||n.open,children:n.modal?(0,s.jsx)(W,{...r,ref:a}):(0,s.jsx)(H,{...r,ref:a})})});J.displayName=G;var Z=(0,z.TL)("PopoverContent.RemoveScroll"),W=t.forwardRef((e,a)=>{let i=R(G,e.__scopePopover),r=t.useRef(null),n=(0,g.s)(a,r),l=t.useRef(!1);return t.useEffect(()=>{let e=r.current;if(e)return(0,S.Eq)(e)},[]),(0,s.jsx)(E.A,{as:Z,allowPinchZoom:!0,children:(0,s.jsx)(K,{...e,ref:n,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,v.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||i.triggerRef.current?.focus()}),onPointerDownOutside:(0,v.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,i=0===a.button&&!0===a.ctrlKey;l.current=2===a.button||i},{checkForDefaultPrevented:!1}),onFocusOutside:(0,v.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),H=t.forwardRef((e,a)=>{let i=R(G,e.__scopePopover),r=t.useRef(!1),n=t.useRef(!1);return(0,s.jsx)(K,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||i.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,"pointerdown"===a.detail.originalEvent.type&&(n.current=!0));let s=a.target;i.triggerRef.current?.contains(s)&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&n.current&&a.preventDefault()}})}),K=t.forwardRef((e,a)=>{let{__scopePopover:i,trapFocus:t,onOpenAutoFocus:r,onCloseAutoFocus:n,disableOutsidePointerEvents:l,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:m,...x}=e,u=R(G,i),p=T(i);return(0,b.Oh)(),(0,s.jsx)(N.n,{asChild:!0,loop:!0,trapped:t,onMountAutoFocus:r,onUnmountAutoFocus:n,children:(0,s.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:m,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>u.onOpenChange(!1),children:(0,s.jsx)(y.UC,{"data-state":Q(u.open),role:"dialog",id:u.contentId,...p,...x,ref:a,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),X="PopoverClose";function Q(e){return e?"open":"closed"}t.forwardRef((e,a)=>{let{__scopePopover:i,...t}=e,r=R(X,i);return(0,s.jsx)(A.sG.button,{type:"button",...t,ref:a,onClick:(0,v.m)(e.onClick,()=>r.onOpenChange(!1))})}).displayName=X,t.forwardRef((e,a)=>{let{__scopePopover:i,...t}=e,r=T(i);return(0,s.jsx)(y.i3,{...r,...t,ref:a})}).displayName="PopoverArrow";var Y=i(4780);let ee=t.forwardRef(({className:e,align:a="center",sideOffset:i=4,...t},r)=>(0,s.jsx)(U,{children:(0,s.jsx)(J,{ref:r,align:a,sideOffset:i,className:(0,Y.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));ee.displayName=J.displayName;var ea=i(62688);let ei=(0,ea.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),es=(0,ea.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),et=(0,ea.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var er=i(11860);let en=(0,ea.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);function el({data:e=[],columns:a=[],loading:i=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:o,renderRow:c,className:d}){let[x,p]=(0,t.useState)({key:null,direction:null}),[h,v]=(0,t.useState)({}),[g,j]=(0,t.useState)({}),f=a=>[...new Set(e.map(e=>e[a]).filter(Boolean))].sort(),b=(0,t.useMemo)(()=>{let a=[...e];return Object.entries(h).forEach(([e,i])=>{!i.value||Array.isArray(i.value)&&0===i.value.length||"string"==typeof i.value&&""===i.value.trim()||(a=a.filter(a=>{let s=a[e];if("select"===i.type)return(Array.isArray(i.value)?i.value:[i.value]).includes(s);if("text"===i.type){let e=i.value.toLowerCase(),a=String(s||"").toLowerCase();return"equals"===i.operator?a===e:a.includes(e)}if("number"===i.type){let e=parseFloat(s),a=parseFloat(i.value);if(isNaN(e)||isNaN(a))return!1;switch(i.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),x.key&&x.direction&&a.sort((e,a)=>{let i=e[x.key],s=a[x.key];if(null==i&&null==s)return 0;if(null==i)return"asc"===x.direction?-1:1;if(null==s)return"asc"===x.direction?1:-1;let t=parseFloat(i),r=parseFloat(s),n=!isNaN(t)&&!isNaN(r),l=0;return l=n?t-r:String(i).localeCompare(String(s)),"asc"===x.direction?l:-l}),a},[e,h,x]),N=e=>{let i=a.find(a=>a.field===e);i?.disableSort||p(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},C=(e,a)=>{v(i=>({...i,[e]:{...i[e],...a}}))},y=e=>{v(a=>{let i={...a};return delete i[e],i})},_=e=>x.key!==e?(0,s.jsx)(ei,{className:"h-3 w-3"}):"asc"===x.direction?(0,s.jsx)(es,{className:"h-3 w-3"}):"desc"===x.direction?(0,s.jsx)(et,{className:"h-3 w-3"}):(0,s.jsx)(ei,{className:"h-3 w-3"}),w=Object.keys(h).length>0;return i?(0,s.jsx)(n.Zp,{className:d,children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,s.jsxs)("div",{className:d,children:[w&&(0,s.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(h).map(([e,i])=>{let t=a.find(a=>a.field===e);if(!t)return null;let r=Array.isArray(i.value)?i.value.join(", "):String(i.value);return(0,s.jsxs)(m.E,{variant:"secondary",className:"gap-1",children:[t.headerName,": ",r,(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>y(e),children:(0,s.jsx)(er.A,{className:"h-3 w-3"})})]},e)}),(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{v({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-0",children:(0,s.jsxs)(u.XI,{children:[(0,s.jsx)(u.A0,{children:(0,s.jsx)(u.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:a.map(a=>(0,s.jsx)(u.nd,{className:(0,Y.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:{width:a.width,...a.headerStyle},children:a.renderHeader?a.renderHeader():(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:a.headerName}),!a.disableSort&&(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-mariner-100",onClick:()=>N(a.field),children:_(a.field)}),!a.disableFilter&&(0,s.jsxs)(P,{open:g[a.field],onOpenChange:e=>j(i=>({...i,[a.field]:e})),children:[(0,s.jsx)(L,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:(0,Y.cn)("h-auto p-0 hover:bg-mariner-100",h[a.field]&&"text-mariner-600"),children:(0,s.jsx)(en,{className:"h-3 w-3"})})}),(0,s.jsx)(ee,{className:"w-64",align:"start",children:(0,s.jsx)(eo,{column:a,data:e,currentFilter:h[a.field],onFilterChange:e=>C(a.field,e),onClearFilter:()=>y(a.field),getUniqueValues:()=>f(a.field)})})]})]})},a.field))})}),(0,s.jsx)(u.BF,{children:b.length>0?b.map((e,i)=>c?c(e,i):(0,s.jsx)(u.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:a.map(a=>(0,s.jsx)(u.nA,{className:(0,Y.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},i)):(0,s.jsx)(u.Hj,{children:(0,s.jsx)(u.nA,{colSpan:a.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})})]})}function eo({column:e,currentFilter:a,onFilterChange:i,onClearFilter:r,getUniqueValues:n}){let[o,c]=(0,t.useState)(a?.value||""),[d,m]=(0,t.useState)(a?.operator||"contains"),u=n(),v="number"!==e.dataType&&u.length<=20,g="number"===e.dataType,j=()=>{v?i({type:"select",value:Array.isArray(o)?o:[o]}):g?i({type:"number",value:o,operator:d}):i({type:"text",value:o,operator:d})};return(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",e.headerName]}),v?(0,s.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(x.S,{id:`filter-${e}`,checked:Array.isArray(o)?o.includes(e):o===e,onCheckedChange:a=>{Array.isArray(o)?c(a?[...o,e]:o.filter(a=>a!==e)):c(a?[e]:[])}}),(0,s.jsx)("label",{htmlFor:`filter-${e}`,className:"text-sm",children:e})]},e))}):(0,s.jsxs)("div",{className:"space-y-2",children:[g&&(0,s.jsxs)(h.l6,{value:d,onValueChange:m,children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"equals",children:"Uguale a"}),(0,s.jsx)(h.eb,{value:"gt",children:"Maggiore di"}),(0,s.jsx)(h.eb,{value:"lt",children:"Minore di"}),(0,s.jsx)(h.eb,{value:"gte",children:"Maggiore o uguale"}),(0,s.jsx)(h.eb,{value:"lte",children:"Minore o uguale"})]})]}),!g&&(0,s.jsxs)(h.l6,{value:d,onValueChange:m,children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,s.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]}),(0,s.jsx)(p.p,{placeholder:`Cerca ${e.headerName.toLowerCase()}...`,value:o,onChange:e=>c(e.target.value),onKeyDown:e=>"Enter"===e.key&&j()})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(l.$,{size:"sm",onClick:j,children:"Applica"}),(0,s.jsx)(l.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var ec=i(99270);let ed=(0,ea.A)("square-check-big",[["path",{d:"M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344",key:"2acyp4"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),em=(0,ea.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);function ex({cavi:e=[],onFilteredDataChange:a,loading:i=!1,selectionEnabled:r=!1,onSelectionToggle:o}){let[c,d]=(0,t.useState)(""),[m,x]=(0,t.useState)("contains"),u=e=>e?e.toString().toLowerCase().trim():"",v=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},g=(0,t.useCallback)((e,a,i)=>{let s=u(a);if(!s)return!0;let t=u(e.id_cavo),{prefix:r,number:n,suffix:l}=v(e.id_cavo||""),o=u(e.tipologia),c=u(e.formazione||e.sezione),d=u(e.utility),m=u(e.sistema),x=u(e.da||e.ubicazione_partenza),p=u(e.a||e.ubicazione_arrivo),h=u(e.utenza_partenza),g=u(e.utenza_arrivo),j=[t,r,n,l,o,c,d,m,x,p,h,g,u(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":u(e.id_bobina)],f=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],b=s.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(b){let e=b[1],a=parseFloat(b[2]);return f.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(s);return!!(!isNaN(N)&&f.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?j.some(e=>e===s):j.some(e=>e.includes(s)))},[]);(0,t.useCallback)(()=>{if(!c.trim())return void a?.(e);let i=c.split(",").map(e=>e.trim()).filter(e=>e.length>0),s=[];s="equals"===m?1===i.length?e.filter(e=>g(e,i[0],!0)):e.filter(e=>i.every(a=>g(e,a,!0))):e.filter(e=>i.some(a=>g(e,a,!1))),a?.(s)},[c,m,e,a,g]);let j=e=>{d(e)},f=()=>{d(""),x("contains")};return(0,s.jsx)(n.Zp,{className:"mb-4",children:(0,s.jsxs)(n.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(ec.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(p.p,{placeholder:"Ricerca intelligente cavi...",value:c,onChange:e=>j(e.target.value),disabled:i,className:"pl-10 pr-10"}),c&&(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:f,children:(0,s.jsx)(er.A,{className:"h-3 w-3"})})]}),(0,s.jsx)("div",{className:"w-40",children:(0,s.jsxs)(h.l6,{value:m,onValueChange:e=>x(e),children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"contains",children:"Contiene"}),(0,s.jsx)(h.eb,{value:"equals",children:"Uguale a"})]})]})}),c&&(0,s.jsx)(l.$,{variant:"outline",size:"sm",onClick:f,disabled:i,children:"Pulisci"}),o&&(0,s.jsxs)(l.$,{variant:r?"default":"outline",size:"sm",onClick:o,className:"flex items-center gap-2",children:[r?(0,s.jsx)(ed,{className:"h-4 w-4"}):(0,s.jsx)(em,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]})]}),c&&(0,s.jsx)("div",{className:"mt-2 text-xs text-muted-foreground",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)("span",{children:"\uD83D\uDCA1 Suggerimenti:"}),(0,s.jsx)("span",{children:"• Usa virgole per termini multipli"}),(0,s.jsx)("span",{children:"• Cerca per ID, tipologia, formazione, ubicazioni"}),(0,s.jsx)("span",{children:"• Usa >100, <=50 per valori numerici"})]})})]})})}let eu=(0,ea.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var ep=i(19080),eh=i(84027),ev=i(93613),eg=i(45583),ej=i(5336),ef=i(48730),eb=i(23361);function eN({cavi:e=[],loading:a=!1,selectionEnabled:i=!1,selectedCavi:r=[],onSelectionChange:o,onStatusAction:c,onContextMenuAction:d}){let[p,h]=(0,t.useState)(e),[v,g]=(0,t.useState)(e),[j,f]=(0,t.useState)(i),b=e=>{o&&o(e?v.map(e=>e.id_cavo):[])},N=(e,a)=>{o&&o(a?[...r,e]:r.filter(a=>a!==e))},C=(0,t.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID Cavo",dataType:"text",headerStyle:{fontWeight:"bold"},renderCell:e=>(0,s.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text"},{field:"utility",headerName:"Utility",dataType:"text"},{field:"tipologia",headerName:"Tipologia",dataType:"text"},{field:"formazione",headerName:"Formazione",dataType:"text",align:"right",renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"Metri Teorici",dataType:"number",align:"right",renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"Metri Reali",dataType:"number",align:"right",renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",renderCell:e=>e.da||e.ubicazione_partenza},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",renderCell:e=>e.a||e.ubicazione_arrivo},{field:"id_bobina",headerName:"Bobina",dataType:"text",renderCell:e=>e.id_bobina||"N/A"},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>y(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>w(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>A(e)},{field:"azioni",headerName:"Azioni",dataType:"text",align:"center",disableFilter:!0,disableSort:!0,renderCell:e=>_(e)},{field:"menu",headerName:"",width:50,disableFilter:!0,disableSort:!0,align:"center",renderCell:e=>(0,s.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>d?.(e,"menu"),children:(0,s.jsx)(eu,{className:"h-4 w-4"})})}];return j&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"center",renderHeader:()=>(0,s.jsx)(x.S,{checked:r.length===v.length&&v.length>0,onCheckedChange:b}),renderCell:e=>(0,s.jsx)(x.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>N(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[j,r,v,b,N]),y=e=>{let a=e.comanda_posa,i=e.comanda_partenza,t=e.comanda_arrivo,r=e.comanda_certificazione,n=a||i||t||r;if(n&&"In corso"===e.stato_installazione)return(0,s.jsx)(m.E,{className:"bg-blue-600 text-white cursor-pointer hover:bg-blue-700",onClick:()=>c?.(e,"view_command",n),children:n});let l=e.stato_installazione||"Da installare";switch(l){case"Installato":return(0,s.jsx)(m.E,{className:"bg-green-100 text-green-800",children:"Installato"});case"In corso":return(0,s.jsx)(m.E,{className:"bg-yellow-100 text-yellow-800",children:"In corso"});case"Da installare":return(0,s.jsx)(m.E,{variant:"outline",children:"Da installare"});default:return(0,s.jsx)(m.E,{variant:"outline",children:l})}},_=e=>e.metri_posati>0?(0,s.jsxs)(l.$,{size:"sm",variant:"outline",onClick:()=>c?.(e,"modify_reel"),className:"text-xs",children:[(0,s.jsx)(eh.A,{className:"h-3 w-3 mr-1"}),"Modifica Bobina"]}):(0,s.jsxs)(l.$,{size:"sm",variant:"outline",onClick:()=>c?.(e,"insert_meters"),className:"text-xs",children:[(0,s.jsx)(ep.A,{className:"h-3 w-3 mr-1"}),"Inserisci Metri"]}),w=e=>{let a,i=e.metri_posati>0||e.metratura_reale>0,t=e.collegamento||e.collegamenti||0;if(!i)return(0,s.jsxs)(l.$,{size:"sm",variant:"outline",disabled:!0,className:"text-xs",children:[(0,s.jsx)(ev.A,{className:"h-3 w-3 mr-1"}),"Non disponibile"]});let r,n,o="outline";switch(t){case 0:r="⚪⚪ Collega cavo",n="connect_cable",a=(0,s.jsx)(eg.A,{className:"h-3 w-3 mr-1"});break;case 1:r="\uD83D\uDFE2⚪ Completa collegamento",n="connect_arrival",a=(0,s.jsx)(eg.A,{className:"h-3 w-3 mr-1"}),o="secondary";break;case 2:r="⚪\uD83D\uDFE2 Completa collegamento",n="connect_departure",a=(0,s.jsx)(eg.A,{className:"h-3 w-3 mr-1"}),o="secondary";break;case 3:r="\uD83D\uDFE2\uD83D\uDFE2 Scollega cavo",n="disconnect_cable",a=(0,s.jsx)(ej.A,{className:"h-3 w-3 mr-1"}),o="default";break;default:r="Gestisci collegamenti",n="manage_connections",a=(0,s.jsx)(eh.A,{className:"h-3 w-3 mr-1"})}return(0,s.jsxs)(l.$,{size:"sm",variant:o,onClick:()=>c?.(e,n),className:"text-xs",children:[a,r]})},A=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?(0,s.jsxs)(l.$,{size:"sm",variant:"default",onClick:()=>c?.(e,"generate_pdf"),className:"text-xs bg-green-600 hover:bg-green-700",children:[(0,s.jsx)(ej.A,{className:"h-3 w-3 mr-1"}),"Genera PDF"]}):(0,s.jsxs)(l.$,{size:"sm",variant:"outline",onClick:()=>c?.(e,"create_certificate"),className:"text-xs",children:[(0,s.jsx)(ef.A,{className:"h-3 w-3 mr-1"}),"Certifica cavo"]}):(0,s.jsxs)(l.$,{size:"sm",variant:"outline",disabled:!0,className:"text-xs",children:[(0,s.jsx)(ev.A,{className:"h-3 w-3 mr-1"}),"Non disponibile"]})};return(0,s.jsxs)("div",{children:[(0,s.jsx)(ex,{cavi:e,onFilteredDataChange:e=>{h(e)},loading:a,selectionEnabled:j,onSelectionToggle:()=>{f(!j)}}),(0,s.jsx)(n.Zp,{className:"mb-4",children:(0,s.jsx)(n.aR,{className:"pb-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(eb.A,{className:"h-5 w-5 text-mariner-600"}),(0,s.jsxs)("span",{children:["Elenco Cavi (",v.length,")"]})]}),j&&r.length>0&&(0,s.jsxs)(m.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[r.length," selezionati"]})]})})}),(0,s.jsx)(el,{data:p,columns:C,loading:a,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{g(e)},renderRow:(e,a)=>{let i=r.includes(e.id_cavo);return(0,s.jsx)(u.Hj,{className:`${i?"bg-mariner-50":""} hover:bg-mariner-50 cursor-pointer border-b border-mariner-100`,onClick:()=>j&&N(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),d?.(e,"context_menu")},children:C.map(a=>(0,s.jsx)(u.nA,{className:`py-2 px-4 ${"center"===a.align?"text-center":"right"===a.align?"text-right":""}`,style:a.cellStyle,onClick:e=>{["stato_installazione","collegamenti","certificato","azioni","menu"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},e.id_cavo)}})]})}var eC=i(43649),ey=i(53411);function e_({cavi:e,filteredCavi:a,className:i}){let r=(0,t.useMemo)(()=>{let i=e.length,s=a.length,t=a.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,r=a.filter(e=>"In corso"===e.stato_installazione).length,n=a.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,l=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=a.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=a.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=a.reduce((e,a)=>e+(a.metri_teorici||0),0),m=a.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0);return{totalCavi:i,filteredCount:s,installati:t,inCorso:r,daInstallare:s-t-r,collegati:n,parzialmenteCollegati:l,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:d>0?m/d*100:0}},[e,a]);return(0,s.jsx)(n.Zp,{className:i,children:(0,s.jsxs)(n.Wu,{className:"p-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eb.A,{className:"h-4 w-4 text-mariner-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-mariner-900",children:r.filteredCount}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["di ",r.totalCavi," cavi"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(ej.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-green-700",children:r.installati}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Installati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(ef.A,{className:"h-4 w-4 text-yellow-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-yellow-700",children:r.inCorso}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"In corso"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eC.A,{className:"h-4 w-4 text-gray-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-gray-700",children:r.daInstallare}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Da installare"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(eg.A,{className:"h-4 w-4 text-blue-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-blue-700",children:r.collegati}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Collegati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(ep.A,{className:"h-4 w-4 text-purple-600"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-purple-700",children:r.certificati}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Certificati"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 md:col-span-2",children:[(0,s.jsx)(ey.A,{className:"h-4 w-4 text-indigo-600"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-semibold text-indigo-700",children:[r.metriInstallati.toLocaleString(),"m"]}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["di ",r.metriTotali.toLocaleString(),"m (",r.percentualeInstallazione.toFixed(1),"%)"]})]})]}),r.parzialmenteCollegati>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"h-4 w-4 flex items-center justify-center",children:(0,s.jsx)("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-yellow-700",children:r.parzialmenteCollegati}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Parziali"})]})]}),r.nonCollegati>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"h-4 w-4 flex items-center justify-center",children:(0,s.jsx)("div",{className:"h-2 w-2 bg-red-500 rounded-full"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-red-700",children:r.nonCollegati}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"Non collegati"})]})]})]}),r.metriTotali>0&&(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground mb-1",children:[(0,s.jsx)("span",{children:"Progresso installazione"}),(0,s.jsxs)("span",{children:[r.percentualeInstallazione.toFixed(1),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-mariner-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.min(r.percentualeInstallazione,100)}%`}})})]})]})})}var ew=i(22126),eA=i(80013),ez=i(41862);function ek({open:e,onClose:a,cavo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,t.useState)(""),[v,g]=(0,t.useState)(""),[j,f]=(0,t.useState)([]),[b,N]=(0,t.useState)(!1),[C,y]=(0,t.useState)(!1),[_,w]=(0,t.useState)(""),A=async()=>{if(!i||!x||!v)return void w("Compilare tutti i campi obbligatori");let e=parseFloat(x);if(isNaN(e)||e<=0)return void w("Inserire un valore valido per i metri posati");if(e>(i.metri_teorici||0))return void w("I metri posati non possono superare i metri teorici");let s=j.find(e=>e.id_bobina===v);if(s&&"BOBINA_VUOTA"!==s.id_bobina&&e>s.metri_residui)return void w(`La bobina selezionata ha solo ${s.metri_residui}m disponibili`);try{if(N(!0),w(""),!m)throw Error("Cantiere non selezionato");await d.At.updateMetriPosati(m.id_cantiere,i.id_cavo,e,"BOBINA_VUOTA"!==v?v:void 0),r(`Metri posati aggiornati con successo per il cavo ${i.id_cavo}: ${e}m`),a()}catch(e){console.error("Errore nel salvataggio:",e),n(e.response?.data?.detail||e.message||"Errore durante il salvataggio dei metri posati")}finally{N(!1)}},z=()=>{b||(u(""),g(""),w(""),a())};return i?(0,s.jsx)(ew.lG,{open:e,onOpenChange:z,children:(0,s.jsxs)(ew.Cf,{className:"sm:max-w-md",children:[(0,s.jsxs)(ew.c7,{children:[(0,s.jsx)(ew.L3,{children:"Inserisci Metri Posati"}),(0,s.jsxs)(ew.rr,{children:["Inserisci i metri posati per il cavo ",i.id_cavo]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Sistema:"})," ",i.sistema]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Utility:"})," ",i.utility]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipologia:"})," ",i.tipologia]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Formazione:"})," ",i.formazione]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Da:"})," ",i.da]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"A:"})," ",i.a]}),(0,s.jsxs)("div",{className:"col-span-2",children:[(0,s.jsx)("strong",{children:"Metri teorici:"})," ",i.metri_teorici,"m"]})]})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"metri",children:"Metri Posati *"}),(0,s.jsx)(p.p,{id:"metri",type:"number",step:"0.1",min:"0",max:i.metri_teorici||0,value:x,onChange:e=>u(e.target.value),placeholder:"Inserisci metri posati"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"bobina",children:"Bobina *"}),C?(0,s.jsxs)("div",{className:"flex items-center space-x-2 p-2",children:[(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Caricamento bobine..."})]}):(0,s.jsxs)(h.l6,{value:v,onValueChange:g,children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{placeholder:"Seleziona bobina"})}),(0,s.jsx)(h.gC,{children:j.map(e=>(0,s.jsx)(h.eb,{value:e.id_bobina,children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{children:e.id_bobina}),"BOBINA_VUOTA"!==e.id_bobina&&(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.metri_residui,"m disponibili",e.fornitore&&` - ${e.fornitore}`]})]})},e.id_bobina))})]})]}),_&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:_})]})]}),(0,s.jsxs)(ew.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:z,disabled:b,children:"Annulla"}),(0,s.jsxs)(l.$,{onClick:A,disabled:b||!x||!v,children:[b&&(0,s.jsx)(ez.A,{className:"mr-2 h-4 w-4 animate-spin"}),b?"Salvando...":"Salva"]})]})]})}):null}function eS({open:e,onClose:a,cavo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,t.useState)(""),[p,v]=(0,t.useState)([]),[g,j]=(0,t.useState)(!1),[f,b]=(0,t.useState)(!1),[N,C]=(0,t.useState)(""),y=async()=>{if(!i||!x)return void C("Selezionare una bobina");if(x===i.id_bobina)return void C("La bobina selezionata \xe8 gi\xe0 associata al cavo");let e=p.find(e=>e.id_bobina===x);if(e&&"BOBINA_VUOTA"!==e.id_bobina&&i.metri_posati>e.metri_residui)return void C(`La bobina selezionata ha solo ${e.metri_residui}m disponibili, ma il cavo ha ${i.metri_posati}m posati`);try{if(j(!0),C(""),!m)throw Error("Cantiere non selezionato");await d.At.updateBobina(m.id_cantiere,i.id_cavo,x);let e="BOBINA_VUOTA"===x?`Bobina vuota assegnata al cavo ${i.id_cavo}`:`Bobina ${x} assegnata al cavo ${i.id_cavo}`;r(e),a()}catch(e){console.error("Errore nel salvataggio:",e),n(e.response?.data?.detail||e.message||"Errore durante la modifica della bobina")}finally{j(!1)}},_=()=>{g||(u(""),C(""),a())};return i?(0,s.jsx)(ew.lG,{open:e,onOpenChange:_,children:(0,s.jsxs)(ew.Cf,{className:"sm:max-w-md",children:[(0,s.jsxs)(ew.c7,{children:[(0,s.jsxs)(ew.L3,{className:"flex items-center space-x-2",children:[(0,s.jsx)(ep.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"Modifica Bobina"})]}),(0,s.jsxs)(ew.rr,{children:["Modifica la bobina associata al cavo ",i.id_cavo]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"p-3 bg-gray-50 rounded-lg",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Sistema:"})," ",i.sistema]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Utility:"})," ",i.utility]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipologia:"})," ",i.tipologia]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Formazione:"})," ",i.formazione]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Metri posati:"})," ",i.metri_posati||0,"m"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Bobina attuale:"})," ",i.id_bobina||"Nessuna"]})]})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"bobina",children:"Nuova Bobina *"}),f?(0,s.jsxs)("div",{className:"flex items-center space-x-2 p-2",children:[(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Caricamento bobine..."})]}):(0,s.jsxs)(h.l6,{value:x,onValueChange:u,children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{placeholder:"Seleziona nuova bobina"})}),(0,s.jsx)(h.gC,{children:p.map(e=>(0,s.jsx)(h.eb,{value:e.id_bobina,disabled:e.id_bobina===i.id_bobina,children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{children:e.id_bobina}),e.id_bobina===i.id_bobina&&(0,s.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-1 rounded",children:"Attuale"})]}),"BOBINA_VUOTA"!==e.id_bobina&&(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.metri_residui,"m disponibili",e.fornitore&&` - ${e.fornitore}`]})]})},e.id_bobina))})]})]}),"BOBINA_VUOTA"===x&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Stai assegnando una bobina vuota. Questo permetter\xe0 di posare il cavo e associare la bobina reale in un secondo momento."})]}),N&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:N})]})]}),(0,s.jsxs)(ew.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:_,disabled:g,children:"Annulla"}),(0,s.jsxs)(l.$,{onClick:y,disabled:g||!x||x===i.id_bobina,children:[g&&(0,s.jsx)(ez.A,{className:"mr-2 h-4 w-4 animate-spin"}),g?"Salvando...":"Salva"]})]})]})}):null}function eE({open:e,onClose:a,cavo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,t.useState)(""),[p,v]=(0,t.useState)([]),[g,j]=(0,t.useState)(!1),[f,b]=(0,t.useState)(!1),[N,C]=(0,t.useState)(""),y=async()=>{if(i&&m)try{j(!0),C(""),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"partenza",x),r(`Collegamento lato partenza completato per il cavo ${i.id_cavo}`),a()}catch(e){console.error("Errore nel collegamento:",e),n(e.response?.data?.detail||e.message||"Errore durante il collegamento")}finally{j(!1)}},_=async()=>{if(i&&m)try{j(!0),C(""),await d.At.collegaCavo(m.id_cantiere,i.id_cavo,"arrivo",x),r(`Collegamento lato arrivo completato per il cavo ${i.id_cavo}`),a()}catch(e){console.error("Errore nel collegamento:",e),n(e.response?.data?.detail||e.message||"Errore durante il collegamento")}finally{j(!1)}},w=async e=>{if(i&&m)try{j(!0),C(""),await d.At.scollegaCavo(m.id_cantiere,i.id_cavo,e);let s=e?` lato ${e}`:"";r(`Scollegamento${s} completato per il cavo ${i.id_cavo}`),a()}catch(e){console.error("Errore nello scollegamento:",e),n(e.response?.data?.detail||e.message||"Errore durante lo scollegamento")}finally{j(!1)}};if(!i)return null;let A=(()=>{if(!i)return{stato:"non_collegato",descrizione:"Non collegato"};switch(i.collegamento||i.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato"}}})(),z=(i.metri_posati||i.metratura_reale||0)>0;return(0,s.jsx)(ew.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(ew.Cf,{className:"sm:max-w-[500px]",children:[(0,s.jsxs)(ew.c7,{children:[(0,s.jsxs)(ew.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eg.A,{className:"h-5 w-5"}),"Gestione Collegamenti - ",i.id_cavo]}),(0,s.jsxs)(ew.rr,{children:["Gestisci i collegamenti del cavo ",i.id_cavo]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Stato Attuale"}),(0,s.jsx)("div",{className:"mt-1 text-lg font-semibold",children:A.descrizione})]}),!z&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Il cavo deve essere installato prima di poter essere collegato."})]}),N&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:N})]}),z&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"responsabile",children:"Responsabile Collegamento"}),(0,s.jsxs)(h.l6,{value:x,onValueChange:u,disabled:f,children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,s.jsx)(h.gC,{children:p.map(e=>(0,s.jsxs)(h.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&` - ${e.numero_telefono}`]},e.id))})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Azioni Disponibili"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:["partenza"!==A.stato&&"completo"!==A.stato&&(0,s.jsxs)(l.$,{onClick:y,disabled:g||!x,className:"w-full",children:[g?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eg.A,{className:"h-4 w-4 mr-2"}),"Collega Partenza"]}),"arrivo"!==A.stato&&"completo"!==A.stato&&(0,s.jsxs)(l.$,{onClick:_,disabled:g||!x,className:"w-full",children:[g?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eg.A,{className:"h-4 w-4 mr-2"}),"Collega Arrivo"]})]}),"non_collegato"!==A.stato&&(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsxs)(l.$,{onClick:()=>w(),disabled:g,variant:"destructive",className:"w-full",children:[g?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(ev.A,{className:"h-4 w-4 mr-2"}),"Scollega Completamente"]})})]})]})]}),(0,s.jsx)(ew.Es,{children:(0,s.jsx)(l.$,{variant:"outline",onClick:a,disabled:g,children:"Chiudi"})})]})})}let eF=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)("textarea",{className:(0,Y.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:i,...a}));eF.displayName="Textarea";var eO=i(86561),eI=i(31158);function eT({open:e,onClose:a,cavo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,t.useState)({responsabile_certificazione:"",data_certificazione:new Date().toISOString().split("T")[0],esito_certificazione:"CONFORME",note_certificazione:""}),[v,g]=(0,t.useState)([]),[j,f]=(0,t.useState)(!1),[b,N]=(0,t.useState)(!1),[C,y]=(0,t.useState)(""),_=async()=>{if(i&&m){if(!x.responsabile_certificazione)return void y("Seleziona un responsabile per la certificazione");try{f(!0),y("");let e={id_cavo:i.id_cavo,responsabile_certificazione:x.responsabile_certificazione,data_certificazione:x.data_certificazione,esito_certificazione:x.esito_certificazione,note_certificazione:x.note_certificazione||null};await d.km.createCertificazione(m.id_cantiere,e),r(`Certificazione completata per il cavo ${i.id_cavo}`),a()}catch(e){console.error("Errore nella certificazione:",e),n(e.response?.data?.detail||e.message||"Errore durante la certificazione")}finally{f(!1)}}},w=async()=>{if(i&&m)try{f(!0),y("");let e=await d.km.generatePDF(m.id_cantiere,i.id_cavo),a=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=a,s.setAttribute("download",`certificato_${i.id_cavo}.pdf`),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(a),r(`PDF certificato generato per il cavo ${i.id_cavo}`)}catch(e){console.error("Errore nella generazione PDF:",e),n(e.response?.data?.detail||e.message||"Errore durante la generazione del PDF")}finally{f(!1)}};if(!i)return null;let A=(i.metri_posati||i.metratura_reale||0)>0,z=!!i&&3===(i.collegamento||i.collegamenti||0),k=!!i&&(!0===i.certificato||"SI"===i.certificato||"CERTIFICATO"===i.certificato);return(0,s.jsx)(ew.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(ew.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(ew.c7,{children:[(0,s.jsxs)(ew.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eO.A,{className:"h-5 w-5"}),"Gestione Certificazione - ",i.id_cavo]}),(0,s.jsxs)(ew.rr,{children:["Certifica il cavo ",i.id_cavo," o genera il PDF del certificato"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Stato Cavo"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:`w-3 h-3 rounded-full ${A?"bg-green-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:"text-sm",children:A?"Installato":"Non installato"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:`w-3 h-3 rounded-full ${z?"bg-green-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:"text-sm",children:z?"Collegato":"Non collegato"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:`w-3 h-3 rounded-full ${k?"bg-green-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:"text-sm",children:k?"Certificato":"Non certificato"})]})]})]}),!A&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Il cavo deve essere installato prima di poter essere certificato."})]}),!z&&A&&(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Il cavo deve essere completamente collegato prima di poter essere certificato."})]}),C&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:C})]}),k?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(eO.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Questo cavo \xe8 gi\xe0 stato certificato. Puoi generare il PDF del certificato."})]}),(0,s.jsxs)(l.$,{onClick:w,disabled:j,className:"w-full",children:[j?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eI.A,{className:"h-4 w-4 mr-2"}),"Genera PDF Certificato"]})]}):A&&z&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"responsabile",children:"Responsabile Certificazione *"}),(0,s.jsxs)(h.l6,{value:x.responsabile_certificazione,onValueChange:e=>u(a=>({...a,responsabile_certificazione:e})),disabled:b,children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,s.jsx)(h.gC,{children:v.map(e=>(0,s.jsxs)(h.eb,{value:e.nome_responsabile,children:[e.nome_responsabile,e.numero_telefono&&` - ${e.numero_telefono}`]},e.id))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"data",children:"Data Certificazione"}),(0,s.jsx)(p.p,{id:"data",type:"date",value:x.data_certificazione,onChange:e=>u(a=>({...a,data_certificazione:e.target.value}))})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"esito",children:"Esito Certificazione"}),(0,s.jsxs)(h.l6,{value:x.esito_certificazione,onValueChange:e=>u(a=>({...a,esito_certificazione:e})),children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"CONFORME",children:"CONFORME"}),(0,s.jsx)(h.eb,{value:"NON_CONFORME",children:"NON CONFORME"}),(0,s.jsx)(h.eb,{value:"CONFORME_CON_RISERVA",children:"CONFORME CON RISERVA"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,s.jsx)(eF,{id:"note",placeholder:"Inserisci eventuali note sulla certificazione...",value:x.note_certificazione,onChange:e=>u(a=>({...a,note_certificazione:e.target.value})),rows:3})]}),(0,s.jsxs)(l.$,{onClick:_,disabled:j||!x.responsabile_certificazione,className:"w-full",children:[j?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eO.A,{className:"h-4 w-4 mr-2"}),"Certifica Cavo"]})]})]}),(0,s.jsx)(ew.Es,{children:(0,s.jsx)(l.$,{variant:"outline",onClick:a,disabled:j,children:"Chiudi"})})]})})}var e$=i(6727),eR=i(41312);function eP({open:e,onClose:a,caviSelezionati:i,tipoComanda:r,onSuccess:n,onError:m}){let{cantiere:x}=(0,c.A)(),[u,p]=(0,t.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[v,g]=(0,t.useState)([]),[j,f]=(0,t.useState)(!1),[b,N]=(0,t.useState)(!1),[C,y]=(0,t.useState)(""),_=async()=>{if(x){if(!u.responsabile)return void y("Seleziona un responsabile per la comanda");if(0===i.length)return void y("Seleziona almeno un cavo per la comanda");try{f(!0),y("");let e={tipo_comanda:u.tipo_comanda,responsabile:u.responsabile,note:u.note||null},s=await d.CV.createComandaWithCavi(x.id_cantiere,e,i);n(`Comanda ${s.data.codice_comanda} creata con successo per ${i.length} cavi`),a()}catch(e){console.error("Errore nella creazione comanda:",e),m(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{f(!1)}}};return(0,s.jsx)(ew.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(ew.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(ew.c7,{children:[(0,s.jsxs)(ew.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(e$.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,s.jsxs)(ew.rr,{children:["Crea una nuova comanda per ",i.length," cavi selezionati"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)(eA.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",i.length,")"]}),(0,s.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[i.slice(0,10).map(e=>(0,s.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),i.length>10&&(0,s.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",i.length-10," altri..."]})]})})]}),C&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:C})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,s.jsxs)(h.l6,{value:u.tipo_comanda,onValueChange:e=>p(a=>({...a,tipo_comanda:e})),children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{})}),(0,s.jsxs)(h.gC,{children:[(0,s.jsx)(h.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,s.jsx)(h.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,s.jsx)(h.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,s.jsx)(h.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,s.jsxs)(h.l6,{value:u.responsabile,onValueChange:e=>p(a=>({...a,responsabile:e})),disabled:b,children:[(0,s.jsx)(h.bq,{children:(0,s.jsx)(h.yv,{placeholder:"Seleziona responsabile..."})}),(0,s.jsx)(h.gC,{children:v.map(e=>(0,s.jsx)(h.eb,{value:e.nome_responsabile,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,s.jsx)(eF,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:u.note,onChange:e=>p(a=>({...a,note:e.target.value})),rows:3})]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(u.tipo_comanda)]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Responsabile:"})," ",u.responsabile||"Non selezionato"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cavi:"})," ",i.length," selezionati"]}),u.note&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Note:"})," ",u.note]})]})]})]}),(0,s.jsxs)(ew.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:a,disabled:j,children:"Annulla"}),(0,s.jsxs)(l.$,{onClick:_,disabled:j||!u.responsabile||0===i.length,children:[j?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(e$.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var eM=i(16023);let eD=(0,ea.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);function eL({open:e,onClose:a,tipo:i,onSuccess:r,onError:n}){let{cantiere:m}=(0,c.A)(),[x,u]=(0,t.useState)(null),[h,v]=(0,t.useState)(""),[g,j]=(0,t.useState)(!1),[f,b]=(0,t.useState)(""),[N,C]=(0,t.useState)(0),y=(0,t.useRef)(null),_=async()=>{if(x&&m){if("cavi"===i&&!h.trim())return void b("Inserisci il codice revisione per l'importazione cavi");try{let e;if(j(!0),b(""),C(0),e="cavi"===i?await d.mg.importCavi(m.id_cantiere,x,h.trim()):await d.mg.importBobine(m.id_cantiere,x),C(100),e.data.success){let s=e.data.details,t=e.data.message;"cavi"===i&&s?.cavi_importati?t+=` (${s.cavi_importati} cavi importati)`:"bobine"===i&&s?.bobine_importate&&(t+=` (${s.bobine_importate} bobine importate)`),r(t),a()}else n(e.data.message||"Errore durante l'importazione")}catch(e){console.error("Errore nell'importazione:",e),n(e.response?.data?.detail||e.message||"Errore durante l'importazione del file")}finally{j(!1),C(0)}}},w=()=>{g||(u(null),v(""),b(""),C(0),y.current&&(y.current.value=""),a())},A=()=>"cavi"===i?"Cavi":"Bobine";return(0,s.jsx)(ew.lG,{open:e,onOpenChange:w,children:(0,s.jsxs)(ew.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(ew.c7,{children:[(0,s.jsxs)(ew.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eM.A,{className:"h-5 w-5"}),"Importa ",A()," da Excel"]}),(0,s.jsxs)(ew.rr,{children:["Carica un file Excel per importare ",A().toLowerCase()," nel cantiere"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,s.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===i?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,s.jsxs)("li",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,s.jsx)("span",{children:e})]},a))})]}),f&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:f})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"file",children:"File Excel *"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.p,{ref:y,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{let a=e.target.files?.[0];if(a){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(a.type)&&!a.name.toLowerCase().endsWith(".xlsx")&&!a.name.toLowerCase().endsWith(".xls"))return void b("Seleziona un file Excel valido (.xlsx o .xls)");u(a),b("")}},disabled:g,className:"flex-1"}),x&&(0,s.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,s.jsx)(ej.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),x&&(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsx)(eD,{className:"h-4 w-4 inline mr-1"}),x.name," (",(x.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===i&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(eA.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,s.jsx)(p.p,{id:"revisione",value:h,onChange:e=>v(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:g}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),g&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${N}%`}})})]}),x&&(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Tipo:"})," ",A()]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"File:"})," ",x.name]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Dimensione:"})," ",(x.size/1024/1024).toFixed(2)," MB"]}),"cavi"===i&&h&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Revisione:"})," ",h]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cantiere:"})," ",m?.nome_cantiere]})]})]})]}),(0,s.jsxs)(ew.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:w,disabled:g,children:"Annulla"}),(0,s.jsxs)(l.$,{onClick:_,disabled:g||!x||"cavi"===i&&!h.trim(),children:[g?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eM.A,{className:"h-4 w-4 mr-2"}),"Importa ",A()]})]})]})})}var eq=i(61611);function eB({open:e,onClose:a,onSuccess:i,onError:r}){let{cantiere:n}=(0,c.A)(),[m,u]=(0,t.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[p,h]=(0,t.useState)(!1),[v,g]=(0,t.useState)(""),j=(e,a)=>{u(i=>({...i,[e]:a}))},f=async()=>{if(n)try{h(!0);let e=await d.mg.exportCavi(n.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=a,s.setAttribute("download",`cavi_${n.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(a),i("Export cavi completato con successo")}catch(e){console.error("Errore nell'export cavi:",e),r(e.response?.data?.detail||e.message||"Errore durante l'export dei cavi")}finally{h(!1)}},b=async()=>{if(n)try{h(!0);let e=await d.mg.exportBobine(n.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),s=document.createElement("a");s.href=a,s.setAttribute("download",`bobine_${n.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(s),s.click(),s.remove(),window.URL.revokeObjectURL(a),i("Export bobine completato con successo")}catch(e){console.error("Errore nell'export bobine:",e),r(e.response?.data?.detail||e.message||"Errore durante l'export delle bobine")}finally{h(!1)}},N=async()=>{if(n)try{h(!0),g("");let e=[];m.cavi&&e.push(f()),m.bobine&&e.push(b()),m.comande&&console.log("Export comande - da implementare"),m.certificazioni&&console.log("Export certificazioni - da implementare"),m.responsabili&&console.log("Export responsabili - da implementare"),await Promise.all(e);let s=Object.values(m).filter(Boolean).length;i(`Export completato: ${s} file scaricati`),a()}catch(e){console.error("Errore nell'export:",e),r(e.response?.data?.detail||e.message||"Errore durante l'export dei dati")}finally{h(!1)}},C=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,s.jsx)(eq.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,s.jsx)(eD,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,s.jsx)(eD,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,s.jsx)(eD,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,s.jsx)(eD,{className:"h-4 w-4"}),available:!1}];return(0,s.jsx)(ew.lG,{open:e,onOpenChange:a,children:(0,s.jsxs)(ew.Cf,{className:"sm:max-w-[600px]",children:[(0,s.jsxs)(ew.c7,{children:[(0,s.jsxs)(ew.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(eI.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,s.jsxs)(ew.rr,{children:["Seleziona i dati da esportare dal cantiere ",n?.nome_cantiere]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[v&&(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:v})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),C.map(e=>(0,s.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border ${e.available?"bg-white":"bg-gray-50"}`,children:[(0,s.jsx)(x.S,{id:e.key,checked:m[e.key],onCheckedChange:a=>j(e.key,a),disabled:!e.available||p}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,s.jsxs)(eA.J,{htmlFor:e.key,className:`font-medium ${!e.available?"text-gray-500":""}`,children:[e.label,!e.available&&(0,s.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,s.jsx)("p",{className:`text-sm mt-1 ${!e.available?"text-gray-400":"text-gray-600"}`,children:e.description})]})]},e.key))]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,s.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,s.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,s.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,s.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,s.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(m).filter(Boolean).length>0&&(0,s.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,s.jsx)(eA.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cantiere:"})," ",n?.nome_cantiere]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(m).filter(Boolean).length]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,s.jsxs)(ew.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:a,disabled:p,children:"Annulla"}),(0,s.jsxs)(l.$,{onClick:N,disabled:p||0===Object.values(m).filter(Boolean).length,children:[p?(0,s.jsx)(ez.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(eI.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(m).filter(Boolean).length>0?`(${Object.values(m).filter(Boolean).length})`:""]})]})]})})}function eV(){let{user:e,cantiere:a,isAuthenticated:i,isLoading:m}=(0,c.A)();(0,r.useRouter)();let x=({title:e,description:a,variant:i})=>{console.log(`${"destructive"===i?"❌":"✅"} ${e}: ${a}`)},[u,p]=(0,t.useState)([]),[h,v]=(0,t.useState)([]),[g,j]=(0,t.useState)(!0),[f,b]=(0,t.useState)(""),[N,C]=(0,t.useState)([]),[y,_]=(0,t.useState)(!1),[w,A]=(0,t.useState)([]),[z,k]=(0,t.useState)({open:!1,cavo:null}),[S,E]=(0,t.useState)({open:!1,cavo:null}),[F,O]=(0,t.useState)({open:!1,cavo:null}),[I,T]=(0,t.useState)({open:!1,cavo:null}),[$,R]=(0,t.useState)({open:!1}),[P,M]=(0,t.useState)({open:!1}),[D,L]=(0,t.useState)(!1),[q,B]=(0,t.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),[V,U]=(0,t.useState)(0),G=async()=>{try{j(!0),b(""),console.log("\uD83D\uDD0D Tentativo caricamento cavi per cantiere:",V),console.log("\uD83D\uDD0D Token presente:",!!localStorage.getItem("token"));try{let e=await d.At.getCavi(V);console.log("✅ API normale riuscita, cavi ricevuti:",e?.length||0);let a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);p(a),v(i),J(a)}catch(e){console.log("❌ API normale fallita, provo endpoint debug..."),console.error("Errore API normale:",e);try{let e=await fetch(`http://localhost:8001/api/cavi/debug/${V}`),a=await e.json();if(console.log("✅ Endpoint debug riuscito:",a),a.cavi&&Array.isArray(a.cavi)){let e=a.cavi.filter(e=>!e.spare),i=a.cavi.filter(e=>e.spare);p(e),v(i),J(e),b("⚠️ Dati caricati tramite endpoint debug (problema autenticazione)")}else throw Error("Formato dati debug non valido")}catch(a){throw console.error("❌ Anche endpoint debug fallito:",a),e}}}catch(e){console.error("❌ Errore generale nel caricamento cavi:",e),b(`Errore nel caricamento dei cavi: ${e.response?.data?.detail||e.message}`)}finally{j(!1)}},J=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,s=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,t=e.filter(e=>e.certificato).length,r=e.reduce((e,a)=>e+(a.metri_teorici||0),0),n=e.reduce((e,a)=>e+(a.metri_posati||0),0);B({totali:a,installati:i,collegati:s,certificati:t,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(s/a*100):0,percentualeCertificazione:a>0?Math.round(t/a*100):0,metriTotali:r,metriInstallati:n,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},Z=(e,a,i)=>{switch(console.log("Status action:",a,"for cavo:",e.id_cavo,"label:",i),a){case"insert_meters":k({open:!0,cavo:e});break;case"modify_reel":E({open:!0,cavo:e});break;case"view_command":x({title:"Visualizza Comanda",description:`Apertura comanda ${i} per cavo ${e.id_cavo}`});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"disconnect_cable":case"manage_connections":O({open:!0,cavo:e});break;case"create_certificate":case"generate_pdf":T({open:!0,cavo:e})}},W=(e,a)=>{switch(console.log("Context menu action:",a,"for cavo:",e.id_cavo),a){case"view_details":x({title:"Visualizza Dettagli",description:`Apertura dettagli per cavo ${e.id_cavo}`});break;case"edit":x({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":x({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":x({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":N.includes(e.id_cavo)?(C(N.filter(a=>a!==e.id_cavo)),x({title:"Cavo Deselezionato",description:`Cavo ${e.id_cavo} deselezionato`})):(C([...N,e.id_cavo]),x({title:"Cavo Selezionato",description:`Cavo ${e.id_cavo} selezionato`}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),x({title:"ID Copiato",description:`ID cavo ${e.id_cavo} copiato negli appunti`});break;case"copy_details":let i=`ID: ${e.id_cavo}, Tipologia: ${e.tipologia}, Formazione: ${e.formazione||e.sezione}, Metri: ${e.metri_teorici}`;navigator.clipboard.writeText(i),x({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":x({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":x({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":R({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":R({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":R({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":R({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":x({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":x({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:x({title:"Azione non implementata",description:`Azione ${a} non ancora implementata`})}},H=e=>{x({title:"Operazione completata",description:e}),G()},K=e=>{x({title:"Errore",description:e,variant:"destructive"})};return m||g?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(ez.A,{className:"h-8 w-8 animate-spin"})}):V?f?(0,s.jsxs)("div",{className:"container mx-auto p-6",children:[(0,s.jsxs)(o.Fc,{variant:"destructive",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:f})]}),(0,s.jsx)(l.$,{onClick:G,className:"mt-4",children:"Riprova"})]}):(0,s.jsxs)("div",{className:"container mx-auto p-6",children:[(0,s.jsx)(e_,{cavi:u,filteredCavi:w,className:"mb-6"}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(eN,{cavi:u,loading:g,selectionEnabled:y,selectedCavi:N,onSelectionChange:C,onStatusAction:Z,onContextMenuAction:W})}),h.length>0&&(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,s.jsx)(ep.A,{className:"h-5 w-5"}),(0,s.jsxs)("span",{children:["Cavi Spare (",h.length,")"]})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)(eN,{cavi:h,loading:g,selectionEnabled:!1,onStatusAction:Z,onContextMenuAction:W})})]})}),!1,(0,s.jsx)(ek,{open:z.open,onClose:()=>k({open:!1,cavo:null}),cavo:z.cavo,onSuccess:H,onError:K}),(0,s.jsx)(eS,{open:S.open,onClose:()=>E({open:!1,cavo:null}),cavo:S.cavo,onSuccess:H,onError:K}),(0,s.jsx)(eE,{open:F.open,onClose:()=>O({open:!1,cavo:null}),cavo:F.cavo,onSuccess:H,onError:K}),(0,s.jsx)(eT,{open:I.open,onClose:()=>T({open:!1,cavo:null}),cavo:I.cavo,onSuccess:H,onError:K}),(0,s.jsx)(eP,{open:$.open,onClose:()=>R({open:!1}),caviSelezionati:N,tipoComanda:$.tipoComanda,onSuccess:H,onError:K}),(0,s.jsx)(eL,{open:P.open,onClose:()=>M({open:!1}),tipo:P.tipo||"cavi",onSuccess:H,onError:K}),(0,s.jsx)(eB,{open:D,onClose:()=>L(!1),onSuccess:H,onError:K})]}):(0,s.jsxs)("div",{className:"container mx-auto p-6",children:[(0,s.jsxs)(o.Fc,{children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:"Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i cavi."})]}),(0,s.jsxs)("div",{className:"mt-4 p-4 bg-gray-100 rounded",children:[(0,s.jsx)("h3",{className:"font-bold",children:"Debug Info:"}),(0,s.jsxs)("p",{children:["User: ",e?e.username:"Non autenticato"]}),(0,s.jsxs)("p",{children:["Cantiere context: ",a?a.commessa:"Nessuno"]}),(0,s.jsxs)("p",{children:["Token presente: ","N/A"]})]})]})}},52270:(e,a,i)=>{"use strict";i.r(a),i.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=i(65239),t=i(48088),r=i(88170),n=i.n(r),l=i(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);i.d(a,o);let c={children:["",{children:["cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,10698)),"C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx"],m={require:i,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/cavi/page",pathname:"/cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,i)=>{"use strict";i.d(a,{S:()=>l});var s=i(60687);i(43210);var t=i(40211),r=i(13964),n=i(4780);function l({className:e,...a}){return(0,s.jsx)(t.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,s.jsx)(t.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(r.A,{className:"size-3.5"})})})}},58235:(e,a,i)=>{Promise.resolve().then(i.bind(i,10698))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86561:(e,a,i)=>{"use strict";i.d(a,{A:()=>s});let s=(0,i(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},91821:(e,a,i)=>{"use strict";i.d(a,{Fc:()=>o,TN:()=>c});var s=i(60687),t=i(43210),r=i(24224),n=i(4780);let l=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=t.forwardRef(({className:e,variant:a,...i},t)=>(0,s.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(l({variant:a}),e),...i}));o.displayName="Alert",t.forwardRef(({className:e,...a},i)=>(0,s.jsx)("h5",{ref:i,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...a})).displayName="AlertTitle";let c=t.forwardRef(({className:e,...a},i)=>(0,s.jsx)("div",{ref:i,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...a}));c.displayName="AlertDescription"},94735:e=>{"use strict";e.exports=require("events")},96834:(e,a,i)=>{"use strict";i.d(a,{E:()=>o});var s=i(60687);i(43210);var t=i(8730),r=i(24224),n=i(4780);let l=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,asChild:i=!1,...r}){let o=i?t.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:a}),e),...r})}}};var a=require("../../webpack-runtime.js");a.C(e);var i=e=>a(a.s=e),s=a.X(0,[447,538,658,952,146,892,615,868],()=>i(52270));module.exports=s})();