#!/bin/bash
# run_system.sh - Script shell per avviare webapp-nextjs su Unix/Linux

echo ""
echo "=== Avvio del sistema CABLYS NEXT.JS WEBAPP ==="
echo ""

# Verifica che Python sia installato
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "Errore: Python non trovato. Installare Python prima di continuare."
    exit 1
fi

# Verifica che Node.js sia installato
if ! command -v node &> /dev/null; then
    echo "Errore: Node.js non trovato. Installare Node.js prima di continuare."
    exit 1
fi

# Verifica che npm sia installato
if ! command -v npm &> /dev/null; then
    echo "Errore: npm non trovato. Installare npm prima di continuare."
    exit 1
fi

# Determina quale comando Python usare
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

# Esegui lo script Python
$PYTHON_CMD run_system.py
