(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{17313:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>n});var s=t(95155),i=t(12115),r=t(30064),l=t(59434);let n=r.bL,d=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,s.jsx)(r.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...i})});d.displayName=r.B8.displayName;let o=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,s.jsx)(r.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...i})});o.displayName=r.l9.displayName;let c=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,s.jsx)(r.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...i})});c.displayName=r.UC.displayName},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>d});var s=t(95155);t(12115);var i=t(99708),r=t(74466),l=t(59434);let n=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:t,asChild:r=!1,...d}=e,o=r?i.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(n({variant:t}),a),...d})}},27883:(e,a,t)=>{"use strict";t.d(a,{Qi:()=>u,eU:()=>x,jn:()=>d,k7:()=>c,rp:()=>m,tA:()=>o});var s=t(95155),i=t(12115),r=t(59434),l=t(51154);let n=i.forwardRef((e,a)=>{let{className:t,variant:i="primary",size:n="md",loading:d=!1,glow:o=!1,icon:c,children:u,disabled:m,...x}=e,h=m||d;return(0,s.jsxs)("button",{className:(0,r.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[i],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[n],o&&"quick"!==i&&"btn-glow",h&&"opacity-50 cursor-not-allowed hover:shadow-none",t),disabled:h,ref:a,...x,children:[(0,s.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,s.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[d?(0,s.jsx)(l.A,{className:"h-4 w-4 animate-spin btn-icon"}):c?(0,s.jsx)("span",{className:"btn-icon",children:c}):null,u]})]})});n.displayName="AnimatedButton";let d=e=>(0,s.jsx)(n,{variant:"primary",...e}),o=e=>(0,s.jsx)(n,{variant:"secondary",...e}),c=e=>(0,s.jsx)(n,{variant:"success",...e}),u=e=>(0,s.jsx)(n,{variant:"danger",...e}),m=e=>(0,s.jsx)(n,{variant:"outline",...e}),x=e=>(0,s.jsx)(n,{variant:"quick",...e})},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>d});var s=t(95155);t(12115);var i=t(99708),r=t(74466),l=t(59434);let n=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:a,variant:t,size:r,asChild:d=!1,...o}=e,c=d?i.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:t,size:r,className:a})),...o})}},42221:(e,a,t)=>{Promise.resolve().then(t.bind(t,83245))},47262:(e,a,t)=>{"use strict";t.d(a,{S:()=>n});var s=t(95155);t(12115);var i=t(76981),r=t(5196),l=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)(i.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(r.A,{className:"size-3.5"})})})}},57434:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>u,eb:()=>x,gC:()=>m,l6:()=>o,yv:()=>c});var s=t(95155);t(12115);var i=t(38715),r=t(66474),l=t(5196),n=t(47863),d=t(59434);function o(e){let{...a}=e;return(0,s.jsx)(i.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,s.jsx)(i.WT,{"data-slot":"select-value",...a})}function u(e){let{className:a,size:t="default",children:l,...n}=e;return(0,s.jsxs)(i.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[l,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:a,children:t,position:r="popper",...l}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:r,...l,children:[(0,s.jsx)(h,{}),(0,s.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(p,{})]})})}function x(e){let{className:a,children:t,...r}=e;return(0,s.jsxs)(i.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsx)(i.p4,{children:t})]})}function h(e){let{className:a,...t}=e;return(0,s.jsx)(i.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}function p(e){let{className:a,...t}=e;return(0,s.jsx)(i.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...t,children:(0,s.jsx)(r.A,{className:"size-4"})})}},59434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>r});var s=t(52596),i=t(39688);function r(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,i.QP)((0,s.$)(a))}},61610:(e,a,t)=>{"use strict";t.d(a,{Eb:()=>h,GN:()=>p,TU:()=>n});let s=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,i=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,r=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(s,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},o=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:255;return l(e).length>a?{isValid:!1,error:"Testo troppo lungo (max ".concat(a," caratteri)")}:r.test(e)||i.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0}},u=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},m=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},x=new Map,h=(e,a,t)=>{let s=Date.now(),i=x.get(e);return!i||s>i.resetTime?(x.set(e,{count:1,resetTime:s+t}),!0):!(i.count>=a)&&(i.count++,!0)},p=e=>{let a={},t=n(e.username);if(t.isValid||(a.username=t.error),e.password){let t=d(e.password);t.isValid||(a.password=t.error)}let s=u(e.ragione_sociale);if(s.isValid||(a.ragione_sociale=s.error),e.email){let t=o(e.email);t.isValid||(a.email=t.error)}if(e.vat){let t=m(e.vat);t.isValid||(a.vat=t.error)}if(e.indirizzo){let t=c(e.indirizzo,200);t.isValid||(a.indirizzo=t.error)}if(e.nazione){let t=c(e.nazione,50);t.isValid||(a.nazione=t.error)}if(e.referente_aziendale){let t=c(e.referente_aziendale,100);t.isValid||(a.referente_aziendale=t.error)}return{isValid:0===Object.keys(a).length,errors:a}}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>r});var s=t(95155);t(12115);var i=t(59434);function r(e){let{className:a,type:t,...r}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...r})}},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>n,Zp:()=>r,aR:()=>l});var s=t(95155);t(12115);var i=t(59434);function r(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",a),...t})}},78749:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},83245:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>H});var s=t(95155),i=t(12115),r=t(66695),l=t(27883),n=t(13717),d=t(14186),o=t(40646),c=t(62525);function u(e){let{user:a,onEdit:t,onToggleStatus:i,onDelete:r}=e;return(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("Edit clicked for user:",a.username),t()},type:"button",className:"p-1.5 rounded hover:bg-blue-50 transition-colors",title:"Modifica utente",children:(0,s.jsx)(n.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("Toggle status clicked for user:",a.username),i()},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded transition-colors ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-slate-50"),title:a.abilitato?"Disabilita utente":"Abilita utente",children:a.abilitato?(0,s.jsx)(d.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,s.jsx)(o.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),(0,s.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),console.log("Delete clicked for user:",a.username),r()},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded transition-colors ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:bg-red-50"),title:"Elimina utente",children:(0,s.jsx)(c.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})})]})}var m=t(26126),x=t(85127),h=t(17313),p=t(40283),g=t(35695),v=t(25731),b=t(30285),j=t(62523),f=t(85057),N=t(59409),y=t(47262),w=t(61610),z=t(78749),A=t(92657),k=t(54416),E=t(4229);function C(e){let{user:a,onSave:t,onCancel:n}=e,[d,o]=(0,i.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[c,u]=(0,i.useState)({}),[m,x]=(0,i.useState)(!1),[h,p]=(0,i.useState)(""),[g,C]=(0,i.useState)(!1);(0,i.useEffect)(()=>{a&&o({username:a.username||"",password:"",ruolo:a.ruolo||"user",data_scadenza:a.data_scadenza?a.data_scadenza.split("T")[0]:"",abilitato:void 0===a.abilitato||a.abilitato,ragione_sociale:a.ragione_sociale||"",indirizzo:a.indirizzo||"",nazione:a.nazione||"",email:a.email||"",vat:a.vat||"",referente_aziendale:a.referente_aziendale||""})},[a]);let _=(e,a)=>{o(t=>({...t,[e]:a})),c[e]&&u(a=>({...a,[e]:""}))},S=()=>{let e=(0,w.GN)({username:d.username,password:a?void 0:d.password,ragione_sociale:d.ragione_sociale,email:d.email,vat:d.vat,indirizzo:d.indirizzo,nazione:d.nazione,referente_aziendale:d.referente_aziendale});return u(e.errors),e.isValid},T=async e=>{e.preventDefault();let s="user-form-".concat((null==a?void 0:a.id_utente)||"new","-").concat(Date.now());if(!(0,w.Eb)(s,5,6e4))return void p("Troppi tentativi. Riprova tra un minuto.");if(S()){x(!0),p("");try{let e,s={...d};a||(s.ruolo="user"),a&&!s.password.trim()&&delete s.password,s.data_scadenza&&(s.data_scadenza=s.data_scadenza),e=a?await v.dG.updateUser(a.id_utente,s):await v.dG.createUser(s),t(e)}catch(e){var i,r;p((null==(r=e.response)||null==(i=r.data)?void 0:i.detail)||e.message||"Errore durante il salvataggio dell'utente")}finally{x(!1)}}};return(0,s.jsxs)(r.Zp,{children:[(0,s.jsx)(r.aR,{children:(0,s.jsx)(r.ZB,{children:a?"Modifica Utente: ".concat(a.username):"Crea Nuovo Utente Standard"})}),(0,s.jsxs)(r.Wu,{children:[h&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-600",children:h})}),(0,s.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"username",children:"Username *"}),(0,s.jsx)(j.p,{id:"username",value:d.username,onChange:e=>_("username",e.target.value),disabled:m,className:c.username?"border-red-500":""}),c.username&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.username})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"password",children:a?"Nuova Password (lascia vuoto per non modificare)":"Password *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(j.p,{id:"password",type:g?"text":"password",value:d.password,onChange:e=>_("password",e.target.value),disabled:m,className:c.password?"border-red-500 pr-10":"pr-10"}),(0,s.jsx)(b.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C(!g),disabled:m,children:g?(0,s.jsx)(z.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(A.A,{className:"h-4 w-4 text-gray-400"})})]}),c.password&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.password})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"ragione_sociale",children:"Ragione Sociale *"}),(0,s.jsx)(j.p,{id:"ragione_sociale",value:d.ragione_sociale,onChange:e=>_("ragione_sociale",e.target.value),disabled:m,className:c.ragione_sociale?"border-red-500":""}),c.ragione_sociale&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.ragione_sociale})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(j.p,{id:"email",type:"email",value:d.email,onChange:e=>_("email",e.target.value),disabled:m,className:c.email?"border-red-500":""}),c.email&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:c.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"indirizzo",children:"Indirizzo"}),(0,s.jsx)(j.p,{id:"indirizzo",value:d.indirizzo,onChange:e=>_("indirizzo",e.target.value),disabled:m})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"nazione",children:"Nazione"}),(0,s.jsx)(j.p,{id:"nazione",value:d.nazione,onChange:e=>_("nazione",e.target.value),disabled:m})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"vat",children:"VAT"}),(0,s.jsx)(j.p,{id:"vat",value:d.vat,onChange:e=>_("vat",e.target.value),disabled:m})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,s.jsx)(j.p,{id:"referente_aziendale",value:d.referente_aziendale,onChange:e=>_("referente_aziendale",e.target.value),disabled:m})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,s.jsx)(j.p,{id:"data_scadenza",type:"date",value:d.data_scadenza,onChange:e=>_("data_scadenza",e.target.value),disabled:m})]}),a?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsxs)(N.l6,{value:d.ruolo,onValueChange:e=>_("ruolo",e),disabled:m,children:[(0,s.jsx)(N.bq,{children:(0,s.jsx)(N.yv,{})}),(0,s.jsxs)(N.gC,{children:[(0,s.jsx)(N.eb,{value:"user",children:"User"}),(0,s.jsx)(N.eb,{value:"cantieri_user",children:"Cantieri User"})]})]})]}):(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(f.J,{htmlFor:"ruolo",children:"Ruolo"}),(0,s.jsx)("div",{className:"px-3 py-2 bg-slate-50 border border-slate-200 rounded-md text-sm text-slate-600",children:"User (Standard)"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(y.S,{id:"abilitato",checked:d.abilitato,onCheckedChange:e=>_("abilitato",e),disabled:m||a&&"owner"===a.ruolo}),(0,s.jsx)(f.J,{htmlFor:"abilitato",children:"Utente abilitato"})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,s.jsx)(l.tA,{type:"button",onClick:n,disabled:m,icon:(0,s.jsx)(k.A,{className:"h-4 w-4"}),children:"Annulla"}),(0,s.jsx)(l.jn,{type:"submit",loading:m,icon:(0,s.jsx)(E.A,{className:"h-4 w-4"}),glow:!0,children:m?"Salvataggio...":"Salva"})]})]})]})]})}var _=t(54213),S=t(53904),T=t(51154);function R(){let[e,a]=(0,i.useState)(null),[t,n]=(0,i.useState)(!1),[d,o]=(0,i.useState)(""),c=async()=>{n(!0),o("");try{console.log("Caricamento dati database raw...");let e=await v.dG.getDatabaseData();console.log("Dati ricevuti:",e),a(e)}catch(a){var e,t;console.error("Errore durante il caricamento:",a),o((null==(t=a.response)||null==(e=t.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati del database")}finally{n(!1)}};(0,i.useEffect)(()=>{c()},[]);let u=(e,a,t)=>{if(!a||0===a.length)return(0,s.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",t]});let i=Object.keys(a[0]);return(0,s.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,s.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900",children:t}),(0,s.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,s.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,s.jsxs)(x.XI,{children:[(0,s.jsx)(x.A0,{className:"sticky top-0 bg-slate-50",children:(0,s.jsx)(x.Hj,{children:i.map(e=>(0,s.jsx)(x.nd,{className:"font-medium",children:e},e))})}),(0,s.jsx)(x.BF,{children:a.map((e,a)=>(0,s.jsx)(x.Hj,{children:i.map(a=>(0,s.jsx)(x.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,s.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},m=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,s.jsxs)(r.Zp,{children:[(0,s.jsx)(r.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(_.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,s.jsx)(l.jn,{size:"sm",onClick:c,loading:t,icon:(0,s.jsx)(S.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,s.jsxs)(r.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(A.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),t?(0,s.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,s.jsx)(T.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,s.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):d?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,s.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,s.jsx)("p",{className:"text-red-600",children:d})]}):e?(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),m.map(a=>e[a.key]&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),u(a.key,e[a.key],a.title)]},a.key)),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:m.map(a=>(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,s.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,s.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var D=t(19946);let V=(0,D.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var U=t(1243);function I(){let[e,a]=(0,i.useState)(""),[t,n]=(0,i.useState)(!1),[d,o]=(0,i.useState)(!1),[u,m]=(0,i.useState)(""),[x,h]=(0,i.useState)(""),p=async()=>{if("RESET DATABASE"!==e||!t)return void m("Conferma richiesta per procedere con il reset");o(!0),m(""),h("");try{await v.dG.resetDatabase(),h("Database resettato con successo! Tutti i dati sono stati eliminati."),a(""),n(!1)}catch(e){var s,i;m((null==(i=e.response)||null==(s=i.data)?void 0:s.detail)||e.message||"Errore durante il reset del database")}finally{o(!1)}},g="RESET DATABASE"===e&&t&&!d;return(0,s.jsxs)(r.Zp,{children:[(0,s.jsx)(r.aR,{children:(0,s.jsxs)(r.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,s.jsx)(V,{className:"h-5 w-5"}),"Reset Database"]})}),(0,s.jsxs)(r.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(U.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,s.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,s.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,s.jsx)("li",{children:"Tutti i cavi installati"}),(0,s.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,s.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,s.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,s.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),u&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:u})}),x&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-green-600",children:x})}),(0,s.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,s.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(f.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,s.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,s.jsx)(j.p,{id:"confirm-text",value:e,onChange:e=>a(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:d,className:"RESET DATABASE"===e?"border-green-500":""})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(y.S,{id:"confirm-checkbox",checked:t,onCheckedChange:n,disabled:d}),(0,s.jsx)(f.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Stato Conferma:"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("RESET DATABASE"===e?"bg-green-500":"bg-red-500")}),(0,s.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===e?"✓ Corretto":"✗ Richiesto"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(t?"bg-green-500":"bg-red-500")}),(0,s.jsxs)("span",{children:["Checkbox confermata: ",t?"✓ S\xec":"✗ Richiesta"]})]})]})]}),(0,s.jsx)(l.Qi,{onClick:p,disabled:!g,className:"w-full",size:"lg",loading:d,icon:(0,s.jsx)(c.A,{className:"h-5 w-5"}),glow:!0,children:d?"Reset in corso...":"RESET DATABASE - ELIMINA TUTTI I DATI"}),!g&&(0,s.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per abilitare il reset"})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,s.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,s.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,s.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,s.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,s.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var M=t(3493);let L=(0,D.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),P=(0,D.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var Z=t(57434),B=t(84616);function F(){let[e,a]=(0,i.useState)("categorie");return(0,s.jsxs)(r.Zp,{children:[(0,s.jsx)(r.aR,{children:(0,s.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(M.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,s.jsxs)(r.Wu,{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(M.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,s.jsxs)(h.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,s.jsxs)(h.j7,{className:"grid w-full grid-cols-4",children:[(0,s.jsxs)(h.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,s.jsx)(L,{className:"h-4 w-4"}),"Categorie"]}),(0,s.jsxs)(h.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,s.jsx)(P,{className:"h-4 w-4"}),"Produttori"]}),(0,s.jsxs)(h.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,s.jsx)(Z.A,{className:"h-4 w-4"}),"Standard"]}),(0,s.jsxs)(h.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,s.jsx)(M.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,s.jsxs)(h.av,{value:"categorie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,s.jsxs)(b.$,{children:[(0,s.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(L,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,s.jsxs)(h.av,{value:"produttori",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,s.jsxs)(b.$,{children:[(0,s.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(P,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,s.jsxs)(h.av,{value:"standard",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,s.jsxs)(b.$,{children:[(0,s.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(Z.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,s.jsxs)(h.av,{value:"tipologie",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,s.jsxs)(b.$,{children:[(0,s.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,s.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,s.jsx)(M.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,s.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,s.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,s.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var G=t(17580);let X=(0,D.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),q=(0,D.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function H(){let e=(0,g.useRouter)(),[a,t]=(0,i.useState)("visualizza-utenti"),[d,o]=(0,i.useState)(""),[c,b]=(0,i.useState)([]),[j,f]=(0,i.useState)([]),[N,y]=(0,i.useState)(!0),[w,z]=(0,i.useState)(""),[A,k]=(0,i.useState)(null),[E,S]=(0,i.useState)({open:!1,message:"",severity:"success"}),{user:D,impersonateUser:U}=(0,p.A)();(0,i.useEffect)(()=>{L()},[a]);let L=async()=>{try{if(y(!0),z(""),console.log("Caricamento dati per tab:",a),console.log("Token presente:",localStorage.getItem("token")),console.log("Utente corrente:",D),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){console.log("Chiamata API per ottenere utenti...");let e=await v.dG.getUsers();console.log("Utenti ricevuti:",e),b(e)}else if("cantieri"===a){let e=await v._I.getCantieri();f(e)}}catch(a){var e,t;console.error("Errore caricamento dati:",a),console.error("Dettagli errore:",a.response),z((null==(t=a.response)||null==(e=t.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati")}finally{y(!1)}},P=e=>{console.log("Modifica utente:",e.username),k(e),t("modifica-utente")},Z=async e=>{console.log("Toggle status utente:",e);try{await v.dG.toggleUserStatus(e),L()}catch(e){var a,t;console.error("Errore toggle status:",e),z((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante la modifica dello stato utente")}},B=async e=>{if(console.log("Elimina utente:",e),confirm("Sei sicuro di voler eliminare questo utente?"))try{await v.dG.deleteUser(e),L()}catch(e){var a,t;console.error("Errore eliminazione utente:",e),z((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||"Errore durante l'eliminazione dell'utente")}},H=e=>{console.log("Utente salvato:",e),k(null),t("visualizza-utenti"),L()},J=()=>{k(null),t("visualizza-utenti")},O=async a=>{try{console.log("Impersonificazione rapida utente:",a.username,"Ruolo:",a.ruolo),await U(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){var t,s;console.error("Errore durante l'impersonificazione rapida:",e),z((null==(s=e.response)||null==(t=s.data)?void 0:t.detail)||e.message||"Errore durante l'impersonificazione")}},$=e=>{switch(e){case"owner":return(0,s.jsx)(m.E,{className:"bg-purple-100 text-purple-800",children:"Owner"});case"user":return(0,s.jsx)(m.E,{className:"bg-blue-100 text-blue-800",children:"User"});case"cantieri_user":return(0,s.jsx)(m.E,{className:"bg-green-100 text-green-800",children:"Cantieri User"});default:return(0,s.jsx)(m.E,{variant:"secondary",children:e})}},Q=(e,a)=>{if(!e)return(0,s.jsx)(m.E,{className:"bg-red-100 text-red-800",children:"Disabilitato"});if(a){let e=new Date(a),t=new Date;if(e<t)return(0,s.jsx)(m.E,{className:"bg-red-100 text-red-800",children:"Scaduto"});if(e.getTime()-t.getTime()<6048e5)return(0,s.jsx)(m.E,{className:"bg-yellow-100 text-yellow-800",children:"In Scadenza"})}return(0,s.jsx)(m.E,{className:"bg-green-100 text-green-800",children:"Attivo"})};return(c.filter(e=>{var a,t,s;return(null==(a=e.username)?void 0:a.toLowerCase().includes(d.toLowerCase()))||(null==(t=e.ragione_sociale)?void 0:t.toLowerCase().includes(d.toLowerCase()))||(null==(s=e.email)?void 0:s.toLowerCase().includes(d.toLowerCase()))}),D&&"owner"===D.ruolo)?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,s.jsxs)(h.tU,{value:a,onValueChange:t,className:"w-full",children:[(0,s.jsxs)(h.j7,{className:"grid w-full ".concat(A?"grid-cols-6":"grid-cols-5"),children:[(0,s.jsxs)(h.Xi,{value:"visualizza-utenti",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(G.A,{className:"h-4 w-4"}),"Visualizza Utenti"]}),(0,s.jsxs)(h.Xi,{value:"crea-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(X,{className:"h-4 w-4"}),"Crea Nuovo Utente"]}),A&&(0,s.jsxs)(h.Xi,{value:"modifica-utente",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),"Modifica Utente"]}),(0,s.jsxs)(h.Xi,{value:"database-tipologie-cavi",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(M.A,{className:"h-4 w-4"}),"Database Tipologie Cavi"]}),(0,s.jsxs)(h.Xi,{value:"visualizza-database-raw",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(_.A,{className:"h-4 w-4"}),"Visualizza Database Raw"]}),(0,s.jsxs)(h.Xi,{value:"reset-database",className:"tab-trigger flex items-center gap-2",children:[(0,s.jsx)(V,{className:"h-4 w-4"}),"Reset Database"]})]}),(0,s.jsxs)(h.av,{value:"visualizza-utenti",className:"space-y-4",children:[w&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600",children:w})}),(0,s.jsxs)(r.Zp,{children:[(0,s.jsx)(r.aR,{children:(0,s.jsx)(r.ZB,{children:"Lista Utenti"})}),(0,s.jsx)(r.Wu,{children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(x.XI,{className:"min-w-full",children:[(0,s.jsx)(x.A0,{children:(0,s.jsxs)(x.Hj,{children:[(0,s.jsx)(x.nd,{className:"w-[60px] text-center",children:"ID"}),(0,s.jsx)(x.nd,{className:"w-[120px]",children:"Username"}),(0,s.jsx)(x.nd,{className:"w-[100px] text-center",children:"Password"}),(0,s.jsx)(x.nd,{className:"w-[100px] text-center",children:"Ruolo"}),(0,s.jsx)(x.nd,{className:"w-[250px]",children:"Ragione Sociale"}),(0,s.jsx)(x.nd,{className:"w-[200px]",children:"Email"}),(0,s.jsx)(x.nd,{className:"w-[120px] text-center",children:"VAT"}),(0,s.jsx)(x.nd,{className:"w-[100px] text-center",children:"Nazione"}),(0,s.jsx)(x.nd,{className:"w-[150px]",children:"Referente"}),(0,s.jsx)(x.nd,{className:"w-[100px] text-center",children:"Scadenza"}),(0,s.jsx)(x.nd,{className:"w-[100px] text-center",children:"Stato"}),(0,s.jsx)(x.nd,{className:"w-[120px] text-center",children:"Azioni"})]})}),(0,s.jsx)(x.BF,{children:N?(0,s.jsx)(x.Hj,{children:(0,s.jsx)(x.nA,{colSpan:12,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(T.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===c.length?(0,s.jsx)(x.Hj,{children:(0,s.jsx)(x.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):c.map(e=>(0,s.jsxs)(x.Hj,{className:"hover:bg-slate-50",children:[(0,s.jsx)(x.nA,{className:"text-center text-slate-500 text-sm font-mono",children:e.id_utente}),(0,s.jsx)(x.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,s.jsx)(x.nA,{className:"text-center font-mono text-xs text-slate-500",children:e.password_plain||"***"}),(0,s.jsx)(x.nA,{className:"text-center",children:$(e.ruolo)}),(0,s.jsx)(x.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,s.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,s.jsx)(x.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,s.jsx)(x.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,s.jsx)(x.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,s.jsx)(x.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,s.jsx)(x.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,s.jsx)(x.nA,{className:"text-center",children:Q(e.abilitato,e.data_scadenza)}),(0,s.jsx)(x.nA,{className:"text-center",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(u,{user:e,onEdit:()=>P(e),onToggleStatus:()=>Z(e.id_utente),onDelete:()=>B(e.id_utente)}),(0,s.jsx)(l.jn,{size:"sm",onClick:()=>O(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,s.jsx)(q,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,s.jsx)(h.av,{value:"crea-utente",className:"space-y-4",children:(0,s.jsx)(C,{user:null,onSave:H,onCancel:J})}),A&&(0,s.jsx)(h.av,{value:"modifica-utente",className:"space-y-4",children:(0,s.jsx)(C,{user:A,onSave:H,onCancel:J})}),(0,s.jsx)(h.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,s.jsx)(F,{})}),(0,s.jsx)(h.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,s.jsx)(R,{})}),(0,s.jsx)(h.av,{value:"reset-database",className:"space-y-4",children:(0,s.jsx)(I,{})})]})})}):(window.location.replace("/login"),null)}},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>l});var s=t(95155);t(12115);var i=t(40968),r=t(59434);function l(e){let{className:a,...t}=e;return(0,s.jsx)(i.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},85127:(e,a,t)=>{"use strict";t.d(a,{A0:()=>l,BF:()=>n,Hj:()=>d,XI:()=>r,nA:()=>c,nd:()=>o});var s=t(95155);t(12115);var i=t(59434);function r(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,i.cn)("w-full caption-bottom text-sm border-collapse",a),...t})})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,i.cn)("[&_tr]:border-b",a),...t})}function n(e){let{className:a,...t}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,i.cn)("[&_tr:last-child]:border-0",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,i.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,i.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,i.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...t})}},92657:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[848,464,132,606,998,969,172,283,441,684,358],()=>a(42221)),_N_E=e.O()}]);