(()=>{var e={};e.id=313,e.ids=[313],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},60643:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{POST:()=>p});var o=t(96559),n=t(48088),a=t(37719),i=t(32190);async function p(e){try{let r=await e.json(),t=await fetch("http://localhost:8001/api/password/verify-reset-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),s=await t.json();return i.NextResponse.json(s,{status:t.status,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Error in token verification:",e),i.NextResponse.json({success:!1,detail:"Errore interno del server"},{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/password/verify-reset-token/route",pathname:"/api/password/verify-reset-token",filename:"route",bundlePath:"app/api/password/verify-reset-token/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\verify-reset-token\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=u;function x(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(60643));module.exports=s})();