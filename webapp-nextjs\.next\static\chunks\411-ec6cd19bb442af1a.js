"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[411],{381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>ea,ZL:()=>ee,bL:()=>X,bm:()=>el,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(12115),a=r(85185),l=r(6101),o=r(46081),i=r(61285),s=r(5845),d=r(19178),c=r(25519),u=r(34378),p=r(28905),f=r(63655),h=r(92293),y=r(93795),g=r(38168),m=r(99708),v=r(95155),x="Dialog",[k,D]=(0,o.A)(x),[j,A]=k(x),b=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:l,onOpenChange:o,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:a,defaultProp:null!=l&&l,onChange:o,caller:x});return(0,v.jsx)(j,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};b.displayName=x;var C="DialogTrigger",w=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=A(C,r),i=(0,l.s)(t,o.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":H(o.open),...n,ref:i,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})});w.displayName=C;var R="DialogPortal",[I,M]=k(R,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:l}=e,o=A(R,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(a,e=>(0,v.jsx)(p.C,{present:r||o.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:l,children:e})}))})};N.displayName=R;var O="DialogOverlay",_=n.forwardRef((e,t)=>{let r=M(O,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,l=A(O,e.__scopeDialog);return l.modal?(0,v.jsx)(p.C,{present:n||l.open,children:(0,v.jsx)(F,{...a,ref:t})}):null});_.displayName=O;var E=(0,m.TL)("DialogOverlay.RemoveScroll"),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=A(O,r);return(0,v.jsx)(y.A,{as:E,allowPinchZoom:!0,shards:[a.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":H(a.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),q="DialogContent",P=n.forwardRef((e,t)=>{let r=M(q,e.__scopeDialog),{forceMount:n=r.forceMount,...a}=e,l=A(q,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||l.open,children:l.modal?(0,v.jsx)(z,{...a,ref:t}):(0,v.jsx)(V,{...a,ref:t})})});P.displayName=q;var z=n.forwardRef((e,t)=>{let r=A(q,e.__scopeDialog),o=n.useRef(null),i=(0,l.s)(t,r.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,g.Eq)(e)},[]),(0,v.jsx)(T,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),V=n.forwardRef((e,t)=>{let r=A(q,e.__scopeDialog),a=n.useRef(!1),l=n.useRef(!1);return(0,v.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,o;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(a.current||null==(o=r.triggerRef.current)||o.focus(),t.preventDefault()),a.current=!1,l.current=!1},onInteractOutside:t=>{var n,o;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let i=t.target;(null==(o=r.triggerRef.current)?void 0:o.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),T=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,u=A(q,r),p=n.useRef(null),f=(0,l.s)(t,p);return(0,h.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":H(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Y,{titleId:u.titleId}),(0,v.jsx)(Q,{contentRef:p,descriptionId:u.descriptionId})]})]})}),B="DialogTitle",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=A(B,r);return(0,v.jsx)(f.sG.h2,{id:a.titleId,...n,ref:t})});G.displayName=B;var L="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=A(L,r);return(0,v.jsx)(f.sG.p,{id:a.descriptionId,...n,ref:t})});W.displayName=L;var Z="DialogClose",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=A(Z,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,a.m)(e.onClick,()=>l.onOpenChange(!1))})});function H(e){return e?"open":"closed"}S.displayName=Z;var U="DialogTitleWarning",[J,K]=(0,o.q)(U,{contentName:q,titleName:B,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,r=K(U),a="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(a))},[a,t]),null},Q=e=>{let{contentRef:t,descriptionId:r}=e,a=K("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(l))},[l,t,r]),null},X=b,$=w,ee=N,et=_,er=P,en=G,ea=W,el=S},23227:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},28883:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},32919:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},75525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);