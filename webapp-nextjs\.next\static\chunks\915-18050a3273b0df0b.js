"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[915],{14186:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},16785:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},19946:(e,t,r)=>{r.d(t,{A:()=>d});var a=r(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:p,...v}=e;return(0,a.createElement)("svg",{ref:t,...s,width:n,height:n,stroke:r,strokeWidth:o?24*Number(l)/Number(n):l,className:i("lucide",c),...!d&&!u(v)&&{"aria-hidden":"true"},...v},[...p.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:u,...s}=r;return(0,a.createElement)(c,{ref:l,iconNode:t,className:i("lucide-".concat(n(o(e))),"lucide-".concat(e),u),...s})});return r.displayName=o(e),r}},33109:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},46081:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>l});var a=r(12115),n=r(95155);function l(e,t){let r=a.createContext(t),l=e=>{let{children:t,...l}=e,o=a.useMemo(()=>l,Object.values(l));return(0,n.jsx)(r.Provider,{value:o,children:t})};return l.displayName=e+"Provider",[l,function(n){let l=a.useContext(r);if(l)return l;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],l=()=>{let t=r.map(e=>a.createContext(e));return function(r){let n=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return l.scopeName=e,[function(t,l){let o=a.createContext(l),i=r.length;r=[...r,l];let u=t=>{let{scope:r,children:l,...u}=t,s=r?.[e]?.[i]||o,c=a.useMemo(()=>u,Object.values(u));return(0,n.jsx)(s.Provider,{value:c,children:l})};return u.displayName=t+"Provider",[u,function(r,n){let u=n?.[e]?.[i]||o,s=a.useContext(u);if(s)return s;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:a})=>{let n=r(e)[`__scope${a}`];return{...t,...n}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(l,...t)]}},55863:(e,t,r)=>{r.d(t,{C1:()=>A,bL:()=>g});var a=r(12115),n=r(46081),l=r(63655),o=r(95155),i="Progress",[u,s]=(0,n.A)(i),[c,d]=u(i),p=a.forwardRef((e,t)=>{var r,a,n,i;let{__scopeProgress:u,value:s=null,max:d,getValueLabel:p=f,...v}=e;(d||0===d)&&!k(d)&&console.error((r="".concat(d),a="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let m=k(d)?d:100;null===s||x(s,m)||console.error((n="".concat(s),i="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let g=x(s,m)?s:null,A=y(g)?p(g,m):void 0;return(0,o.jsx)(c,{scope:u,value:g,max:m,children:(0,o.jsx)(l.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":y(g)?g:void 0,"aria-valuetext":A,role:"progressbar","data-state":h(g,m),"data-value":null!=g?g:void 0,"data-max":m,...v,ref:t})})});p.displayName=i;var v="ProgressIndicator",m=a.forwardRef((e,t)=>{var r;let{__scopeProgress:a,...n}=e,i=d(v,a);return(0,o.jsx)(l.sG.div,{"data-state":h(i.value,i.max),"data-value":null!=(r=i.value)?r:void 0,"data-max":i.max,...n,ref:t})});function f(e,t){return"".concat(Math.round(e/t*100),"%")}function h(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function k(e){return y(e)&&!isNaN(e)&&e>0}function x(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=v;var g=p,A=m},63655:(e,t,r)=>{r.d(t,{hO:()=>u,sG:()=>i});var a=r(12115),n=r(47650),l=r(99708),o=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?r:t,{...l,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function u(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},69074:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71539:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},72713:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74466:(e,t,r)=>{r.d(t,{F:()=>o});var a=r(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=a.$,o=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:i}=t,u=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],a=null==i?void 0:i[e];if(null===t)return null;let l=n(t)||n(a);return o[e][l]}),s=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return l(e,u,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...s}[t]):({...i,...s})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},79397:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}}]);